package com.example.gxzhaiwu.ui.usermanagement;

/**
 * 用户管理ViewModel
 * 管理用户管理页面的状态和业务逻辑
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0017\u001a\u00020\u0018J\u0010\u0010\u0019\u001a\u00020\u00182\b\u0010\u001a\u001a\u0004\u0018\u00010\u001bJ\u0014\u0010\u001c\u001a\u00020\u001d2\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001b0\u001fJ\u0006\u0010 \u001a\u00020\u0018J\u0006\u0010!\u001a\u00020\u0018J\u0006\u0010\"\u001a\u00020\u0018J\b\u0010#\u001a\u00020\u0018H\u0002J\u0006\u0010$\u001a\u00020\u0018J\u0006\u0010%\u001a\u00020\u0018J\b\u0010&\u001a\u00020\u0018H\u0002J\u0010\u0010\'\u001a\u00020\u00182\b\b\u0002\u0010(\u001a\u00020\u001dJ\u0006\u0010(\u001a\u00020\u0018J\u000e\u0010)\u001a\u00020\u00182\u0006\u0010*\u001a\u00020\u001bJ\u000e\u0010+\u001a\u00020\u00182\u0006\u0010,\u001a\u00020-J\u000e\u0010.\u001a\u00020\u00182\u0006\u0010,\u001a\u00020-J\u000e\u0010/\u001a\u00020\u00182\u0006\u0010,\u001a\u00020-J\u0010\u00100\u001a\u00020\u00182\u0006\u00101\u001a\u00020-H\u0002J\u001c\u00102\u001a\u00020\u00182\u0006\u00103\u001a\u0002042\f\u00105\u001a\b\u0012\u0004\u0012\u0002040\u001fJ\u001c\u00106\u001a\u00020\u00182\u0006\u00103\u001a\u0002042\f\u00107\u001a\b\u0012\u0004\u0012\u0002040\u001fR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00070\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\n0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\f0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0010\u00a8\u00068"}, d2 = {"Lcom/example/gxzhaiwu/ui/usermanagement/UserManagementViewModel;", "Landroidx/lifecycle/ViewModel;", "repository", "Lcom/example/gxzhaiwu/data/repository/UserManagementRepository;", "(Lcom/example/gxzhaiwu/data/repository/UserManagementRepository;)V", "_filterOptions", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/gxzhaiwu/data/model/UserFilterOptions;", "_operationResult", "Lkotlinx/coroutines/flow/MutableSharedFlow;", "Lcom/example/gxzhaiwu/data/model/UserOperationResult;", "_uiState", "Lcom/example/gxzhaiwu/data/model/UserManagementUiState;", "filterOptions", "Lkotlinx/coroutines/flow/StateFlow;", "getFilterOptions", "()Lkotlinx/coroutines/flow/StateFlow;", "operationResult", "Lkotlinx/coroutines/flow/SharedFlow;", "getOperationResult", "()Lkotlinx/coroutines/flow/SharedFlow;", "uiState", "getUiState", "clearError", "", "filterByRole", "role", "", "hasManagementPermission", "", "currentUserRoles", "", "hideRoleDialog", "hideStoreDialog", "hideUserDetail", "loadInitialData", "loadNextPage", "loadPreviousPage", "loadRoles", "loadUsers", "refresh", "searchUsers", "query", "showRoleDialog", "user", "Lcom/example/gxzhaiwu/data/model/UserDetail;", "showStoreDialog", "showUserDetail", "updateUserInList", "updatedUser", "updateUserRoles", "userId", "", "roleIds", "updateUserStores", "storeIds", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class UserManagementViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.repository.UserManagementRepository repository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.gxzhaiwu.data.model.UserManagementUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.gxzhaiwu.data.model.UserManagementUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableSharedFlow<com.example.gxzhaiwu.data.model.UserOperationResult> _operationResult = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.SharedFlow<com.example.gxzhaiwu.data.model.UserOperationResult> operationResult = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.gxzhaiwu.data.model.UserFilterOptions> _filterOptions = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.gxzhaiwu.data.model.UserFilterOptions> filterOptions = null;
    
    @javax.inject.Inject()
    public UserManagementViewModel(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.repository.UserManagementRepository repository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.gxzhaiwu.data.model.UserManagementUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.SharedFlow<com.example.gxzhaiwu.data.model.UserOperationResult> getOperationResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.gxzhaiwu.data.model.UserFilterOptions> getFilterOptions() {
        return null;
    }
    
    /**
     * 加载初始数据
     */
    private final void loadInitialData() {
    }
    
    /**
     * 加载用户列表
     */
    public final void loadUsers(boolean refresh) {
    }
    
    /**
     * 加载角色列表
     */
    private final void loadRoles() {
    }
    
    /**
     * 搜索用户
     */
    public final void searchUsers(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
    }
    
    /**
     * 筛选角色
     */
    public final void filterByRole(@org.jetbrains.annotations.Nullable()
    java.lang.String role) {
    }
    
    /**
     * 加载下一页
     */
    public final void loadNextPage() {
    }
    
    /**
     * 加载上一页
     */
    public final void loadPreviousPage() {
    }
    
    /**
     * 显示用户详情
     */
    public final void showUserDetail(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail user) {
    }
    
    /**
     * 隐藏用户详情
     */
    public final void hideUserDetail() {
    }
    
    /**
     * 显示角色编辑对话框
     */
    public final void showRoleDialog(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail user) {
    }
    
    /**
     * 隐藏角色编辑对话框
     */
    public final void hideRoleDialog() {
    }
    
    /**
     * 显示门店编辑对话框
     */
    public final void showStoreDialog(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail user) {
    }
    
    /**
     * 隐藏门店编辑对话框
     */
    public final void hideStoreDialog() {
    }
    
    /**
     * 更新用户角色
     */
    public final void updateUserRoles(int userId, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> roleIds) {
    }
    
    /**
     * 更新用户门店权限
     */
    public final void updateUserStores(int userId, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> storeIds) {
    }
    
    /**
     * 清除错误信息
     */
    public final void clearError() {
    }
    
    /**
     * 刷新数据
     */
    public final void refresh() {
    }
    
    /**
     * 更新列表中的用户信息
     */
    private final void updateUserInList(com.example.gxzhaiwu.data.model.UserDetail updatedUser) {
    }
    
    /**
     * 检查当前用户是否有管理权限
     */
    public final boolean hasManagementPermission(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> currentUserRoles) {
        return false;
    }
}