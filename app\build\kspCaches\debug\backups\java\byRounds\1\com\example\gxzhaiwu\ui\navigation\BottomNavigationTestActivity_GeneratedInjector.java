package com.example.gxzhaiwu.ui.navigation;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;
import javax.annotation.processing.Generated;

@OriginatingElement(
    topLevelClass = BottomNavigationTestActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
@Generated("dagger.hilt.android.processor.internal.androidentrypoint.InjectorEntryPointGenerator")
public interface BottomNavigationTestActivity_GeneratedInjector {
  void injectBottomNavigationTestActivity(
      BottomNavigationTestActivity bottomNavigationTestActivity);
}
