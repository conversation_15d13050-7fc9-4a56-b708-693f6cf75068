package com.example.gxzhaiwu.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\u0007\u001a\u00020\bH\u0096@\u00a2\u0006\u0002\u0010\tJ\u0010\u0010\n\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\u000bH\u0016J\u001c\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0010\u0010\tJ\u0010\u0010\u0011\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0\u000bH\u0016J\u000e\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\f0\u0013H\u0016J\u000e\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u000bH\u0016J0\u0010\u0016\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u000f0\u00170\u000e2\u0006\u0010\u0018\u001a\u00020\u0019H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001a\u0010\u001bJ\u001c\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\b0\u000eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001d\u0010\tJ0\u0010\u001e\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u000f0\u00170\u000e2\u0006\u0010\u001f\u001a\u00020 H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b!\u0010\"R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006#"}, d2 = {"Lcom/example/gxzhaiwu/data/repository/AuthRepositoryImpl;", "Lcom/example/gxzhaiwu/data/repository/AuthRepository;", "authApi", "Lcom/example/gxzhaiwu/data/api/AuthApi;", "userPreferences", "Lcom/example/gxzhaiwu/data/local/UserPreferences;", "(Lcom/example/gxzhaiwu/data/api/AuthApi;Lcom/example/gxzhaiwu/data/local/UserPreferences;)V", "clearUserData", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAuthToken", "Lkotlinx/coroutines/flow/Flow;", "", "getCurrentUser", "Lkotlin/Result;", "Lcom/example/gxzhaiwu/data/model/User;", "getCurrentUser-IoAF18A", "getCurrentUserFlow", "getCurrentUserRoles", "", "isLoggedIn", "", "login", "Lkotlin/Pair;", "loginRequest", "Lcom/example/gxzhaiwu/data/model/LoginRequest;", "login-gIAlu-s", "(Lcom/example/gxzhaiwu/data/model/LoginRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "logout", "logout-IoAF18A", "register", "registerRequest", "Lcom/example/gxzhaiwu/data/model/RegisterRequest;", "register-gIAlu-s", "(Lcom/example/gxzhaiwu/data/model/RegisterRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class AuthRepositoryImpl implements com.example.gxzhaiwu.data.repository.AuthRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.api.AuthApi authApi = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.local.UserPreferences userPreferences = null;
    
    @javax.inject.Inject()
    public AuthRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.api.AuthApi authApi, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.local.UserPreferences userPreferences) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.String> getAuthToken() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.Boolean> isLoggedIn() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<com.example.gxzhaiwu.data.model.User> getCurrentUserFlow() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.util.List<java.lang.String> getCurrentUserRoles() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object clearUserData(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}