package com.example.gxzhaiwu.ui.usermanagement;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\u001a \u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0003\u001a:\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0003\u001aj\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\b2\b\u0010\r\u001a\u0004\u0018\u00010\b2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u00122\u0014\u0010\u0013\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\b\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0003\u001a8\u0010\u0015\u001a\u00020\u00012\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\b0\u000f2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0017\u001a\u00020\u0018H\u0007\u00a8\u0006\u0019"}, d2 = {"AccessDeniedContent", "", "onNavigateBack", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "ErrorCard", "error", "", "onDismiss", "onRetry", "SearchAndFilterBar", "searchQuery", "selectedRole", "availableRoles", "", "Lcom/example/gxzhaiwu/data/model/Role;", "onSearchQueryChange", "Lkotlin/Function1;", "onRoleFilterChange", "onKeyboardDone", "UserManagementScreen", "currentUserRoles", "viewModel", "Lcom/example/gxzhaiwu/ui/usermanagement/UserManagementViewModel;", "app_debug"})
public final class UserManagementScreenKt {
    
    /**
     * 用户管理主页面
     * 仅限系统管理员访问
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void UserManagementScreen(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> currentUserRoles, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.ui.usermanagement.UserManagementViewModel viewModel) {
    }
    
    /**
     * 搜索和筛选栏组件
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void SearchAndFilterBar(java.lang.String searchQuery, java.lang.String selectedRole, java.util.List<com.example.gxzhaiwu.data.model.Role> availableRoles, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSearchQueryChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onRoleFilterChange, kotlin.jvm.functions.Function0<kotlin.Unit> onKeyboardDone, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 错误卡片组件
     */
    @androidx.compose.runtime.Composable()
    private static final void ErrorCard(java.lang.String error, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, kotlin.jvm.functions.Function0<kotlin.Unit> onRetry, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 访问拒绝内容组件
     */
    @androidx.compose.runtime.Composable()
    private static final void AccessDeniedContent(kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, androidx.compose.ui.Modifier modifier) {
    }
}