package com.example.gxzhaiwu.utils

import android.content.Context
import android.view.inputmethod.InputMethodManager
import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.dp

object UiUtils {
    
    /**
     * 隐藏软键盘
     */
    fun hideKeyboard(context: Context) {
        val inputMethodManager = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        if (context is ComponentActivity) {
            val currentFocus = context.currentFocus
            currentFocus?.let {
                inputMethodManager.hideSoftInputFromWindow(it.windowToken, 0)
            }
        }
    }
    
    /**
     * 显示软键盘
     * 注意：现代 Android 开发中，通常不需要手动显示键盘，
     * 系统会在用户点击输入框时自动显示。
     * 此方法保留用于特殊情况，但建议依赖系统自动管理。
     */
    fun showKeyboard(context: Context) {
        // 在现代 Android 开发中，推荐让系统自动管理键盘显示
        // 如果确实需要手动控制，可以通过 View.requestFocus() 来触发
        // 这里提供一个空实现，因为强制显示键盘通常不是好的用户体验
    }
}

/**
 * 检测键盘是否显示的Composable函数
 */
@Composable
fun rememberIsKeyboardOpen(): State<Boolean> {
    val view = LocalView.current
    val density = LocalDensity.current
    val windowInsets = WindowInsets.ime
    
    return rememberUpdatedState(
        windowInsets.getBottom(density) > 0
    )
}

/**
 * 获取键盘高度的Composable函数
 */
@Composable
fun rememberKeyboardHeight(): State<Int> {
    val density = LocalDensity.current
    val windowInsets = WindowInsets.ime
    
    return rememberUpdatedState(
        with(density) { windowInsets.getBottom(density).toDp().value.toInt() }
    )
}
