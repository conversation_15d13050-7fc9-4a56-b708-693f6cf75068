package com.example.gxzhaiwu.data.model

import kotlinx.serialization.Serializable

/**
 * 用户管理相关数据模型
 */

/**
 * 用户详细信息数据模型（用于用户管理）
 */
@Serializable
data class UserDetail(
    val id: Int,
    val name: String,
    val username: String,
    val email: String,
    val email_verified_at: String? = null,
    val created_at: String,
    val updated_at: String,
    val roles: List<Role> = emptyList(),
    val permissions: UserPermissions? = null,
    val stores: List<UserStore> = emptyList()
)

/**
 * 角色信息数据模型
 */
@Serializable
data class Role(
    val id: Int,
    val name: String,
    val slug: String,
    val description: String? = null,
    val is_system: Boolean = false
)

/**
 * 用户权限数据模型
 */
@Serializable
data class UserPermissions(
    val is_admin: Boolean = false,
    val is_store_owner: Boolean = false,
    val is_store_staff: Boolean = false
)

/**
 * 用户关联门店数据模型
 */
@Serializable
data class UserStore(
    val id: Int,
    val name: String,
    val code: String
)

/**
 * 用户列表响应数据模型
 */
@Serializable
data class UserListResponse(
    val success: Boolean,
    val data: PaginatedUserData,
    val message: String? = null
)

/**
 * 分页用户数据模型
 */
@Serializable
data class PaginatedUserData(
    val current_page: Int,
    val data: List<UserDetail>,
    val first_page_url: String,
    val from: Int,
    val last_page: Int,
    val last_page_url: String,
    val next_page_url: String? = null,
    val path: String,
    val per_page: Int,
    val prev_page_url: String? = null,
    val to: Int,
    val total: Int
)

/**
 * 用户详情响应数据模型
 */
@Serializable
data class UserDetailResponse(
    val success: Boolean,
    val data: UserDetail,
    val message: String? = null
)

/**
 * 角色列表响应数据模型
 */
@Serializable
data class RoleListResponse(
    val success: Boolean,
    val data: List<Role>,
    val message: String? = null
)

/**
 * 更新用户角色请求数据模型
 */
@Serializable
data class UpdateUserRolesRequest(
    val role_ids: List<Int>
)

/**
 * 更新用户门店请求数据模型
 */
@Serializable
data class UpdateUserStoresRequest(
    val stores: List<Int>
)

/**
 * 用户管理UI状态数据模型
 */
data class UserManagementUiState(
    val isLoading: Boolean = false,
    val users: List<UserDetail> = emptyList(),
    val roles: List<Role> = emptyList(),
    val currentPage: Int = 1,
    val totalPages: Int = 1,
    val totalUsers: Int = 0,
    val searchQuery: String = "",
    val selectedRole: String? = null,
    val error: String? = null,
    val isRefreshing: Boolean = false,
    val selectedUser: UserDetail? = null,
    val showUserDetail: Boolean = false,
    val showRoleDialog: Boolean = false,
    val showStoreDialog: Boolean = false
)

/**
 * 用户管理操作类型
 */
enum class UserManagementAction {
    VIEW_DETAIL,        // 查看详情
    EDIT_ROLES,         // 编辑角色
    EDIT_STORES,        // 编辑门店
    TOGGLE_STATUS       // 切换状态（如果API支持）
}

/**
 * 用户状态枚举（用于UI显示）
 */
enum class UserStatus {
    ACTIVE,             // 活跃
    INACTIVE,           // 非活跃
    PENDING             // 待验证
}

/**
 * 用户筛选选项
 */
data class UserFilterOptions(
    val searchQuery: String = "",
    val selectedRole: String? = null,
    val selectedStatus: UserStatus? = null,
    val page: Int = 1,
    val perPage: Int = 15
)

/**
 * 用户操作结果
 */
sealed class UserOperationResult {
    object Success : UserOperationResult()
    data class Error(val message: String) : UserOperationResult()
    object Loading : UserOperationResult()
}

/**
 * 扩展函数：获取用户的主要角色
 */
fun UserDetail.getPrimaryRole(): Role? {
    return roles.firstOrNull { role ->
        when (role.slug) {
            "admin" -> true
            "store_owner" -> roles.none { it.slug == "admin" }
            "store_staff" -> roles.none { it.slug == "admin" || it.slug == "store_owner" }
            else -> false
        }
    }
}

/**
 * 扩展函数：获取用户角色显示名称
 */
fun UserDetail.getRoleDisplayName(): String {
    return getPrimaryRole()?.name ?: "未知角色"
}

/**
 * 扩展函数：获取用户角色颜色（用于UI）
 */
fun UserDetail.getRoleColor(): String {
    return when (getPrimaryRole()?.slug) {
        "admin" -> "primary"
        "store_owner" -> "secondary"
        "store_staff" -> "tertiary"
        else -> "surface"
    }
}

/**
 * 扩展函数：检查用户是否为管理员
 */
fun UserDetail.isAdmin(): Boolean {
    return roles.any { it.slug == "admin" }
}

/**
 * 扩展函数：检查用户是否为店长
 */
fun UserDetail.isStoreOwner(): Boolean {
    return roles.any { it.slug == "store_owner" }
}

/**
 * 扩展函数：检查用户是否为店员
 */
fun UserDetail.isStoreStaff(): Boolean {
    return roles.any { it.slug == "store_staff" }
}

/**
 * 扩展函数：获取用户门店名称列表
 */
fun UserDetail.getStoreNames(): String {
    return if (stores.isEmpty()) {
        "未分配门店"
    } else {
        stores.joinToString(", ") { it.name }
    }
}
