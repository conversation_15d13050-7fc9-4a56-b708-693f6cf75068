package com.example.gxzhaiwu.ui.dashboard.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.DashboardPermissions
import com.example.gxzhaiwu.data.model.UserRole
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import com.example.gxzhaiwu.ui.theme.cardElevationColor
import com.example.gxzhaiwu.utils.Permission
import com.example.gxzhaiwu.utils.RoleUtils

/**
 * 基于角色的内容显示组件
 * 根据用户权限动态显示或隐藏内容
 */
@Composable
fun RoleBasedContent(
    userRoles: List<String>,
    requiredPermission: Permission,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    if (RoleUtils.hasPermission(userRoles, requiredPermission)) {
        Box(modifier = modifier) {
            content()
        }
    }
}

/**
 * 角色权限不足提示组件
 */
@Composable
fun PermissionDeniedCard(
    message: String = "您没有权限查看此内容",
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.cardElevationColor(1)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 1.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Lock,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(48.dp)
            )
            
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 用户角色标识组件
 */
@Composable
fun UserRoleBadge(
    userRoles: List<String>,
    modifier: Modifier = Modifier
) {
    val primaryRole = RoleUtils.getPrimaryRole(userRoles)
    val roleColor = when (primaryRole) {
        UserRole.ADMIN -> MaterialTheme.colorScheme.primary
        UserRole.STORE_OWNER -> MaterialTheme.colorScheme.secondary
        UserRole.STORE_STAFF -> MaterialTheme.colorScheme.tertiary
    }
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = roleColor.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        )
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            val roleIcon = when (primaryRole) {
                UserRole.ADMIN -> Icons.Default.AdminPanelSettings
                UserRole.STORE_OWNER -> Icons.Default.Store
                UserRole.STORE_STAFF -> Icons.Default.Person
            }
            
            Icon(
                imageVector = roleIcon,
                contentDescription = null,
                tint = roleColor,
                modifier = Modifier.size(16.dp)
            )
            
            Text(
                text = primaryRole.displayName,
                style = MaterialTheme.typography.labelMedium,
                color = roleColor,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * 权限功能卡片组件
 * 显示用户当前拥有的权限功能
 */
@Composable
fun PermissionSummaryCard(
    userRoles: List<String>,
    modifier: Modifier = Modifier
) {
    val permissions = RoleUtils.getDashboardPermissions(userRoles)
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.cardElevationColor(2)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "权限概览",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                UserRoleBadge(userRoles = userRoles)
            }
            
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                PermissionItem(
                    title = "查看所有门店",
                    enabled = permissions.canViewAllStores
                )
                
                PermissionItem(
                    title = "查看财务详情",
                    enabled = permissions.canViewFinancialDetails
                )
                
                PermissionItem(
                    title = "门店数据对比",
                    enabled = permissions.canViewStoreComparison
                )
                
                PermissionItem(
                    title = "高级统计功能",
                    enabled = permissions.canAccessAdvancedStatistics
                )
            }
        }
    }
}

/**
 * 权限项组件
 */
@Composable
private fun PermissionItem(
    title: String,
    enabled: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = if (enabled) Icons.Default.CheckCircle else Icons.Default.Cancel,
            contentDescription = null,
            tint = if (enabled) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(16.dp)
        )
        
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium,
            color = if (enabled) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Preview(name = "管理员角色标识")
@Composable
private fun UserRoleBadgeAdminPreview() {
    GxZhaiWuTheme {
        Surface {
            UserRoleBadge(
                userRoles = listOf("admin"),
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Preview(name = "权限不足提示")
@Composable
private fun PermissionDeniedCardPreview() {
    GxZhaiWuTheme {
        Surface {
            PermissionDeniedCard(
                message = "您需要店长权限才能查看此内容",
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Preview(name = "店员权限概览")
@Composable
private fun PermissionSummaryCardStaffPreview() {
    GxZhaiWuTheme {
        Surface {
            PermissionSummaryCard(
                userRoles = listOf("store_staff"),
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Preview(name = "管理员权限概览")
@Composable
private fun PermissionSummaryCardAdminPreview() {
    GxZhaiWuTheme {
        Surface {
            PermissionSummaryCard(
                userRoles = listOf("admin"),
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}
