package com.example.gxzhaiwu.ui.bills;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class BillManagementViewModel_Factory implements Factory<BillManagementViewModel> {
  @Override
  public BillManagementViewModel get() {
    return newInstance();
  }

  public static BillManagementViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static BillManagementViewModel newInstance() {
    return new BillManagementViewModel();
  }

  private static final class InstanceHolder {
    private static final BillManagementViewModel_Factory INSTANCE = new BillManagementViewModel_Factory();
  }
}
