package com.example.gxzhaiwu.utils;

/**
 * 权限枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0010\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010\u00a8\u0006\u0011"}, d2 = {"Lcom/example/gxzhaiwu/utils/Permission;", "", "(Ljava/lang/String;I)V", "VIEW_ALL_STORES", "VIEW_FINANCIAL_DETAILS", "VIEW_STORE_COMPARISON", "ACCESS_ADVANCED_STATISTICS", "MANAGE_USERS", "MANAGE_STORES", "CREATE_INVOICE", "RECORD_PAYMENT", "ADD_CUSTOMER", "VIEW_REPORTS", "ACCESS_MANAGEMENT_CENTER", "MANAGE_BILLS", "MANAGE_PAYMENTS", "VIEW_ADVANCED_REPORTS", "app_debug"})
public enum Permission {
    /*public static final*/ VIEW_ALL_STORES /* = new VIEW_ALL_STORES() */,
    /*public static final*/ VIEW_FINANCIAL_DETAILS /* = new VIEW_FINANCIAL_DETAILS() */,
    /*public static final*/ VIEW_STORE_COMPARISON /* = new VIEW_STORE_COMPARISON() */,
    /*public static final*/ ACCESS_ADVANCED_STATISTICS /* = new ACCESS_ADVANCED_STATISTICS() */,
    /*public static final*/ MANAGE_USERS /* = new MANAGE_USERS() */,
    /*public static final*/ MANAGE_STORES /* = new MANAGE_STORES() */,
    /*public static final*/ CREATE_INVOICE /* = new CREATE_INVOICE() */,
    /*public static final*/ RECORD_PAYMENT /* = new RECORD_PAYMENT() */,
    /*public static final*/ ADD_CUSTOMER /* = new ADD_CUSTOMER() */,
    /*public static final*/ VIEW_REPORTS /* = new VIEW_REPORTS() */,
    /*public static final*/ ACCESS_MANAGEMENT_CENTER /* = new ACCESS_MANAGEMENT_CENTER() */,
    /*public static final*/ MANAGE_BILLS /* = new MANAGE_BILLS() */,
    /*public static final*/ MANAGE_PAYMENTS /* = new MANAGE_PAYMENTS() */,
    /*public static final*/ VIEW_ADVANCED_REPORTS /* = new VIEW_ADVANCED_REPORTS() */;
    
    Permission() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.gxzhaiwu.utils.Permission> getEntries() {
        return null;
    }
}