package com.example.gxzhaiwu.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import retrofit2.HttpException
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

object NetworkUtils {
    
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        
        return when {
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> true
            else -> false
        }
    }
    
    fun getErrorMessage(throwable: Throwable): String {
        return when (throwable) {
            is UnknownHostException -> "网络连接失败，请检查网络设置"
            is SocketTimeoutException -> "网络请求超时，请重试"
            is IOException -> "网络连接异常，请检查网络"
            is HttpException -> {
                when (throwable.code()) {
                    400 -> "请求参数错误"
                    401 -> "认证失败，请重新登录"
                    403 -> "权限不足"
                    404 -> "请求的资源不存在"
                    422 -> "数据验证失败"
                    500 -> "服务器内部错误"
                    502 -> "网关错误"
                    503 -> "服务暂时不可用"
                    else -> "网络请求失败 (${throwable.code()})"
                }
            }
            else -> throwable.message ?: "未知错误"
        }
    }
}
