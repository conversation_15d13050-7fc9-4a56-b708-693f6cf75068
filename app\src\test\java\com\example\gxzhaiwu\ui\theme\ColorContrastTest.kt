package com.example.gxzhaiwu.ui.theme

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.luminance
import org.junit.Test
import org.junit.Assert.*
import kotlin.math.max
import kotlin.math.min

/**
 * 颜色对比度测试
 * 验证所有颜色组合是否满足WCAG 2.1 AA标准（对比度 >= 4.5:1）
 */
class ColorContrastTest {

    /**
     * 计算两个颜色之间的对比度
     * 公式：(L1 + 0.05) / (L2 + 0.05)，其中L1是较亮颜色的亮度，L2是较暗颜色的亮度
     */
    private fun calculateContrastRatio(color1: Color, color2: Color): Double {
        val luminance1 = color1.luminance()
        val luminance2 = color2.luminance()
        val lighter = max(luminance1, luminance2)
        val darker = min(luminance1, luminance2)
        return (lighter + 0.05) / (darker + 0.05)
    }

    /**
     * 验证对比度是否满足WCAG AA标准
     */
    private fun assertContrastRatio(
        foreground: Color,
        background: Color,
        minRatio: Double = 4.5,
        description: String
    ) {
        val ratio = calculateContrastRatio(foreground, background)
        assertTrue(
            "$description - 对比度 ${"%.2f".format(ratio)}:1 不满足最低要求 ${"%.1f".format(minRatio)}:1",
            ratio >= minRatio
        )
        println("✓ $description - 对比度: ${"%.2f".format(ratio)}:1")
    }

    @Test
    fun `测试浅色主题对比度`() {
        println("=== 浅色主题对比度测试 ===")
        
        // 主色调对比度
        assertContrastRatio(OnPrimary, Primary, description = "主色调文字/背景")
        assertContrastRatio(OnSecondary, Secondary, description = "次要色调文字/背景")
        
        // 背景和表面对比度
        assertContrastRatio(OnBackground, Background, description = "背景文字/背景")
        assertContrastRatio(OnSurface, Surface, description = "表面文字/表面")
        assertContrastRatio(OnSurfaceVariant, SurfaceVariant, description = "表面变体文字/表面变体")
        
        // 语义化颜色对比度
        assertContrastRatio(OnError, Error, description = "错误文字/错误背景")
        assertContrastRatio(OnSuccess, Success, description = "成功文字/成功背景")
        assertContrastRatio(OnWarning, Warning, description = "警告文字/警告背景")
        assertContrastRatio(OnInfo, Info, description = "信息文字/信息背景")
        
        // 轮廓颜色对比度（较低要求，3:1即可）
        assertContrastRatio(Outline, Background, minRatio = 3.0, description = "轮廓/背景")
    }

    @Test
    fun `测试深色主题对比度`() {
        println("=== 深色主题对比度测试 ===")
        
        // 主色调对比度
        assertContrastRatio(DarkOnPrimary, DarkPrimary, description = "深色主色调文字/背景")
        assertContrastRatio(DarkOnSecondary, DarkSecondary, description = "深色次要色调文字/背景")
        
        // 背景和表面对比度
        assertContrastRatio(DarkOnBackground, DarkBackground, description = "深色背景文字/背景")
        assertContrastRatio(DarkOnSurface, DarkSurface, description = "深色表面文字/表面")
        assertContrastRatio(DarkOnSurfaceVariant, DarkSurfaceVariant, description = "深色表面变体文字/表面变体")
        
        // 语义化颜色对比度
        assertContrastRatio(DarkOnError, DarkError, description = "深色错误文字/错误背景")
        assertContrastRatio(DarkOnSuccess, DarkSuccess, description = "深色成功文字/成功背景")
        assertContrastRatio(DarkOnWarning, DarkWarning, description = "深色警告文字/警告背景")
        assertContrastRatio(DarkOnInfo, DarkInfo, description = "深色信息文字/信息背景")
        
        // 轮廓颜色对比度
        assertContrastRatio(DarkOutline, DarkBackground, minRatio = 3.0, description = "深色轮廓/背景")
    }

    @Test
    fun `测试黑白灰主题色彩一致性`() {
        println("=== 黑白灰主题色彩一致性测试 ===")
        
        // 验证主色调确实是灰色系（饱和度应该很低）
        val primaryHsl = rgbToHsl(Primary)
        val secondaryHsl = rgbToHsl(Secondary)
        
        assertTrue("主色调应该是低饱和度的灰色系", primaryHsl.saturation < 0.1f)
        assertTrue("次要色调应该是低饱和度的灰色系", secondaryHsl.saturation < 0.1f)
        
        println("✓ 主色调饱和度: ${"%.2f".format(primaryHsl.saturation * 100)}%")
        println("✓ 次要色调饱和度: ${"%.2f".format(secondaryHsl.saturation * 100)}%")
    }

    /**
     * RGB转HSL辅助函数
     */
    private fun rgbToHsl(color: Color): HSL {
        val r = color.red
        val g = color.green
        val b = color.blue
        
        val max = maxOf(r, g, b)
        val min = minOf(r, g, b)
        val diff = max - min
        
        val lightness = (max + min) / 2f
        
        val saturation = if (diff == 0f) {
            0f
        } else {
            diff / (1f - kotlin.math.abs(2f * lightness - 1f))
        }
        
        return HSL(0f, saturation, lightness)
    }
    
    private data class HSL(val hue: Float, val saturation: Float, val lightness: Float)
}
