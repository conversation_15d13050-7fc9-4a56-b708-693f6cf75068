package com.example.gxzhaiwu.data.model;

/**
 * 仪表盘权限配置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0011\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0087\b\u0018\u0000 \u00182\u00020\u0001:\u0001\u0018B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J1\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00032\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\tR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\t\u00a8\u0006\u0019"}, d2 = {"Lcom/example/gxzhaiwu/data/model/DashboardPermissions;", "", "canViewAllStores", "", "canViewFinancialDetails", "canViewStoreComparison", "canAccessAdvancedStatistics", "(ZZZZ)V", "getCanAccessAdvancedStatistics", "()Z", "getCanViewAllStores", "getCanViewFinancialDetails", "getCanViewStoreComparison", "component1", "component2", "component3", "component4", "copy", "equals", "other", "hashCode", "", "toString", "", "Companion", "app_debug"})
public final class DashboardPermissions {
    private final boolean canViewAllStores = false;
    private final boolean canViewFinancialDetails = false;
    private final boolean canViewStoreComparison = false;
    private final boolean canAccessAdvancedStatistics = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.gxzhaiwu.data.model.DashboardPermissions.Companion Companion = null;
    
    public DashboardPermissions(boolean canViewAllStores, boolean canViewFinancialDetails, boolean canViewStoreComparison, boolean canAccessAdvancedStatistics) {
        super();
    }
    
    public final boolean getCanViewAllStores() {
        return false;
    }
    
    public final boolean getCanViewFinancialDetails() {
        return false;
    }
    
    public final boolean getCanViewStoreComparison() {
        return false;
    }
    
    public final boolean getCanAccessAdvancedStatistics() {
        return false;
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.gxzhaiwu.data.model.DashboardPermissions copy(boolean canViewAllStores, boolean canViewFinancialDetails, boolean canViewStoreComparison, boolean canAccessAdvancedStatistics) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0014\u0010\u0003\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a8\u0006\b"}, d2 = {"Lcom/example/gxzhaiwu/data/model/DashboardPermissions$Companion;", "", "()V", "fromUserRoles", "Lcom/example/gxzhaiwu/data/model/DashboardPermissions;", "roles", "", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.gxzhaiwu.data.model.DashboardPermissions fromUserRoles(@org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> roles) {
            return null;
        }
    }
}