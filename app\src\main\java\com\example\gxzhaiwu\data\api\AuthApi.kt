package com.example.gxzhaiwu.data.api

import com.example.gxzhaiwu.data.model.LoginRequest
import com.example.gxzhaiwu.data.model.LoginResponse
import com.example.gxzhaiwu.data.model.RegisterRequest
import com.example.gxzhaiwu.data.model.RegisterResponse
import com.example.gxzhaiwu.data.model.User
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

interface AuthApi {
    
    @POST("login")
    suspend fun login(@Body request: LoginRequest): Response<ApiResponse<LoginResponse>>
    
    @POST("register")
    suspend fun register(@Body request: RegisterRequest): Response<ApiResponse<RegisterResponse>>
    
    @POST("logout")
    suspend fun logout(@Header("Authorization") token: String): Response<ApiResponse<Unit>>
    
    @GET("user")
    suspend fun getCurrentUser(@Header("Authorization") token: String): Response<ApiResponse<User>>
}
