package com.example.gxzhaiwu.ui.payments

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.utils.RoleUtils
import java.math.BigDecimal
import java.time.LocalDateTime
import javax.inject.Inject

/**
 * 还款管理ViewModel
 * 管理还款记录列表、搜索、过滤和操作
 */
@HiltViewModel
class PaymentManagementViewModel @Inject constructor(
    // TODO: 注入还款仓库和相关服务
) : ViewModel() {

    private val _uiState = MutableStateFlow(PaymentManagementUiState())
    val uiState: StateFlow<PaymentManagementUiState> = _uiState.asStateFlow()

    private val _sideEffect = MutableSharedFlow<PaymentManagementSideEffect>()
    val sideEffect: SharedFlow<PaymentManagementSideEffect> = _sideEffect.asSharedFlow()

    init {
        initializeUserRoles()
        loadPayments()
    }

    /**
     * 初始化用户角色
     */
    private fun initializeUserRoles() {
        viewModelScope.launch {
            try {
                // TODO: 从认证服务获取用户角色
                val userRoles = getMockUserRoles()
                _uiState.update { it.copy(userRoles = userRoles) }
            } catch (e: Exception) {
                handleError("获取用户信息失败", e)
            }
        }
    }

    /**
     * 加载还款记录列表
     */
    fun loadPayments(refresh: Boolean = false) {
        viewModelScope.launch {
            try {
                _uiState.update { 
                    it.copy(
                        isLoading = !refresh,
                        isRefreshing = refresh,
                        hasError = false,
                        errorMessage = null
                    )
                }

                // TODO: 调用实际的API
                val payments = getMockPayments()
                val statistics = calculateStatistics(payments)

                _uiState.update { currentState ->
                    val newPayments = if (refresh) payments else currentState.payments + payments
                    currentState.copy(
                        payments = newPayments,
                        filteredPayments = applyFilters(newPayments, currentState.currentFilter, currentState.searchQuery),
                        statistics = statistics,
                        isLoading = false,
                        isRefreshing = false,
                        hasMoreData = payments.size >= 20 // 假设每页20条
                    )
                }
            } catch (e: Exception) {
                handleError("加载还款记录失败", e)
            }
        }
    }

    /**
     * 搜索还款记录
     */
    fun searchPayments(query: String) {
        _uiState.update { currentState ->
            val filteredPayments = applyFilters(currentState.payments, currentState.currentFilter, query)
            currentState.copy(
                searchQuery = query,
                filteredPayments = filteredPayments
            )
        }
    }

    /**
     * 应用过滤器
     */
    fun applyFilter(filter: PaymentFilter) {
        _uiState.update { currentState ->
            val filteredPayments = applyFilters(currentState.payments, filter, currentState.searchQuery)
            currentState.copy(
                currentFilter = filter,
                filteredPayments = filteredPayments
            )
        }
    }

    /**
     * 清除过滤器
     */
    fun clearFilters() {
        _uiState.update { currentState ->
            val clearedFilter = PaymentFilter()
            val filteredPayments = applyFilters(currentState.payments, clearedFilter, currentState.searchQuery)
            currentState.copy(
                currentFilter = clearedFilter,
                filteredPayments = filteredPayments
            )
        }
    }

    /**
     * 切换查看模式
     */
    fun setViewMode(viewMode: PaymentViewMode) {
        _uiState.update { it.copy(viewMode = viewMode) }
    }

    /**
     * 选择还款记录
     */
    fun selectPayment(payment: Payment) {
        _uiState.update { it.copy(selectedPayment = payment) }
    }

    /**
     * 处理还款操作
     */
    fun handlePaymentAction(action: PaymentAction, payment: Payment) {
        viewModelScope.launch {
            when (action) {
                PaymentAction.VIEW -> {
                    _sideEffect.emit(PaymentManagementSideEffect.NavigateToPaymentDetail(payment.id))
                }
                PaymentAction.EDIT -> {
                    _sideEffect.emit(PaymentManagementSideEffect.NavigateToEditPayment(payment.id))
                }
                PaymentAction.DELETE -> {
                    _sideEffect.emit(
                        PaymentManagementSideEffect.ShowConfirmDialog(
                            "确定要删除这条还款记录吗？",
                            { deletePayment(payment) }
                        )
                    )
                }
                PaymentAction.CONFIRM -> {
                    confirmPayment(payment)
                }
                PaymentAction.CANCEL -> {
                    _sideEffect.emit(
                        PaymentManagementSideEffect.ShowConfirmDialog(
                            "确定要取消这条还款记录吗？",
                            { cancelPayment(payment) }
                        )
                    )
                }
                PaymentAction.REFUND -> {
                    _sideEffect.emit(
                        PaymentManagementSideEffect.ShowConfirmDialog(
                            "确定要申请退款吗？",
                            { refundPayment(payment) }
                        )
                    )
                }
                PaymentAction.VIEW_BILL -> {
                    _sideEffect.emit(PaymentManagementSideEffect.NavigateToBillDetail(payment.billId))
                }
                PaymentAction.DUPLICATE -> {
                    duplicatePayment(payment)
                }
                PaymentAction.EXPORT -> {
                    exportPayment(payment)
                }
            }
        }
    }

    /**
     * 记录新还款
     */
    fun recordPayment(billId: Int? = null) {
        viewModelScope.launch {
            _sideEffect.emit(PaymentManagementSideEffect.NavigateToRecordPayment(billId))
        }
    }

    /**
     * 确认还款
     */
    private suspend fun confirmPayment(payment: Payment) {
        try {
            // TODO: 调用确认API
            _uiState.update { currentState ->
                val updatedPayments = currentState.payments.map { p ->
                    if (p.id == payment.id) {
                        p.copy(status = PaymentStatus.CONFIRMED)
                    } else p
                }
                val filteredPayments = applyFilters(updatedPayments, currentState.currentFilter, currentState.searchQuery)
                currentState.copy(
                    payments = updatedPayments,
                    filteredPayments = filteredPayments,
                    statistics = calculateStatistics(updatedPayments)
                )
            }
            _sideEffect.emit(PaymentManagementSideEffect.ShowSuccess("还款确认成功"))
        } catch (e: Exception) {
            handleError("确认还款失败", e)
        }
    }

    /**
     * 取消还款
     */
    private suspend fun cancelPayment(payment: Payment) {
        try {
            // TODO: 调用取消API
            _uiState.update { currentState ->
                val updatedPayments = currentState.payments.map { p ->
                    if (p.id == payment.id) {
                        p.copy(status = PaymentStatus.CANCELLED)
                    } else p
                }
                val filteredPayments = applyFilters(updatedPayments, currentState.currentFilter, currentState.searchQuery)
                currentState.copy(
                    payments = updatedPayments,
                    filteredPayments = filteredPayments,
                    statistics = calculateStatistics(updatedPayments)
                )
            }
            _sideEffect.emit(PaymentManagementSideEffect.ShowSuccess("还款取消成功"))
        } catch (e: Exception) {
            handleError("取消还款失败", e)
        }
    }

    /**
     * 退款
     */
    private suspend fun refundPayment(payment: Payment) {
        try {
            // TODO: 调用退款API
            _uiState.update { currentState ->
                val updatedPayments = currentState.payments.map { p ->
                    if (p.id == payment.id) {
                        p.copy(status = PaymentStatus.REFUNDED)
                    } else p
                }
                val filteredPayments = applyFilters(updatedPayments, currentState.currentFilter, currentState.searchQuery)
                currentState.copy(
                    payments = updatedPayments,
                    filteredPayments = filteredPayments,
                    statistics = calculateStatistics(updatedPayments)
                )
            }
            _sideEffect.emit(PaymentManagementSideEffect.ShowSuccess("退款申请已提交"))
        } catch (e: Exception) {
            handleError("申请退款失败", e)
        }
    }

    /**
     * 删除还款记录
     */
    private suspend fun deletePayment(payment: Payment) {
        try {
            // TODO: 调用删除API
            _uiState.update { currentState ->
                val updatedPayments = currentState.payments.filter { it.id != payment.id }
                val filteredPayments = applyFilters(updatedPayments, currentState.currentFilter, currentState.searchQuery)
                currentState.copy(
                    payments = updatedPayments,
                    filteredPayments = filteredPayments,
                    statistics = calculateStatistics(updatedPayments)
                )
            }
            _sideEffect.emit(PaymentManagementSideEffect.ShowSuccess("还款记录删除成功"))
        } catch (e: Exception) {
            handleError("删除还款记录失败", e)
        }
    }

    /**
     * 复制还款记录
     */
    private suspend fun duplicatePayment(payment: Payment) {
        try {
            // TODO: 调用复制API
            _sideEffect.emit(PaymentManagementSideEffect.ShowSuccess("还款记录复制成功"))
            loadPayments(refresh = true)
        } catch (e: Exception) {
            handleError("复制还款记录失败", e)
        }
    }

    /**
     * 导出还款记录
     */
    private suspend fun exportPayment(payment: Payment) {
        try {
            // TODO: 实现导出功能
            _sideEffect.emit(PaymentManagementSideEffect.ShowSuccess("还款记录导出成功"))
        } catch (e: Exception) {
            handleError("导出还款记录失败", e)
        }
    }

    /**
     * 应用过滤和搜索逻辑
     */
    private fun applyFilters(payments: List<Payment>, filter: PaymentFilter, searchQuery: String): List<Payment> {
        var filteredPayments = payments

        // 应用搜索
        if (searchQuery.isNotEmpty()) {
            filteredPayments = filteredPayments.filter { payment ->
                payment.paymentNumber.contains(searchQuery, ignoreCase = true) ||
                payment.billNumber.contains(searchQuery, ignoreCase = true) ||
                payment.customerName.contains(searchQuery, ignoreCase = true) ||
                payment.storeName.contains(searchQuery, ignoreCase = true)
            }
        }

        // 应用过滤器
        filter.status?.let { status ->
            filteredPayments = filteredPayments.filter { it.status == status }
        }

        filter.paymentMethod?.let { method ->
            filteredPayments = filteredPayments.filter { it.paymentMethod == method }
        }

        filter.storeId?.let { storeId ->
            filteredPayments = filteredPayments.filter { it.storeId == storeId }
        }

        filter.customerId?.let { customerId ->
            filteredPayments = filteredPayments.filter { it.customerId == customerId }
        }

        filter.billId?.let { billId ->
            filteredPayments = filteredPayments.filter { it.billId == billId }
        }

        filter.dateRange?.let { dateRange ->
            filteredPayments = filteredPayments.filter { dateRange.contains(it.paymentDate) }
        }

        filter.amountRange?.let { amountRange ->
            filteredPayments = filteredPayments.filter { amountRange.contains(it.amount) }
        }

        return filteredPayments
    }

    /**
     * 计算统计信息
     */
    private fun calculateStatistics(payments: List<Payment>): PaymentStatistics {
        val totalAmount = payments.sumOf { it.amount }
        val confirmedAmount = payments.filter { it.status == PaymentStatus.CONFIRMED }.sumOf { it.amount }
        val pendingAmount = payments.filter { it.status == PaymentStatus.PENDING }.sumOf { it.amount }
        val refundedAmount = payments.filter { it.status == PaymentStatus.REFUNDED }.sumOf { it.amount }
        
        val today = LocalDateTime.now().toLocalDate()
        val todayAmount = payments.filter { it.paymentDate.toLocalDate() == today }.sumOf { it.amount }
        
        val thisMonth = LocalDateTime.now().withDayOfMonth(1)
        val thisMonthAmount = payments.filter { it.paymentDate.isAfter(thisMonth) }.sumOf { it.amount }
        
        val paymentMethodBreakdown = payments.groupBy { it.paymentMethod }
            .mapValues { (_, paymentList) -> paymentList.sumOf { it.amount } }

        return PaymentStatistics(
            totalPayments = payments.size,
            totalAmount = totalAmount,
            confirmedAmount = confirmedAmount,
            pendingAmount = pendingAmount,
            refundedAmount = refundedAmount,
            todayAmount = todayAmount,
            thisMonthAmount = thisMonthAmount,
            paymentMethodBreakdown = paymentMethodBreakdown
        )
    }

    /**
     * 处理错误
     */
    private fun handleError(message: String, exception: Exception) {
        _uiState.update { 
            it.copy(
                isLoading = false,
                isRefreshing = false,
                hasError = true,
                errorMessage = "$message: ${exception.message}"
            )
        }
        viewModelScope.launch {
            _sideEffect.emit(PaymentManagementSideEffect.ShowError("$message: ${exception.message}"))
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.update { it.copy(hasError = false, errorMessage = null) }
    }

    // 模拟数据方法
    private suspend fun getMockUserRoles(): List<String> {
        kotlinx.coroutines.delay(100)
        return listOf("admin") // 模拟管理员角色
    }

    private suspend fun getMockPayments(): List<Payment> {
        kotlinx.coroutines.delay(500)
        return listOf(
            Payment(
                id = 1,
                paymentNumber = "PAY-2024-001",
                billId = 1,
                billNumber = "BILL-2024-001",
                customerId = 1,
                customerName = "张三",
                storeId = 1,
                storeName = "总店",
                amount = BigDecimal("500.00"),
                paymentMethod = PaymentMethod.ALIPAY,
                status = PaymentStatus.CONFIRMED,
                paymentDate = LocalDateTime.now().minusDays(1),
                createdAt = LocalDateTime.now().minusDays(1),
                updatedAt = LocalDateTime.now().minusDays(1),
                operatorName = "管理员"
            ),
            Payment(
                id = 2,
                paymentNumber = "PAY-2024-002",
                billId = 2,
                billNumber = "BILL-2024-002",
                customerId = 2,
                customerName = "李四",
                storeId = 1,
                storeName = "总店",
                amount = BigDecimal("1000.00"),
                paymentMethod = PaymentMethod.CASH,
                status = PaymentStatus.PENDING,
                paymentDate = LocalDateTime.now(),
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                operatorName = "店员A"
            )
        )
    }
}
