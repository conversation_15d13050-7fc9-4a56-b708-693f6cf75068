package com.example.gxzhaiwu.data.repository

import com.example.gxzhaiwu.data.model.DashboardOverview
import com.example.gxzhaiwu.data.model.DashboardStatistics

/**
 * 仪表盘数据仓库接口
 * 提供仪表盘相关数据的访问抽象
 */
interface DashboardRepository {

    /**
     * 获取仪表盘概览数据
     * 
     * @return 包含概览数据的Result对象
     */
    suspend fun getOverview(): Result<DashboardOverview>

    /**
     * 获取详细统计数据
     * 
     * @param startDate 开始日期，格式：YYYY-MM-DD（可选）
     * @param endDate 结束日期，格式：YYYY-MM-DD（可选）
     * @return 包含详细统计数据的Result对象
     */
    suspend fun getStatistics(
        startDate: String? = null,
        endDate: String? = null
    ): Result<DashboardStatistics>
}
