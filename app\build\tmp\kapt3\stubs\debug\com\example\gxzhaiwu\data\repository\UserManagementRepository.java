package com.example.gxzhaiwu.data.repository;

/**
 * 用户管理Repository接口
 * 定义用户管理相关的数据操作接口
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\bf\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006H&J\"\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00070\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\f\u0010\u0004J\"\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00070\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000f\u0010\u0004J$\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\b0\n2\u0006\u0010\u0011\u001a\u00020\u0012H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0013\u0010\u0014JH\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\n2\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00182\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u00182\b\b\u0002\u0010\u001a\u001a\u00020\u00122\b\b\u0002\u0010\u001b\u001a\u00020\u0012H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001c\u0010\u001dJ\u001c\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u00062\u0006\u0010\u001f\u001a\u00020\u0018H&J2\u0010 \u001a\b\u0012\u0004\u0012\u00020\b0\n2\u0006\u0010\u0011\u001a\u00020\u00122\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00120\u0007H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010#J2\u0010$\u001a\b\u0012\u0004\u0012\u00020\b0\n2\u0006\u0010\u0011\u001a\u00020\u00122\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00120\u0007H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b&\u0010#\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\'"}, d2 = {"Lcom/example/gxzhaiwu/data/repository/UserManagementRepository;", "", "clearCache", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCachedUsers", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/gxzhaiwu/data/model/UserDetail;", "getRoles", "Lkotlin/Result;", "Lcom/example/gxzhaiwu/data/model/Role;", "getRoles-IoAF18A", "getStores", "Lcom/example/gxzhaiwu/data/api/Store;", "getStores-IoAF18A", "getUserDetail", "userId", "", "getUserDetail-gIAlu-s", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUsers", "Lcom/example/gxzhaiwu/data/model/PaginatedUserData;", "search", "", "role", "page", "perPage", "getUsers-yxL6bBk", "(Ljava/lang/String;Ljava/lang/String;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchUsers", "query", "updateUserRoles", "roleIds", "updateUserRoles-0E7RQCE", "(ILjava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserStores", "storeIds", "updateUserStores-0E7RQCE", "app_debug"})
public abstract interface UserManagementRepository {
    
    /**
     * 搜索用户（本地缓存搜索）
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.gxzhaiwu.data.model.UserDetail>> searchUsers(@org.jetbrains.annotations.NotNull()
    java.lang.String query);
    
    /**
     * 获取缓存的用户列表
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.gxzhaiwu.data.model.UserDetail>> getCachedUsers();
    
    /**
     * 清除缓存
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearCache(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 用户管理Repository接口
     * 定义用户管理相关的数据操作接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}