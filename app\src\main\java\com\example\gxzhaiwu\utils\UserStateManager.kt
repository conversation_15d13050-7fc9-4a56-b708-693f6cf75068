package com.example.gxzhaiwu.utils

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户状态管理器
 * 管理全局用户状态，包括角色、权限等信息
 */
@Singleton
class UserStateManager @Inject constructor() {

    private val _userState = MutableStateFlow(UserState())
    val userState: StateFlow<UserState> = _userState.asStateFlow()

    /**
     * 设置用户信息
     */
    fun setUser(
        userId: Int,
        userName: String,
        userEmail: String,
        userRoles: List<String>,
        userStoreIds: List<Int> = emptyList()
    ) {
        _userState.value = UserState(
            isLoggedIn = true,
            userId = userId,
            userName = userName,
            userEmail = userEmail,
            userRoles = userRoles,
            userStoreIds = userStoreIds
        )
    }

    /**
     * 清除用户状态（登出）
     */
    fun clearUser() {
        _userState.value = UserState()
    }

    /**
     * 获取当前用户角色
     */
    fun getCurrentUserRoles(): List<String> {
        return _userState.value.userRoles
    }

    /**
     * 获取当前用户ID
     */
    fun getCurrentUserId(): Int? {
        return if (_userState.value.isLoggedIn) _userState.value.userId else null
    }

    /**
     * 获取当前用户名
     */
    fun getCurrentUserName(): String? {
        return if (_userState.value.isLoggedIn) _userState.value.userName else null
    }

    /**
     * 检查用户是否已登录
     */
    fun isLoggedIn(): Boolean {
        return _userState.value.isLoggedIn
    }

    /**
     * 检查用户是否有特定权限
     */
    fun hasPermission(permission: Permission): Boolean {
        return RoleUtils.hasPermission(_userState.value.userRoles, permission)
    }

    /**
     * 检查用户是否为管理员
     */
    fun isAdmin(): Boolean {
        return RoleUtils.isAdmin(_userState.value.userRoles)
    }

    /**
     * 检查用户是否为店长
     */
    fun isStoreOwner(): Boolean {
        return RoleUtils.isStoreOwner(_userState.value.userRoles)
    }

    /**
     * 检查用户是否为店员
     */
    fun isStoreStaff(): Boolean {
        return RoleUtils.isStoreStaff(_userState.value.userRoles)
    }

    /**
     * 获取用户角色显示名称
     */
    fun getUserRoleDisplayName(): String {
        return RoleUtils.getRoleDisplayName(_userState.value.userRoles)
    }

    /**
     * 模拟登录（用于测试）
     */
    fun simulateLogin(roleType: String = "admin") {
        when (roleType) {
            "admin" -> {
                setUser(
                    userId = 1,
                    userName = "系统管理员",
                    userEmail = "<EMAIL>",
                    userRoles = listOf("admin"),
                    userStoreIds = listOf(1, 2, 3)
                )
            }
            "store_owner" -> {
                setUser(
                    userId = 2,
                    userName = "店长",
                    userEmail = "<EMAIL>",
                    userRoles = listOf("store_owner"),
                    userStoreIds = listOf(1)
                )
            }
            "store_staff" -> {
                setUser(
                    userId = 3,
                    userName = "店员",
                    userEmail = "<EMAIL>",
                    userRoles = listOf("store_staff"),
                    userStoreIds = listOf(1)
                )
            }
        }
    }
}

/**
 * 用户状态数据类
 */
data class UserState(
    val isLoggedIn: Boolean = false,
    val userId: Int = 0,
    val userName: String = "",
    val userEmail: String = "",
    val userRoles: List<String> = emptyList(),
    val userStoreIds: List<Int> = emptyList()
)
