package com.example.gxzhaiwu.ui.theme;

/**
 * 便捷的颜色访问方法
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001d\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0007\u0010\bJ\u001d\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\n\u0010\bJ\u001d\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\f\u0010\bJ\u001d\u0010\r\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000e\u0010\bJ\u001d\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0010\u0010\bJ\u001d\u0010\u0011\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0012\u0010\b\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0013"}, d2 = {"Lcom/example/gxzhaiwu/ui/theme/ExtendedColors;", "", "()V", "infoContainer", "Landroidx/compose/ui/graphics/Color;", "colorScheme", "Landroidx/compose/material3/ColorScheme;", "infoContainer-vNxB06k", "(Landroidx/compose/material3/ColorScheme;)J", "onInfoContainer", "onInfoContainer-vNxB06k", "onSuccessContainer", "onSuccessContainer-vNxB06k", "onWarningContainer", "onWarningContainer-vNxB06k", "successContainer", "successContainer-vNxB06k", "warningContainer", "warningContainer-vNxB06k", "app_debug"})
public final class ExtendedColors {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.gxzhaiwu.ui.theme.ExtendedColors INSTANCE = null;
    
    private ExtendedColors() {
        super();
    }
}