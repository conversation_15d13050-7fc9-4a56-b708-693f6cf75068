package com.example.gxzhaiwu.ui.auth.register

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gxzhaiwu.data.repository.AuthRepository
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import javax.inject.Inject

@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class RegisterScreenTest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createComposeRule()

    @Inject
    lateinit var authRepository: AuthRepository

    private var registerSuccessCalled = false
    private var navigateToLoginCalled = false

    @Before
    fun setup() {
        hiltRule.inject()
        registerSuccessCalled = false
        navigateToLoginCalled = false
    }

    @Test
    fun registerScreen_displaysAllRequiredFields() {
        composeTestRule.setContent {
            GxZhaiWuTheme {
                RegisterScreen(
                    onRegisterSuccess = { registerSuccessCalled = true },
                    onNavigateToLogin = { navigateToLoginCalled = true }
                )
            }
        }

        // Verify all input fields are displayed
        composeTestRule.onNodeWithText("姓名").assertIsDisplayed()
        composeTestRule.onNodeWithText("用户名").assertIsDisplayed()
        composeTestRule.onNodeWithText("邮箱").assertIsDisplayed()
        composeTestRule.onNodeWithText("密码").assertIsDisplayed()
        composeTestRule.onNodeWithText("确认密码").assertIsDisplayed()
        
        // Verify buttons are displayed
        composeTestRule.onNodeWithText("注册").assertIsDisplayed()
        composeTestRule.onNodeWithText("立即登录").assertIsDisplayed()
        
        // Verify title and subtitle
        composeTestRule.onNodeWithText("债务管理系统").assertIsDisplayed()
        composeTestRule.onNodeWithText("创建新账户").assertIsDisplayed()
    }

    @Test
    fun registerScreen_registerButtonDisabledWhenFormInvalid() {
        composeTestRule.setContent {
            GxZhaiWuTheme {
                RegisterScreen(
                    onRegisterSuccess = { registerSuccessCalled = true },
                    onNavigateToLogin = { navigateToLoginCalled = true }
                )
            }
        }

        // Register button should be disabled when form is empty
        composeTestRule.onNodeWithText("注册").assertIsNotEnabled()
    }

    @Test
    fun registerScreen_canInputTextInAllFields() {
        composeTestRule.setContent {
            GxZhaiWuTheme {
                RegisterScreen(
                    onRegisterSuccess = { registerSuccessCalled = true },
                    onNavigateToLogin = { navigateToLoginCalled = true }
                )
            }
        }

        // Input text in all fields
        composeTestRule.onNodeWithText("姓名").performTextInput("张三")
        composeTestRule.onNodeWithText("用户名").performTextInput("zhangsan")
        composeTestRule.onNodeWithText("邮箱").performTextInput("<EMAIL>")
        composeTestRule.onNodeWithText("密码").performTextInput("password123")
        composeTestRule.onNodeWithText("确认密码").performTextInput("password123")

        // Verify text was input correctly
        composeTestRule.onNodeWithDisplayValue("张三").assertIsDisplayed()
        composeTestRule.onNodeWithDisplayValue("zhangsan").assertIsDisplayed()
        composeTestRule.onNodeWithDisplayValue("<EMAIL>").assertIsDisplayed()
    }

    @Test
    fun registerScreen_registerButtonEnabledWhenFormValid() {
        composeTestRule.setContent {
            GxZhaiWuTheme {
                RegisterScreen(
                    onRegisterSuccess = { registerSuccessCalled = true },
                    onNavigateToLogin = { navigateToLoginCalled = true }
                )
            }
        }

        // Fill all fields with valid data
        composeTestRule.onNodeWithText("姓名").performTextInput("张三")
        composeTestRule.onNodeWithText("用户名").performTextInput("zhangsan")
        composeTestRule.onNodeWithText("邮箱").performTextInput("<EMAIL>")
        composeTestRule.onNodeWithText("密码").performTextInput("password123")
        composeTestRule.onNodeWithText("确认密码").performTextInput("password123")

        // Register button should be enabled
        composeTestRule.onNodeWithText("注册").assertIsEnabled()
    }

    @Test
    fun registerScreen_showsValidationErrors() {
        composeTestRule.setContent {
            GxZhaiWuTheme {
                RegisterScreen(
                    onRegisterSuccess = { registerSuccessCalled = true },
                    onNavigateToLogin = { navigateToLoginCalled = true }
                )
            }
        }

        // Input invalid email
        composeTestRule.onNodeWithText("邮箱").performTextInput("invalid-email")
        
        // Input mismatched passwords
        composeTestRule.onNodeWithText("密码").performTextInput("password123")
        composeTestRule.onNodeWithText("确认密码").performTextInput("different")

        // Trigger validation by trying to register
        composeTestRule.onNodeWithText("注册").performClick()

        // Should show validation errors
        composeTestRule.onNodeWithText("邮箱格式不正确").assertIsDisplayed()
        composeTestRule.onNodeWithText("两次输入的密码不一致").assertIsDisplayed()
    }

    @Test
    fun registerScreen_navigateToLoginWhenLinkClicked() {
        composeTestRule.setContent {
            GxZhaiWuTheme {
                RegisterScreen(
                    onRegisterSuccess = { registerSuccessCalled = true },
                    onNavigateToLogin = { navigateToLoginCalled = true }
                )
            }
        }

        // Click login link
        composeTestRule.onNodeWithText("立即登录").performClick()

        // Verify navigation callback was called
        assert(navigateToLoginCalled)
    }

    @Test
    fun registerScreen_passwordFieldsAreObscured() {
        composeTestRule.setContent {
            GxZhaiWuTheme {
                RegisterScreen(
                    onRegisterSuccess = { registerSuccessCalled = true },
                    onNavigateToLogin = { navigateToLoginCalled = true }
                )
            }
        }

        // Input password
        composeTestRule.onNodeWithText("密码").performTextInput("password123")
        composeTestRule.onNodeWithText("确认密码").performTextInput("password123")

        // Password should not be visible as plain text
        composeTestRule.onNodeWithDisplayValue("password123").assertDoesNotExist()
    }

    @Test
    fun registerScreen_showsLoadingStateWhenRegistering() {
        // Mock repository to simulate loading
        val mockRepository = mockk<AuthRepository>()
        coEvery { mockRepository.register(any()) } coAnswers {
            kotlinx.coroutines.delay(1000) // Simulate network delay
            Result.success(Pair("token", mockk()))
        }

        composeTestRule.setContent {
            GxZhaiWuTheme {
                RegisterScreen(
                    onRegisterSuccess = { registerSuccessCalled = true },
                    onNavigateToLogin = { navigateToLoginCalled = true }
                )
            }
        }

        // Fill form with valid data
        composeTestRule.onNodeWithText("姓名").performTextInput("张三")
        composeTestRule.onNodeWithText("用户名").performTextInput("zhangsan")
        composeTestRule.onNodeWithText("邮箱").performTextInput("<EMAIL>")
        composeTestRule.onNodeWithText("密码").performTextInput("password123")
        composeTestRule.onNodeWithText("确认密码").performTextInput("password123")

        // Click register button
        composeTestRule.onNodeWithText("注册").performClick()

        // Should show loading indicator (this test might need adjustment based on actual loading implementation)
        // composeTestRule.onNode(hasTestTag("loading_indicator")).assertIsDisplayed()
    }

    @Test
    fun registerScreen_darkThemeDisplaysCorrectly() {
        composeTestRule.setContent {
            GxZhaiWuTheme(darkTheme = true) {
                RegisterScreen(
                    onRegisterSuccess = { registerSuccessCalled = true },
                    onNavigateToLogin = { navigateToLoginCalled = true }
                )
            }
        }

        // Verify all components are displayed in dark theme
        composeTestRule.onNodeWithText("债务管理系统").assertIsDisplayed()
        composeTestRule.onNodeWithText("创建新账户").assertIsDisplayed()
        composeTestRule.onNodeWithText("注册").assertIsDisplayed()
        composeTestRule.onNodeWithText("立即登录").assertIsDisplayed()
    }

    @Test
    fun registerScreen_lightThemeDisplaysCorrectly() {
        composeTestRule.setContent {
            GxZhaiWuTheme(darkTheme = false) {
                RegisterScreen(
                    onRegisterSuccess = { registerSuccessCalled = true },
                    onNavigateToLogin = { navigateToLoginCalled = true }
                )
            }
        }

        // Verify all components are displayed in light theme
        composeTestRule.onNodeWithText("债务管理系统").assertIsDisplayed()
        composeTestRule.onNodeWithText("创建新账户").assertIsDisplayed()
        composeTestRule.onNodeWithText("注册").assertIsDisplayed()
        composeTestRule.onNodeWithText("立即登录").assertIsDisplayed()
    }
}
