package com.example.gxzhaiwu.data.model

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 还款记录数据模型
 * 对应API文档中的还款相关接口
 */
data class Payment(
    val id: Int,
    val paymentNumber: String,               // 还款编号
    val billId: Int,                        // 关联账单ID
    val billNumber: String,                 // 账单编号
    val customerId: Int,                    // 客户ID
    val customerName: String,               // 客户姓名
    val storeId: Int,                      // 门店ID
    val storeName: String,                 // 门店名称
    val amount: BigDecimal,                // 还款金额
    val paymentMethod: PaymentMethod,      // 还款方式
    val status: PaymentStatus,             // 还款状态
    val paymentDate: LocalDateTime,        // 还款日期
    val createdAt: LocalDateTime,          // 创建时间
    val updatedAt: LocalDateTime,          // 更新时间
    val description: String? = null,       // 还款备注
    val receiptUrl: String? = null,        // 收据URL
    val operatorId: Int? = null,           // 操作员ID
    val operatorName: String? = null       // 操作员姓名
) {
    /**
     * 获取状态显示文本
     */
    fun getStatusDisplayText(): String {
        return status.displayName
    }

    /**
     * 获取状态颜色标识
     */
    fun getStatusColorKey(): String {
        return when (status) {
            PaymentStatus.PENDING -> "warning"
            PaymentStatus.CONFIRMED -> "success"
            PaymentStatus.CANCELLED -> "error"
            PaymentStatus.REFUNDED -> "info"
        }
    }

    /**
     * 获取支付方式显示文本
     */
    fun getPaymentMethodDisplayText(): String {
        return paymentMethod.displayName
    }

    /**
     * 检查是否可以取消
     */
    fun canCancel(): Boolean {
        return status == PaymentStatus.PENDING
    }

    /**
     * 检查是否可以退款
     */
    fun canRefund(): Boolean {
        return status == PaymentStatus.CONFIRMED
    }
}

/**
 * 还款状态枚举
 */
enum class PaymentStatus(val value: String, val displayName: String) {
    PENDING("pending", "待确认"),
    CONFIRMED("confirmed", "已确认"),
    CANCELLED("cancelled", "已取消"),
    REFUNDED("refunded", "已退款");

    companion object {
        fun fromValue(value: String): PaymentStatus {
            return values().find { it.value == value } ?: PENDING
        }

        fun getActiveStatuses(): List<PaymentStatus> {
            return listOf(PENDING, CONFIRMED)
        }
    }
}

/**
 * 支付方式枚举
 */
enum class PaymentMethod(val value: String, val displayName: String) {
    CASH("cash", "现金"),
    BANK_TRANSFER("bank_transfer", "银行转账"),
    ALIPAY("alipay", "支付宝"),
    WECHAT_PAY("wechat_pay", "微信支付"),
    CREDIT_CARD("credit_card", "信用卡"),
    OTHER("other", "其他");

    companion object {
        fun fromValue(value: String): PaymentMethod {
            return values().find { it.value == value } ?: CASH
        }

        fun getDigitalMethods(): List<PaymentMethod> {
            return listOf(ALIPAY, WECHAT_PAY, BANK_TRANSFER, CREDIT_CARD)
        }
    }
}

/**
 * 创建还款记录请求
 */
data class CreatePaymentRequest(
    val billId: Int,
    val amount: BigDecimal,
    val paymentMethod: PaymentMethod,
    val paymentDate: LocalDateTime = LocalDateTime.now(),
    val description: String? = null,
    val receiptUrl: String? = null
)

/**
 * 更新还款记录请求
 */
data class UpdatePaymentRequest(
    val amount: BigDecimal? = null,
    val paymentMethod: PaymentMethod? = null,
    val paymentDate: LocalDateTime? = null,
    val description: String? = null,
    val status: PaymentStatus? = null
)

/**
 * 还款查询参数
 */
data class PaymentQueryParams(
    val billId: Int? = null,
    val customerId: Int? = null,
    val storeId: Int? = null,
    val status: PaymentStatus? = null,
    val paymentMethod: PaymentMethod? = null,
    val startDate: LocalDateTime? = null,
    val endDate: LocalDateTime? = null,
    val page: Int = 1,
    val pageSize: Int = 20,
    val sortBy: String = "payment_date",
    val sortOrder: String = "desc"
)

/**
 * 还款统计信息
 */
data class PaymentStatistics(
    val totalPayments: Int,
    val totalAmount: BigDecimal,
    val confirmedAmount: BigDecimal,
    val pendingAmount: BigDecimal,
    val refundedAmount: BigDecimal,
    val todayAmount: BigDecimal,
    val thisMonthAmount: BigDecimal,
    val paymentMethodBreakdown: Map<PaymentMethod, BigDecimal> = emptyMap()
) {
    /**
     * 计算确认率
     */
    fun getConfirmationRate(): Float {
        return if (totalAmount > BigDecimal.ZERO) {
            (confirmedAmount.toFloat() / totalAmount.toFloat()).coerceIn(0f, 1f)
        } else {
            0f
        }
    }

    /**
     * 获取最常用的支付方式
     */
    fun getMostUsedPaymentMethod(): PaymentMethod? {
        return paymentMethodBreakdown.maxByOrNull { it.value }?.key
    }

    /**
     * 计算今日收款占比
     */
    fun getTodayPercentage(): Float {
        return if (totalAmount > BigDecimal.ZERO) {
            (todayAmount.toFloat() / totalAmount.toFloat()).coerceIn(0f, 1f)
        } else {
            0f
        }
    }
}

/**
 * 还款汇总信息（按账单分组）
 */
data class PaymentSummaryByBill(
    val billId: Int,
    val billNumber: String,
    val customerName: String,
    val totalBillAmount: BigDecimal,
    val totalPaidAmount: BigDecimal,
    val remainingAmount: BigDecimal,
    val paymentCount: Int,
    val lastPaymentDate: LocalDateTime?,
    val payments: List<Payment> = emptyList()
) {
    /**
     * 计算付款进度
     */
    fun getPaymentProgress(): Float {
        return if (totalBillAmount > BigDecimal.ZERO) {
            (totalPaidAmount.toFloat() / totalBillAmount.toFloat()).coerceIn(0f, 1f)
        } else {
            0f
        }
    }

    /**
     * 检查是否已完全付清
     */
    fun isFullyPaid(): Boolean {
        return remainingAmount <= BigDecimal.ZERO
    }
}

/**
 * 批量还款请求
 */
data class BatchPaymentRequest(
    val payments: List<CreatePaymentRequest>,
    val description: String? = null
)

/**
 * 还款确认请求
 */
data class ConfirmPaymentRequest(
    val paymentIds: List<Int>,
    val operatorNote: String? = null
)

/**
 * 还款退款请求
 */
data class RefundPaymentRequest(
    val paymentId: Int,
    val refundAmount: BigDecimal? = null, // 如果为null则全额退款
    val refundReason: String,
    val refundMethod: PaymentMethod? = null // 如果为null则使用原支付方式
)
