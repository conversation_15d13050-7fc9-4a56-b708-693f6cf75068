package com.example.gxzhaiwu.utils

import org.junit.Assert.*
import org.junit.Test

class SecurityUtilsTest {

    @Test
    fun isValidEmail_withValidEmail_returnsTrue() {
        assertTrue(SecurityUtils.isValidEmail("<EMAIL>"))
        assertTrue(SecurityUtils.isValidEmail("<EMAIL>"))
        assertTrue(SecurityUtils.isValidEmail("<EMAIL>"))
    }

    @Test
    fun isValidEmail_withInvalidEmail_returnsFalse() {
        assertFalse(SecurityUtils.isValidEmail("invalid-email"))
        assertFalse(SecurityUtils.isValidEmail("@example.com"))
        assertFalse(SecurityUtils.isValidEmail("test@"))
        assertFalse(SecurityUtils.isValidEmail("test.example.com"))
    }

    @Test
    fun isValidUsername_withValidUsername_returnsTrue() {
        assertTrue(SecurityUtils.isValidUsername("testuser"))
        assertTrue(SecurityUtils.isValidUsername("test_user"))
        assertTrue(SecurityUtils.isValidUsername("user123"))
        assertTrue(SecurityUtils.isValidUsername("TestUser"))
    }

    @Test
    fun isValidUsername_withInvalidUsername_returnsFalse() {
        assertFalse(SecurityUtils.isValidUsername("ab")) // 太短
        assertFalse(SecurityUtils.isValidUsername("a".repeat(21))) // 太长
        assertFalse(SecurityUtils.isValidUsername("test-user")) // 包含连字符
        assertFalse(SecurityUtils.isValidUsername("test user")) // 包含空格
        assertFalse(SecurityUtils.isValidUsername("test@user")) // 包含特殊字符
    }

    @Test
    fun sanitizeInput_withDangerousInput_returnsSafeString() {
        val input = "<script>alert('xss')</script>"
        val result = SecurityUtils.sanitizeInput(input)
        assertEquals("&lt;script&gt;alert(&#x27;xss&#x27;)&lt;&#x2F;script&gt;", result)
    }

    @Test
    fun sanitizeInput_withNormalInput_returnsTrimmedString() {
        val input = "  normal text  "
        val result = SecurityUtils.sanitizeInput(input)
        assertEquals("normal text", result)
    }

    @Test
    fun isValidJwtFormat_withValidJwt_returnsTrue() {
        val validJwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        assertTrue(SecurityUtils.isValidJwtFormat(validJwt))
    }

    @Test
    fun isValidJwtFormat_withInvalidJwt_returnsFalse() {
        assertFalse(SecurityUtils.isValidJwtFormat("invalid.jwt"))
        assertFalse(SecurityUtils.isValidJwtFormat("invalid"))
        assertFalse(SecurityUtils.isValidJwtFormat(""))
        assertFalse(SecurityUtils.isValidJwtFormat("part1.part2."))
    }

    @Test
    fun containsSqlInjection_withSqlKeywords_returnsTrue() {
        assertTrue(SecurityUtils.containsSqlInjection("SELECT * FROM users"))
        assertTrue(SecurityUtils.containsSqlInjection("DROP TABLE users"))
        assertTrue(SecurityUtils.containsSqlInjection("1' OR '1'='1"))
        assertTrue(SecurityUtils.containsSqlInjection("<script>alert('xss')</script>"))
    }

    @Test
    fun containsSqlInjection_withNormalInput_returnsFalse() {
        assertFalse(SecurityUtils.containsSqlInjection("normal user input"))
        assertFalse(SecurityUtils.containsSqlInjection("<EMAIL>"))
        assertFalse(SecurityUtils.containsSqlInjection("password123"))
    }

    @Test
    fun md5Hash_withSameInput_returnsSameHash() {
        val input = "test string"
        val hash1 = SecurityUtils.md5Hash(input)
        val hash2 = SecurityUtils.md5Hash(input)
        assertEquals(hash1, hash2)
    }

    @Test
    fun md5Hash_withDifferentInput_returnsDifferentHash() {
        val hash1 = SecurityUtils.md5Hash("string1")
        val hash2 = SecurityUtils.md5Hash("string2")
        assertNotEquals(hash1, hash2)
    }

    @Test
    fun generateSecureRandomString_withSpecifiedLength_returnsCorrectLength() {
        val length = 10
        val result = SecurityUtils.generateSecureRandomString(length)
        assertEquals(length, result.length)
    }

    @Test
    fun generateSecureRandomString_multipleCalls_returnsDifferentStrings() {
        val string1 = SecurityUtils.generateSecureRandomString(10)
        val string2 = SecurityUtils.generateSecureRandomString(10)
        assertNotEquals(string1, string2)
    }
}
