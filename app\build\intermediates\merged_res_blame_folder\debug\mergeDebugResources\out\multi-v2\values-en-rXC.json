{"logs": [{"outputFile": "com.example.gxzhaiwu.app-mergeDebugResources-61:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98f289bf6c45d262a7223b262b65f304\\transformed\\foundation-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,243", "endColumns": "187,186", "endOffsets": "238,425"}, "to": {"startLines": "84,85", "startColumns": "4,4", "startOffsets": "16809,16997", "endColumns": "187,186", "endOffsets": "16992,17179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e7a34afc2b6fd3a244eccb13c4cf553\\transformed\\ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,2001,2186,2352,2530,2705,2876,3055,3222", "endColumns": "190,184,196,201,186,184,192,187,186,180,184,165,177,174,170,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1996,2181,2347,2525,2700,2871,3050,3217,3455"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,79,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1532,1723,1908,2105,2307,2494,2679,14590,14778,14965,15146,15331,15497,15675,15850,16225,16404,16571", "endColumns": "190,184,196,201,186,184,192,187,186,180,184,165,177,174,170,178,166,237", "endOffsets": "1718,1903,2100,2302,2489,2674,2867,14773,14960,15141,15326,15492,15670,15845,16016,16399,16566,16804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\34672be5908381f92b2f1efab7609872\\transformed\\material3-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,492,706,923,1124,1322,1532,1771,1989,2228,2414,2614,2808,3008,3229,3457,3664,3894,4120,4349,4610,4832,5051,5272,5497,5692,5892,6110,6336,6535,6738,6943,7173,7414,7623,7824,8003,8201,8397,8587,8777,8982,9164,9350,9554,9758,9960,10162,10352,10561,10765,10972,11191,11374,11575", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "270,487,701,918,1119,1317,1527,1766,1984,2223,2409,2609,2803,3003,3224,3452,3659,3889,4115,4344,4605,4827,5046,5267,5492,5687,5887,6105,6331,6530,6733,6938,7168,7409,7618,7819,7998,8196,8392,8582,8772,8977,9159,9345,9549,9753,9955,10157,10347,10556,10760,10967,11186,11369,11570,11768"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2872,3092,3309,3523,3740,3941,4139,4349,4588,4806,5045,5231,5431,5625,5825,6046,6274,6481,6711,6937,7166,7427,7649,7868,8089,8314,8509,8709,8927,9153,9352,9555,9760,9990,10231,10440,10641,10820,11018,11214,11404,11594,11799,11981,12167,12371,12575,12777,12979,13169,13378,13582,13789,14008,14191,14392", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "3087,3304,3518,3735,3936,4134,4344,4583,4801,5040,5226,5426,5620,5820,6041,6269,6476,6706,6932,7161,7422,7644,7863,8084,8309,8504,8704,8922,9148,9347,9550,9755,9985,10226,10435,10636,10815,11013,11209,11399,11589,11794,11976,12162,12366,12570,12772,12974,13164,13373,13577,13784,14003,14186,14387,14585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca95cc56ae6c869d643262611d469661\\transformed\\core-1.16.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "2,3,4,5,6,7,8,80", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,301,506,707,908,1115,1320,16021", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "296,501,702,903,1110,1315,1527,16220"}}]}]}