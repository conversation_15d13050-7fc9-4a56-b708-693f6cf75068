# 债务管理系统 - 主题适配指南

## 概述

本指南为债务管理系统Android应用提供完整的主题适配规范，确保在浅色模式和深色模式下都有优秀的用户体验。

## 色彩系统

### Material Design 3 色彩角色

我们的应用基于Material Design 3色彩系统，支持完整的浅色和深色主题：

#### 主要颜色 (Primary)
- **浅色模式**: `#1976D2` (蓝色)
- **深色模式**: `#90CAF9` (浅蓝色)
- **用途**: 主要操作按钮、重要文本、品牌元素

#### 次要颜色 (Secondary)
- **浅色模式**: `#0288D1` (青色)
- **深色模式**: `#81D4FA` (浅青色)
- **用途**: 次要操作、辅助元素

#### 背景和表面
- **Background**: 应用主背景
  - 浅色: `#FEFBFF`
  - 深色: `#0F0F0F`
- **Surface**: 卡片、对话框背景
  - 浅色: `#FFFFFF`
  - 深色: `#1C1B1F`
- **SurfaceVariant**: 输入框、次要表面
  - 浅色: `#E7E0EC`
  - 深色: `#49454F`

### 扩展色彩系统

#### 语义化颜色
```kotlin
// 成功状态
MaterialTheme.colorScheme.success
MaterialTheme.colorScheme.onSuccess

// 警告状态
MaterialTheme.colorScheme.warning
MaterialTheme.colorScheme.onWarning

// 信息状态
MaterialTheme.colorScheme.info
MaterialTheme.colorScheme.onInfo
```

#### 使用示例
```kotlin
// 错误提示
Text(
    text = errorMessage,
    color = MaterialTheme.colorScheme.error
)

// 成功提示
Text(
    text = "操作成功",
    color = MaterialTheme.colorScheme.success
)
```

## 组件主题适配

### AuthTextField 组件

#### 颜色状态映射
- **聚焦状态**: `primary` 颜色
- **未聚焦状态**: `outline` 颜色
- **错误状态**: `error` 颜色
- **禁用状态**: `disabledColor()` 扩展方法

#### 实现示例
```kotlin
OutlinedTextField(
    colors = OutlinedTextFieldDefaults.colors(
        focusedBorderColor = MaterialTheme.colorScheme.primary,
        unfocusedBorderColor = MaterialTheme.colorScheme.outline,
        errorBorderColor = MaterialTheme.colorScheme.error,
        disabledBorderColor = MaterialTheme.colorScheme.disabledColor()
    )
)
```

### AuthButton 组件

#### 状态适配
- **正常状态**: `primary` 容器色，`onPrimary` 内容色
- **禁用状态**: `disabledContainerColor()` 和 `disabledColor()`
- **加载状态**: 保持正常状态颜色，显示进度指示器

#### Elevation 设置
```kotlin
elevation = ButtonDefaults.buttonElevation(
    defaultElevation = 2.dp,
    pressedElevation = 4.dp,
    disabledElevation = 0.dp
)
```

### Card 组件

#### 深色模式优化
使用 `cardElevationColor()` 扩展方法根据elevation自动调整颜色：

```kotlin
Card(
    colors = CardDefaults.cardColors(
        containerColor = MaterialTheme.colorScheme.cardElevationColor(3)
    )
)
```

## 可访问性要求

### 对比度标准
- 所有文本必须符合WCAG 2.1 AA标准
- 正常文本: 最小对比度 4.5:1
- 大文本: 最小对比度 3:1

### 颜色使用原则
1. **不依赖颜色传达信息**: 使用图标、文本等辅助手段
2. **提供足够对比度**: 特别是错误状态和重要信息
3. **支持系统主题**: 自动适配用户的主题偏好

## 开发最佳实践

### 1. 始终使用主题颜色
```kotlin
// ✅ 正确
color = MaterialTheme.colorScheme.primary

// ❌ 错误
color = Color(0xFF1976D2)
```

### 2. 使用扩展方法
```kotlin
// ✅ 使用扩展方法获取语义化颜色
color = MaterialTheme.colorScheme.success

// ✅ 使用扩展方法获取状态颜色
color = MaterialTheme.colorScheme.disabledColor()
```

### 3. 测试两种模式
- 在开发过程中切换浅色/深色模式测试
- 验证所有交互状态的视觉效果
- 确保文本在所有背景下都清晰可读

### 4. 组件一致性
- 新组件应遵循现有组件的主题适配模式
- 使用统一的颜色角色和状态映射
- 保持视觉层次的一致性

## 测试检查清单

### 视觉测试
- [ ] 浅色模式下所有页面显示正常
- [ ] 深色模式下所有页面显示正常
- [ ] 主题切换时UI正确响应
- [ ] 所有交互状态颜色正确

### 可访问性测试
- [ ] 文本对比度符合WCAG标准
- [ ] 错误状态清晰可见
- [ ] 禁用状态明显区分
- [ ] 聚焦状态突出显示

### 功能测试
- [ ] 表单验证错误颜色正确
- [ ] 加载状态视觉反馈清晰
- [ ] 按钮状态变化正常
- [ ] 导航元素主题一致

## 常见问题

### Q: 如何添加新的语义化颜色？
A: 在 `ColorSchemeExtensions.kt` 中添加扩展属性，并在 `Color.kt` 中定义对应的颜色值。

### Q: 深色模式下卡片看不清怎么办？
A: 使用 `cardElevationColor()` 方法，它会根据elevation自动调整深色模式下的颜色。

### Q: 如何确保新组件的主题适配？
A: 参考现有的 `AuthTextField` 和 `AuthButton` 组件，使用相同的颜色角色和状态映射模式。

## 更新日志

### v1.0.0 (2025-01-02)
- 建立完整的Material Design 3色彩系统
- 实现浅色和深色模式支持
- 优化AuthTextField和AuthButton组件
- 创建ColorScheme扩展系统
- 建立主题适配最佳实践
