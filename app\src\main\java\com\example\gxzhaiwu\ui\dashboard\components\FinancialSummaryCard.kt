package com.example.gxzhaiwu.ui.dashboard.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.FinancialSummary
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import com.example.gxzhaiwu.ui.theme.cardElevationColor
import com.example.gxzhaiwu.ui.theme.success
import com.example.gxzhaiwu.ui.theme.warning
import com.example.gxzhaiwu.utils.FormatUtils

/**
 * 财务汇总卡片组件
 * 专门用于显示财务相关的汇总数据
 */
@Composable
fun FinancialSummaryCard(
    financialSummary: FinancialSummary,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.cardElevationColor(3)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 3.dp
        ),
        onClick = onClick ?: {}
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "财务概览",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Icon(
                    imageVector = Icons.Default.AccountBalance,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(28.dp)
                )
            }

            // 财务数据网格
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 第一行：总账单金额和已付金额
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    FinancialItem(
                        label = "总账单金额",
                        value = FormatUtils.formatCurrency(financialSummary.total_invoice_amount),
                        icon = Icons.Default.Receipt,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.weight(1f)
                    )
                    
                    FinancialItem(
                        label = "已付金额",
                        value = FormatUtils.formatCurrency(financialSummary.total_paid_amount),
                        icon = Icons.Default.CheckCircle,
                        color = MaterialTheme.colorScheme.success,
                        modifier = Modifier.weight(1f)
                    )
                }

                // 第二行：未付金额和还款总额
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    FinancialItem(
                        label = "未付金额",
                        value = FormatUtils.formatCurrency(financialSummary.total_outstanding_amount),
                        icon = Icons.Default.Warning,
                        color = MaterialTheme.colorScheme.warning,
                        modifier = Modifier.weight(1f)
                    )
                    
                    FinancialItem(
                        label = "还款总额",
                        value = FormatUtils.formatCurrency(financialSummary.total_payment_amount),
                        icon = Icons.Default.Payment,
                        color = MaterialTheme.colorScheme.secondary,
                        modifier = Modifier.weight(1f)
                    )
                }
            }

            // 付款率指示器
            val totalInvoice = financialSummary.total_invoice_amount.toDoubleOrNull() ?: 0.0
            val totalPaid = financialSummary.total_paid_amount.toDoubleOrNull() ?: 0.0
            val paymentRate = if (totalInvoice > 0) (totalPaid / totalInvoice) else 0.0

            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "付款率",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Text(
                        text = "${(paymentRate * 100).toInt()}%",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                LinearProgressIndicator(
                    progress = paymentRate.toFloat(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp),
                    color = when {
                        paymentRate >= 0.8 -> MaterialTheme.colorScheme.success
                        paymentRate >= 0.6 -> MaterialTheme.colorScheme.warning
                        else -> MaterialTheme.colorScheme.error
                    },
                    trackColor = MaterialTheme.colorScheme.surfaceVariant
                )
            }
        }
    }
}

/**
 * 财务数据项组件
 */
@Composable
private fun FinancialItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(20.dp)
            )
            
            Text(
                text = value,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = color
            )
            
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Preview(name = "浅色模式 - 财务汇总卡片")
@Composable
private fun FinancialSummaryCardPreview() {
    GxZhaiWuTheme(darkTheme = false) {
        Surface {
            FinancialSummaryCard(
                financialSummary = FinancialSummary(
                    total_invoice_amount = "125000.00",
                    total_paid_amount = "98000.00",
                    total_outstanding_amount = "27000.00",
                    total_payment_amount = "98000.00"
                ),
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Preview(name = "深色模式 - 财务汇总卡片")
@Composable
private fun FinancialSummaryCardDarkPreview() {
    GxZhaiWuTheme(darkTheme = true) {
        Surface {
            FinancialSummaryCard(
                financialSummary = FinancialSummary(
                    total_invoice_amount = "50000.00",
                    total_paid_amount = "45000.00",
                    total_outstanding_amount = "5000.00",
                    total_payment_amount = "45000.00"
                ),
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}
