package com.example.gxzhaiwu.ui.management.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.ManagementModule
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import com.example.gxzhaiwu.ui.theme.cardElevationColor
import com.example.gxzhaiwu.utils.Permission

/**
 * 管理模块卡片组件
 * 显示单个管理模块的信息和入口
 */
@Composable
fun ModuleCard(
    module: ManagementModule,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .then(
                if (module.enabled) {
                    Modifier.clickable { onClick() }
                } else {
                    Modifier
                }
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (module.enabled) {
                MaterialTheme.colorScheme.cardElevationColor(2)
            } else {
                MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            }
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (module.enabled) 4.dp else 0.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 图标
            val icon = getModuleIcon(module.icon)
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = if (module.enabled) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                },
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 标题
            Text(
                text = module.title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = if (module.enabled) {
                    MaterialTheme.colorScheme.onSurface
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                },
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 描述
            Text(
                text = module.description,
                style = MaterialTheme.typography.bodySmall,
                color = if (module.enabled) {
                    MaterialTheme.colorScheme.onSurfaceVariant
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                },
                textAlign = TextAlign.Center,
                maxLines = 2
            )
        }
    }
}

/**
 * 根据图标名称获取对应的图标
 */
private fun getModuleIcon(iconName: String): ImageVector {
    return when (iconName) {
        "people" -> Icons.Default.People
        "store" -> Icons.Default.Store
        "receipt" -> Icons.Default.Receipt
        "payment" -> Icons.Default.Payment
        "analytics" -> Icons.Default.Analytics
        "settings" -> Icons.Default.Settings
        "admin_panel_settings" -> Icons.Default.AdminPanelSettings
        "business" -> Icons.Default.Business
        "account_balance" -> Icons.Default.AccountBalance
        "assessment" -> Icons.Default.Assessment
        else -> Icons.Default.Dashboard
    }
}

// 预览组件
@Preview(name = "管理模块卡片 - 启用状态")
@Composable
private fun ModuleCardEnabledPreview() {
    GxZhaiWuTheme {
        Surface {
            ModuleCard(
                module = ManagementModule(
                    id = "user_management",
                    title = "用户管理",
                    description = "管理系统用户和权限",
                    icon = "people",
                    requiredPermission = Permission.MANAGE_USERS,
                    route = "user_management",
                    enabled = true
                ),
                onClick = { },
                modifier = Modifier
                    .width(160.dp)
                    .padding(8.dp)
            )
        }
    }
}

@Preview(name = "管理模块卡片 - 禁用状态")
@Composable
private fun ModuleCardDisabledPreview() {
    GxZhaiWuTheme {
        Surface {
            ModuleCard(
                module = ManagementModule(
                    id = "store_management",
                    title = "门店管理",
                    description = "管理门店信息和设置",
                    icon = "store",
                    requiredPermission = Permission.MANAGE_STORES,
                    route = "store_management",
                    enabled = false
                ),
                onClick = { },
                modifier = Modifier
                    .width(160.dp)
                    .padding(8.dp)
            )
        }
    }
}

@Preview(name = "管理模块网格预览")
@Composable
private fun ModuleGridPreview() {
    GxZhaiWuTheme {
        Surface {
            Row(
                modifier = Modifier.padding(16.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                ModuleCard(
                    module = ManagementModule(
                        id = "user_management",
                        title = "用户管理",
                        description = "管理系统用户和权限",
                        icon = "people",
                        requiredPermission = Permission.MANAGE_USERS,
                        route = "user_management",
                        enabled = true
                    ),
                    onClick = { },
                    modifier = Modifier.weight(1f)
                )
                
                ModuleCard(
                    module = ManagementModule(
                        id = "store_management",
                        title = "门店管理",
                        description = "管理门店信息和设置",
                        icon = "store",
                        requiredPermission = Permission.MANAGE_STORES,
                        route = "store_management",
                        enabled = true
                    ),
                    onClick = { },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}
