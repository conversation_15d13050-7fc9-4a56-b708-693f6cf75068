package com.example.gxzhaiwu.ui.usermanagement.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u0012\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u001a\u0012\u0010\u0004\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u001aF\u0010\u0005\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00072\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u001av\u0010\u000e\u001a\u00020\u00012\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\r2\u0018\u0010\u0014\u001a\u0014\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00010\u00152\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0007\u001a2\u0010\u0017\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u00072\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u00072\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u00a8\u0006\u0019"}, d2 = {"EmptyContent", "", "modifier", "Landroidx/compose/ui/Modifier;", "LoadingContent", "PaginationControls", "currentPage", "", "totalPages", "onNextPage", "Lkotlin/Function0;", "onPreviousPage", "enabled", "", "UserListContent", "users", "", "Lcom/example/gxzhaiwu/data/model/UserDetail;", "totalUsers", "isLoading", "onAction", "Lkotlin/Function2;", "Lcom/example/gxzhaiwu/data/model/UserManagementAction;", "UserStatistics", "usersOnPage", "app_debug"})
public final class UserListContentKt {
    
    /**
     * 用户列表内容组件
     * 显示用户列表和分页控制
     */
    @androidx.compose.runtime.Composable()
    public static final void UserListContent(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.gxzhaiwu.data.model.UserDetail> users, int currentPage, int totalPages, int totalUsers, boolean isLoading, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.example.gxzhaiwu.data.model.UserManagementAction, ? super com.example.gxzhaiwu.data.model.UserDetail, kotlin.Unit> onAction, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNextPage, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPreviousPage, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 用户统计信息组件
     */
    @androidx.compose.runtime.Composable()
    private static final void UserStatistics(int totalUsers, int currentPage, int totalPages, int usersOnPage, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 加载状态组件
     */
    @androidx.compose.runtime.Composable()
    private static final void LoadingContent(androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 空状态组件
     */
    @androidx.compose.runtime.Composable()
    private static final void EmptyContent(androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 分页控制组件
     */
    @androidx.compose.runtime.Composable()
    private static final void PaginationControls(int currentPage, int totalPages, kotlin.jvm.functions.Function0<kotlin.Unit> onNextPage, kotlin.jvm.functions.Function0<kotlin.Unit> onPreviousPage, boolean enabled, androidx.compose.ui.Modifier modifier) {
    }
}