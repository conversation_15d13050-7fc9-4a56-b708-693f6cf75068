package com.example.gxzhaiwu.utils

import com.example.gxzhaiwu.data.model.DashboardPermissions
import com.example.gxzhaiwu.data.model.QuickActionData
import com.example.gxzhaiwu.data.model.QuickActionType
import com.example.gxzhaiwu.data.model.UserRole

/**
 * 角色权限工具类
 * 提供角色相关的权限判断和功能配置
 */
object RoleUtils {

    /**
     * 检查用户是否为管理员
     */
    fun isAdmin(roles: List<String>): Boolean {
        return roles.contains(UserRole.ADMIN.value)
    }

    /**
     * 检查用户是否为店长
     */
    fun isStoreOwner(roles: List<String>): Boolean {
        return roles.contains(UserRole.STORE_OWNER.value)
    }

    /**
     * 检查用户是否为店员
     */
    fun isStoreStaff(roles: List<String>): Boolean {
        return roles.contains(UserRole.STORE_STAFF.value)
    }

    /**
     * 获取用户的主要角色（优先级：管理员 > 店长 > 店员）
     */
    fun getPrimaryRole(roles: List<String>): UserRole {
        return when {
            isAdmin(roles) -> UserRole.ADMIN
            isStoreOwner(roles) -> UserRole.STORE_OWNER
            isStoreStaff(roles) -> UserRole.STORE_STAFF
            else -> UserRole.STORE_STAFF // 默认为店员
        }
    }

    /**
     * 获取角色显示名称
     */
    fun getRoleDisplayName(roles: List<String>): String {
        return getPrimaryRole(roles).displayName
    }

    /**
     * 根据用户角色获取仪表盘权限配置
     */
    fun getDashboardPermissions(roles: List<String>): DashboardPermissions {
        return DashboardPermissions.fromUserRoles(roles)
    }

    /**
     * 根据用户角色获取可用的快速操作列表
     */
    fun getAvailableQuickActions(roles: List<String>): List<QuickActionData> {
        val actions = mutableListOf<QuickActionData>()
        
        // 所有角色都可以创建账单和记录还款
        actions.add(
            QuickActionData(
                title = "创建账单",
                description = "为客户创建新的账单",
                icon = "receipt",
                action = QuickActionType.CREATE_INVOICE
            )
        )
        
        actions.add(
            QuickActionData(
                title = "记录还款",
                description = "记录客户的还款信息",
                icon = "payment",
                action = QuickActionType.RECORD_PAYMENT
            )
        )

        // 店长和管理员可以添加客户
        if (isStoreOwner(roles) || isAdmin(roles)) {
            actions.add(
                QuickActionData(
                    title = "添加客户",
                    description = "添加新的客户信息",
                    icon = "people",
                    action = QuickActionType.ADD_CUSTOMER
                )
            )
        }



        return actions
    }

    /**
     * 检查用户是否有权限访问特定功能
     */
    fun hasPermission(roles: List<String>, permission: Permission): Boolean {
        return when (permission) {
            Permission.VIEW_ALL_STORES -> isAdmin(roles)
            Permission.VIEW_FINANCIAL_DETAILS -> isAdmin(roles) || isStoreOwner(roles)
            Permission.VIEW_STORE_COMPARISON -> isAdmin(roles)
            Permission.ACCESS_ADVANCED_STATISTICS -> isAdmin(roles) || isStoreOwner(roles)
            Permission.MANAGE_USERS -> isAdmin(roles)
            Permission.MANAGE_STORES -> isAdmin(roles)
            Permission.CREATE_INVOICE -> true // 所有角色都可以
            Permission.RECORD_PAYMENT -> true // 所有角色都可以
            Permission.ADD_CUSTOMER -> isAdmin(roles) || isStoreOwner(roles)
            Permission.VIEW_REPORTS -> isAdmin(roles)
            // 管理中心相关权限
            Permission.ACCESS_MANAGEMENT_CENTER -> isAdmin(roles) || isStoreOwner(roles)
            Permission.MANAGE_SYSTEM -> isAdmin(roles)
            Permission.MANAGE_CUSTOMERS -> isAdmin(roles) || isStoreOwner(roles)
            Permission.MANAGE_BILLS -> isAdmin(roles) || isStoreOwner(roles)
            Permission.MANAGE_PAYMENTS -> isAdmin(roles) || isStoreOwner(roles)
            Permission.VIEW_ADVANCED_REPORTS -> isAdmin(roles)
        }
    }

    /**
     * 获取角色对应的颜色（用于UI显示）
     */
    fun getRoleColor(roles: List<String>): String {
        return when (getPrimaryRole(roles)) {
            UserRole.ADMIN -> "primary"
            UserRole.STORE_OWNER -> "secondary"
            UserRole.STORE_STAFF -> "tertiary"
        }
    }

    /**
     * 检查用户是否可以查看特定门店的数据
     */
    fun canAccessStore(userRoles: List<String>, userStoreIds: List<Int>, targetStoreId: Int): Boolean {
        // 管理员可以访问所有门店
        if (isAdmin(userRoles)) {
            return true
        }
        
        // 其他角色只能访问自己所属的门店
        return userStoreIds.contains(targetStoreId)
    }

    /**
     * 获取用户可以访问的门店ID列表
     */
    fun getAccessibleStoreIds(userRoles: List<String>, userStoreIds: List<Int>): List<Int> {
        return if (isAdmin(userRoles)) {
            // 管理员可以访问所有门店，返回空列表表示无限制
            emptyList()
        } else {
            // 其他角色只能访问自己所属的门店
            userStoreIds
        }
    }
}

/**
 * 权限枚举
 */
enum class Permission {
    VIEW_ALL_STORES,                // 查看所有门店
    VIEW_FINANCIAL_DETAILS,         // 查看财务详情
    VIEW_STORE_COMPARISON,          // 查看门店对比
    ACCESS_ADVANCED_STATISTICS,     // 访问高级统计
    MANAGE_USERS,                   // 管理用户
    MANAGE_STORES,                  // 管理门店
    CREATE_INVOICE,                 // 创建账单
    RECORD_PAYMENT,                 // 记录还款
    ADD_CUSTOMER,                   // 添加客户
    VIEW_REPORTS,                   // 查看报表
    // 管理中心相关权限
    ACCESS_MANAGEMENT_CENTER,       // 访问管理中心
    MANAGE_SYSTEM,                  // 系统管理
    MANAGE_CUSTOMERS,               // 客户管理
    MANAGE_BILLS,                   // 管理账单
    MANAGE_PAYMENTS,                // 管理还款
    VIEW_ADVANCED_REPORTS           // 查看高级报表
}
