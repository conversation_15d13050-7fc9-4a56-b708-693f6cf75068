package com.example.gxzhaiwu.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.gxzhaiwu.data.repository.AuthRepository
import com.example.gxzhaiwu.ui.auth.login.LoginScreen
import com.example.gxzhaiwu.ui.auth.register.RegisterScreen
import com.example.gxzhaiwu.ui.dashboard.DashboardScreen
import com.example.gxzhaiwu.ui.home.HomeScreen
import com.example.gxzhaiwu.ui.management.ManagementCenterScreen
import com.example.gxzhaiwu.ui.usermanagement.UserManagementScreen
import com.example.gxzhaiwu.ui.bills.BillManagementScreen
import com.example.gxzhaiwu.ui.payments.PaymentManagementScreen

@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController(),
    authRepository: AuthRepository = hiltViewModel<AuthNavigationViewModel>().authRepository
) {
    val isLoggedIn by authRepository.isLoggedIn().collectAsState(initial = false)
    
    val startDestination = if (isLoggedIn) {
        Screen.MainContainer.route
    } else {
        Screen.Login.route
    }

    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // 认证相关页面
        composable(Screen.Login.route) {
            LoginScreen(
                onLoginSuccess = {
                    navController.navigate(Screen.MainContainer.route) {
                        popUpTo(Screen.Login.route) { inclusive = true }
                    }
                },
                onNavigateToRegister = {
                    navController.navigate(Screen.Register.route)
                }
            )
        }
        
        composable(Screen.Register.route) {
            RegisterScreen(
                onRegisterSuccess = {
                    navController.navigate(Screen.MainContainer.route) {
                        popUpTo(0) { inclusive = true }
                    }
                },
                onNavigateToLogin = {
                    navController.popBackStack()
                }
            )
        }

        // 主容器页面（底部导航）
        composable(Screen.MainContainer.route) {
            MainContainerScreen()
        }

        // 仪表盘页面（底部导航中的一个标签）
        composable(Screen.MainDashboard.route) {
            DashboardScreen(
                onNavigateToScreen = { route ->
                    when (route) {
                        "login" -> {
                            navController.navigate(Screen.Login.route) {
                                popUpTo(0) { inclusive = true }
                            }
                        }
                        "customers" -> {
                            // TODO: 导航到客户列表页面
                            // navController.navigate(Screen.CustomerList.route)
                        }
                        "invoices" -> {
                            // TODO: 导航到账单列表页面
                            // navController.navigate(Screen.InvoiceList.route)
                        }
                        "payments" -> {
                            // TODO: 导航到还款记录页面
                            // navController.navigate(Screen.PaymentList.route)
                        }
                        "stores" -> {
                            // TODO: 导航到门店管理页面
                            // navController.navigate(Screen.StoreList.route)
                        }
                        "create_invoice" -> {
                            // TODO: 导航到创建账单页面
                            // navController.navigate(Screen.CreateInvoice.route)
                        }
                        "record_payment" -> {
                            // TODO: 导航到记录还款页面
                            // navController.navigate(Screen.RecordPayment.route)
                        }
                        "add_customer" -> {
                            // TODO: 导航到添加客户页面
                            // navController.navigate(Screen.AddCustomer.route)
                        }
                        "reports" -> {
                            // TODO: 导航到报表页面
                            // navController.navigate(Screen.Reports.route)
                        }
                        "user_management" -> {
                            navController.navigate(Screen.UserManagement.route)
                        }
                        "management_center" -> {
                            navController.navigate(Screen.ManagementCenter.route)
                        }
                        else -> {
                            // 未知路由，可以记录日志或显示错误
                        }
                    }
                }
            )
        }

        // 账单管理页面（底部导航中的一个标签）
        composable(Screen.MainBills.route) {
            BillManagementScreen(
                onNavigateToScreen = { route ->
                    // TODO: 处理账单管理页面的导航
                }
            )
        }

        // 还款管理页面（底部导航中的一个标签）
        composable(Screen.MainPayments.route) {
            PaymentManagementScreen(
                onNavigateToScreen = { route ->
                    // TODO: 处理还款管理页面的导航
                }
            )
        }

        // 管理中心页面（底部导航中的一个标签）
        composable(Screen.MainManagement.route) {
            ManagementCenterScreen(
                currentUserRoles = authRepository.getCurrentUserRoles(),
                onNavigateBack = {
                    // 在底部导航中不需要返回按钮
                },
                onNavigateToModule = { route ->
                    when (route) {
                        "user_management" -> {
                            navController.navigate(Screen.UserManagement.route)
                        }
                        "store_management" -> {
                            navController.navigate(Screen.StoreManagement.route)
                        }
                        "system_management" -> {
                            navController.navigate(Screen.SystemManagement.route)
                        }
                        "customer_management" -> {
                            navController.navigate(Screen.CustomerManagement.route)
                        }
                        else -> {
                            // 未知模块路由
                        }
                    }
                }
            )
        }

        // 主页面（保留用于其他用途）
        composable(Screen.Home.route) {
            HomeScreen(
                onNavigateToLogin = {
                    navController.navigate(Screen.Login.route) {
                        popUpTo(0) { inclusive = true }
                    }
                }
            )
        }

        // 管理中心页面（管理员和店长）
        composable(Screen.ManagementCenter.route) {
            ManagementCenterScreen(
                currentUserRoles = authRepository.getCurrentUserRoles(),
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToModule = { route ->
                    when (route) {
                        "user_management" -> {
                            navController.navigate(Screen.UserManagement.route)
                        }
                        // TODO: 添加其他管理模块的导航
                        else -> {
                            // 未知模块路由
                        }
                    }
                }
            )
        }

        // 用户管理页面（仅管理员）
        composable(Screen.UserManagement.route) {
            UserManagementScreen(
                currentUserRoles = authRepository.getCurrentUserRoles(),
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        // TODO: 添加其他页面的导航配置
        // composable(Screen.CustomerList.route) { ... }
        // composable(Screen.InvoiceList.route) { ... }
        // composable(Screen.PaymentList.route) { ... }
        // composable(Screen.StoreList.route) { ... }
        // composable(Screen.CreateInvoice.route) { ... }
        // composable(Screen.RecordPayment.route) { ... }
        // composable(Screen.AddCustomer.route) { ... }
        // composable(Screen.Reports.route) { ... }
    }
}
