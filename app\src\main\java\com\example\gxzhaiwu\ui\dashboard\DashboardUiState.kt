package com.example.gxzhaiwu.ui.dashboard

import com.example.gxzhaiwu.data.model.DashboardOverview
import com.example.gxzhaiwu.data.model.DashboardPermissions
import com.example.gxzhaiwu.data.model.DashboardStatistics
import com.example.gxzhaiwu.data.model.QuickActionData
import com.example.gxzhaiwu.data.model.StatisticsCardData

/**
 * 仪表盘UI状态数据类
 * 管理仪表盘页面的所有状态信息
 */
data class DashboardUiState(
    // 加载状态
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    
    // 数据状态
    val overview: DashboardOverview? = null,
    val statistics: DashboardStatistics? = null,
    
    // 错误状态
    val errorMessage: String? = null,
    val hasError: Boolean = false,
    
    // 用户权限相关
    val userRoles: List<String> = emptyList(),
    val permissions: DashboardPermissions = DashboardPermissions(
        canViewAllStores = false,
        canViewFinancialDetails = false,
        canViewStoreComparison = false,
        canAccessAdvancedStatistics = false
    ),
    
    // UI数据
    val statisticsCards: List<StatisticsCardData> = emptyList(),
    val quickActions: List<QuickActionData> = emptyList(),
    
    // 筛选和配置
    val selectedDateRange: DateRange? = null,
    val showAdvancedStatistics: Boolean = false,
    
    // 网络状态
    val isNetworkAvailable: Boolean = true,
    
    // 最后更新时间
    val lastUpdated: Long? = null
) {
    /**
     * 是否有数据可显示
     */
    val hasData: Boolean
        get() = overview != null

    /**
     * 是否显示空状态
     */
    val showEmptyState: Boolean
        get() = !isLoading && !hasData && !hasError

    /**
     * 是否可以刷新
     */
    val canRefresh: Boolean
        get() = !isLoading && !isRefreshing

    /**
     * 获取主要错误信息
     */
    val primaryErrorMessage: String?
        get() = when {
            !isNetworkAvailable -> "网络连接不可用"
            hasError -> errorMessage
            else -> null
        }
}

/**
 * 日期范围数据类
 */
data class DateRange(
    val startDate: String,
    val endDate: String,
    val displayName: String
) {
    companion object {
        /**
         * 预定义的日期范围选项
         */
        fun getPresetRanges(): List<DateRange> {
            val today = java.time.LocalDate.now()
            return listOf(
                DateRange(
                    startDate = today.minusDays(7).toString(),
                    endDate = today.toString(),
                    displayName = "最近7天"
                ),
                DateRange(
                    startDate = today.minusDays(30).toString(),
                    endDate = today.toString(),
                    displayName = "最近30天"
                ),
                DateRange(
                    startDate = today.minusDays(90).toString(),
                    endDate = today.toString(),
                    displayName = "最近3个月"
                ),
                DateRange(
                    startDate = today.withDayOfYear(1).toString(),
                    endDate = today.toString(),
                    displayName = "今年至今"
                )
            )
        }
    }
}

/**
 * 仪表盘事件类型
 */
sealed class DashboardEvent {
    object LoadData : DashboardEvent()
    object RefreshData : DashboardEvent()
    object ClearError : DashboardEvent()
    data class SelectDateRange(val dateRange: DateRange?) : DashboardEvent()
    data class ToggleAdvancedStatistics(val show: Boolean) : DashboardEvent()
    data class QuickActionClicked(val action: QuickActionData) : DashboardEvent()
    data class StatisticsCardClicked(val card: StatisticsCardData) : DashboardEvent()
}

/**
 * 仪表盘副作用类型
 */
sealed class DashboardSideEffect {
    data class ShowError(val message: String) : DashboardSideEffect()
    data class NavigateToScreen(val route: String) : DashboardSideEffect()
    data class ShowSnackbar(val message: String) : DashboardSideEffect()
    object NavigateToLogin : DashboardSideEffect()
}
