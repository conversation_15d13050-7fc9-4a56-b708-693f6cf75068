package com.example.gxzhaiwu.data.repository;

/**
 * 用户管理Repository实现类
 * 处理用户管理相关的数据操作和缓存管理
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0012\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\u0013\u001a\u00020\u0014H\u0096@\u00a2\u0006\u0002\u0010\u0015J\u000e\u0010\u0016\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0002\u0010\u0015J\u0014\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\t0\u0019H\u0016J\"\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u001bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001c\u0010\u0015J\"\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\t0\u001bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001e\u0010\u0015J$\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u000e0\u001b2\u0006\u0010 \u001a\u00020!H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010#J@\u0010$\u001a\b\u0012\u0004\u0012\u00020%0\u001b2\b\u0010&\u001a\u0004\u0018\u00010\u00172\b\u0010\'\u001a\u0004\u0018\u00010\u00172\u0006\u0010(\u001a\u00020!2\u0006\u0010)\u001a\u00020!H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b*\u0010+J\u001c\u0010,\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\t0\u00192\u0006\u0010-\u001a\u00020\u0017H\u0016J\u0010\u0010.\u001a\u00020\u00142\u0006\u0010/\u001a\u00020\u000eH\u0002J2\u00100\u001a\b\u0012\u0004\u0012\u00020\u000e0\u001b2\u0006\u0010 \u001a\u00020!2\f\u00101\u001a\b\u0012\u0004\u0012\u00020!0\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b2\u00103J2\u00104\u001a\b\u0012\u0004\u0012\u00020\u000e0\u001b2\u0006\u0010 \u001a\u00020!2\f\u00105\u001a\b\u0012\u0004\u0012\u00020!0\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b6\u00103R\u001a\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\t0\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\t0\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00067"}, d2 = {"Lcom/example/gxzhaiwu/data/repository/UserManagementRepositoryImpl;", "Lcom/example/gxzhaiwu/data/repository/UserManagementRepository;", "api", "Lcom/example/gxzhaiwu/data/api/UserManagementApi;", "userPreferences", "Lcom/example/gxzhaiwu/data/local/UserPreferences;", "(Lcom/example/gxzhaiwu/data/api/UserManagementApi;Lcom/example/gxzhaiwu/data/local/UserPreferences;)V", "_cachedRoles", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/example/gxzhaiwu/data/model/Role;", "_cachedStores", "Lcom/example/gxzhaiwu/data/api/Store;", "_cachedUsers", "Lcom/example/gxzhaiwu/data/model/UserDetail;", "cachedRoles", "Lkotlinx/coroutines/flow/StateFlow;", "cachedStores", "cachedUsers", "clearCache", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAuthHeader", "", "getCachedUsers", "Lkotlinx/coroutines/flow/Flow;", "getRoles", "Lkotlin/Result;", "getRoles-IoAF18A", "getStores", "getStores-IoAF18A", "getUserDetail", "userId", "", "getUserDetail-gIAlu-s", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUsers", "Lcom/example/gxzhaiwu/data/model/PaginatedUserData;", "search", "role", "page", "perPage", "getUsers-yxL6bBk", "(Ljava/lang/String;Ljava/lang/String;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchUsers", "query", "updateUserInCache", "updatedUser", "updateUserRoles", "roleIds", "updateUserRoles-0E7RQCE", "(ILjava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserStores", "storeIds", "updateUserStores-0E7RQCE", "app_debug"})
public final class UserManagementRepositoryImpl implements com.example.gxzhaiwu.data.repository.UserManagementRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.api.UserManagementApi api = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.local.UserPreferences userPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.gxzhaiwu.data.model.UserDetail>> _cachedUsers = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.gxzhaiwu.data.model.UserDetail>> cachedUsers = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.gxzhaiwu.data.model.Role>> _cachedRoles = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.gxzhaiwu.data.model.Role>> cachedRoles = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.gxzhaiwu.data.api.Store>> _cachedStores = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.gxzhaiwu.data.api.Store>> cachedStores = null;
    
    @javax.inject.Inject()
    public UserManagementRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.api.UserManagementApi api, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.local.UserPreferences userPreferences) {
        super();
    }
    
    /**
     * 获取认证头
     */
    private final java.lang.Object getAuthHeader(kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * 搜索用户（本地缓存搜索）
     */
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.gxzhaiwu.data.model.UserDetail>> searchUsers(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
        return null;
    }
    
    /**
     * 获取缓存的用户列表
     */
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.gxzhaiwu.data.model.UserDetail>> getCachedUsers() {
        return null;
    }
    
    /**
     * 清除缓存
     */
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object clearCache(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新缓存中的用户信息
     */
    private final void updateUserInCache(com.example.gxzhaiwu.data.model.UserDetail updatedUser) {
    }
}