package com.example.gxzhaiwu.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\bZ\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0013\u0010\u001f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b \u0010\u0003\"\u0013\u0010!\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\"\u0010\u0003\"\u0013\u0010#\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b$\u0010\u0003\"\u0013\u0010%\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b&\u0010\u0003\"\u0013\u0010\'\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b(\u0010\u0003\"\u0013\u0010)\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b*\u0010\u0003\"\u0013\u0010+\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b,\u0010\u0003\"\u0013\u0010-\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b.\u0010\u0003\"\u0013\u0010/\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b0\u0010\u0003\"\u0013\u00101\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b2\u0010\u0003\"\u0013\u00103\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b4\u0010\u0003\"\u0013\u00105\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b6\u0010\u0003\"\u0013\u00107\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b8\u0010\u0003\"\u0013\u00109\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b:\u0010\u0003\"\u0013\u0010;\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b<\u0010\u0003\"\u0013\u0010=\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b>\u0010\u0003\"\u0013\u0010?\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b@\u0010\u0003\"\u0013\u0010A\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bB\u0010\u0003\"\u0013\u0010C\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bD\u0010\u0003\"\u0013\u0010E\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bF\u0010\u0003\"\u0013\u0010G\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bH\u0010\u0003\"\u0013\u0010I\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bJ\u0010\u0003\"\u0013\u0010K\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bL\u0010\u0003\"\u0013\u0010M\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bN\u0010\u0003\"\u0013\u0010O\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bP\u0010\u0003\"\u0013\u0010Q\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bR\u0010\u0003\"\u0013\u0010S\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bT\u0010\u0003\"\u0013\u0010U\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bV\u0010\u0003\"\u0013\u0010W\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bX\u0010\u0003\"\u0013\u0010Y\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bZ\u0010\u0003\u00a8\u0006["}, d2 = {"Background", "Landroidx/compose/ui/graphics/Color;", "getBackground", "()J", "J", "DarkBackground", "getDarkBackground", "DarkError", "getDarkError", "DarkInfo", "getDarkInfo", "DarkOnBackground", "getDarkOnBackground", "DarkOnError", "getDarkOnError", "DarkOnInfo", "getDarkOnInfo", "DarkOnPrimary", "getDarkOnPrimary", "DarkOnSecondary", "getDarkOnSecondary", "DarkOnSuccess", "getDarkOnSuccess", "DarkOnSurface", "getDarkOnSurface", "DarkOnSurfaceVariant", "getDarkOnSurfaceVariant", "DarkOnWarning", "getDarkOnWarning", "DarkOutline", "getDarkOutline", "DarkOutlineVariant", "getDarkOutlineVariant", "DarkPrimary", "getDarkPrimary", "DarkPrimaryVariant", "getDarkPrimaryVariant", "DarkSecondary", "getDarkSecondary", "DarkSecondaryVariant", "getDarkSecondaryVariant", "DarkSuccess", "getDarkSuccess", "DarkSurface", "getDarkSurface", "DarkSurfaceVariant", "getDarkSurfaceVariant", "DarkWarning", "getDarkWarning", "Error", "getError", "Info", "getInfo", "OnBackground", "getOnBackground", "OnError", "getOnError", "OnInfo", "getOnInfo", "OnPrimary", "getOnPrimary", "OnSecondary", "getOnSecondary", "OnSuccess", "getOnSuccess", "OnSurface", "getOnSurface", "OnSurfaceVariant", "getOnSurfaceVariant", "OnWarning", "getOnWarning", "Outline", "getOutline", "OutlineVariant", "getOutlineVariant", "Primary", "getPrimary", "PrimaryVariant", "getPrimaryVariant", "Secondary", "getSecondary", "SecondaryVariant", "getSecondaryVariant", "Success", "getSuccess", "Surface", "getSurface", "SurfaceVariant", "getSurfaceVariant", "Warning", "getWarning", "app_debug"})
public final class ColorKt {
    private static final long Primary = 0L;
    private static final long PrimaryVariant = 0L;
    private static final long OnPrimary = 0L;
    private static final long Secondary = 0L;
    private static final long SecondaryVariant = 0L;
    private static final long OnSecondary = 0L;
    private static final long Background = 0L;
    private static final long OnBackground = 0L;
    private static final long Surface = 0L;
    private static final long OnSurface = 0L;
    private static final long SurfaceVariant = 0L;
    private static final long OnSurfaceVariant = 0L;
    private static final long Error = 0L;
    private static final long OnError = 0L;
    private static final long Success = 0L;
    private static final long OnSuccess = 0L;
    private static final long Warning = 0L;
    private static final long OnWarning = 0L;
    private static final long Info = 0L;
    private static final long OnInfo = 0L;
    private static final long Outline = 0L;
    private static final long OutlineVariant = 0L;
    private static final long DarkPrimary = 0L;
    private static final long DarkPrimaryVariant = 0L;
    private static final long DarkOnPrimary = 0L;
    private static final long DarkSecondary = 0L;
    private static final long DarkSecondaryVariant = 0L;
    private static final long DarkOnSecondary = 0L;
    private static final long DarkBackground = 0L;
    private static final long DarkOnBackground = 0L;
    private static final long DarkSurface = 0L;
    private static final long DarkOnSurface = 0L;
    private static final long DarkSurfaceVariant = 0L;
    private static final long DarkOnSurfaceVariant = 0L;
    private static final long DarkError = 0L;
    private static final long DarkOnError = 0L;
    private static final long DarkSuccess = 0L;
    private static final long DarkOnSuccess = 0L;
    private static final long DarkWarning = 0L;
    private static final long DarkOnWarning = 0L;
    private static final long DarkInfo = 0L;
    private static final long DarkOnInfo = 0L;
    private static final long DarkOutline = 0L;
    private static final long DarkOutlineVariant = 0L;
    
    public static final long getPrimary() {
        return 0L;
    }
    
    public static final long getPrimaryVariant() {
        return 0L;
    }
    
    public static final long getOnPrimary() {
        return 0L;
    }
    
    public static final long getSecondary() {
        return 0L;
    }
    
    public static final long getSecondaryVariant() {
        return 0L;
    }
    
    public static final long getOnSecondary() {
        return 0L;
    }
    
    public static final long getBackground() {
        return 0L;
    }
    
    public static final long getOnBackground() {
        return 0L;
    }
    
    public static final long getSurface() {
        return 0L;
    }
    
    public static final long getOnSurface() {
        return 0L;
    }
    
    public static final long getSurfaceVariant() {
        return 0L;
    }
    
    public static final long getOnSurfaceVariant() {
        return 0L;
    }
    
    public static final long getError() {
        return 0L;
    }
    
    public static final long getOnError() {
        return 0L;
    }
    
    public static final long getSuccess() {
        return 0L;
    }
    
    public static final long getOnSuccess() {
        return 0L;
    }
    
    public static final long getWarning() {
        return 0L;
    }
    
    public static final long getOnWarning() {
        return 0L;
    }
    
    public static final long getInfo() {
        return 0L;
    }
    
    public static final long getOnInfo() {
        return 0L;
    }
    
    public static final long getOutline() {
        return 0L;
    }
    
    public static final long getOutlineVariant() {
        return 0L;
    }
    
    public static final long getDarkPrimary() {
        return 0L;
    }
    
    public static final long getDarkPrimaryVariant() {
        return 0L;
    }
    
    public static final long getDarkOnPrimary() {
        return 0L;
    }
    
    public static final long getDarkSecondary() {
        return 0L;
    }
    
    public static final long getDarkSecondaryVariant() {
        return 0L;
    }
    
    public static final long getDarkOnSecondary() {
        return 0L;
    }
    
    public static final long getDarkBackground() {
        return 0L;
    }
    
    public static final long getDarkOnBackground() {
        return 0L;
    }
    
    public static final long getDarkSurface() {
        return 0L;
    }
    
    public static final long getDarkOnSurface() {
        return 0L;
    }
    
    public static final long getDarkSurfaceVariant() {
        return 0L;
    }
    
    public static final long getDarkOnSurfaceVariant() {
        return 0L;
    }
    
    public static final long getDarkError() {
        return 0L;
    }
    
    public static final long getDarkOnError() {
        return 0L;
    }
    
    public static final long getDarkSuccess() {
        return 0L;
    }
    
    public static final long getDarkOnSuccess() {
        return 0L;
    }
    
    public static final long getDarkWarning() {
        return 0L;
    }
    
    public static final long getDarkOnWarning() {
        return 0L;
    }
    
    public static final long getDarkInfo() {
        return 0L;
    }
    
    public static final long getDarkOnInfo() {
        return 0L;
    }
    
    public static final long getDarkOutline() {
        return 0L;
    }
    
    public static final long getDarkOutlineVariant() {
        return 0L;
    }
}