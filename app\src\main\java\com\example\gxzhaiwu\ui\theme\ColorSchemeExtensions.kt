package com.example.gxzhaiwu.ui.theme

import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.luminance

/**
 * 扩展ColorScheme以支持自定义语义化颜色
 * 提供Success、Warning、Info等额外的颜色角色
 */

/**
 * 判断当前是否为浅色主题
 */
private fun ColorScheme.isLight(): <PERSON>olean {
    return this.background.luminance() > 0.5
}

/**
 * 成功状态颜色
 */
val ColorScheme.success: Color
    @Composable
    get() = if (isLight()) Success else DarkSuccess

val ColorScheme.onSuccess: Color
    @Composable
    get() = if (isLight()) OnSuccess else DarkOnSuccess

/**
 * 警告状态颜色
 */
val ColorScheme.warning: Color
    @Composable
    get() = if (isLight()) Warning else DarkWarning

val ColorScheme.onWarning: Color
    @Composable
    get() = if (isLight()) OnWarning else DarkOnWarning

/**
 * 信息状态颜色
 */
val ColorScheme.info: Color
    @Composable
    get() = if (isLight()) Info else DarkInfo

val ColorScheme.onInfo: Color
    @Composable
    get() = if (isLight()) OnInfo else DarkOnInfo

/**
 * 扩展的表面颜色变体
 */
val ColorScheme.surfaceVariant: Color
    @Composable
    get() = if (isLight()) SurfaceVariant else DarkSurfaceVariant

val ColorScheme.onSurfaceVariant: Color
    @Composable
    get() = if (isLight()) OnSurfaceVariant else DarkOnSurfaceVariant

/**
 * 轮廓颜色
 */
val ColorScheme.outline: Color
    @Composable
    get() = if (isLight()) Outline else DarkOutline

val ColorScheme.outlineVariant: Color
    @Composable
    get() = if (isLight()) OutlineVariant else DarkOutlineVariant

/**
 * 便捷的颜色访问方法
 */
object ExtendedColors {
    
    @Composable
    fun successContainer(colorScheme: ColorScheme): Color {
        return if (colorScheme.isLight()) {
            Color(0xFFE8F5E8)           // 浅绿容器，保持可识别性
        } else {
            Color(0xFF2E2E2E)           // 深灰容器，符合黑白灰主题
        }
    }

    @Composable
    fun onSuccessContainer(colorScheme: ColorScheme): Color {
        return if (colorScheme.isLight()) {
            Color(0xFF2E7D32)           // 深绿文字，保持可识别性
        } else {
            Color(0xFF66BB6A)           // 浅绿文字，保持可识别性
        }
    }
    
    @Composable
    fun warningContainer(colorScheme: ColorScheme): Color {
        return if (colorScheme.isLight()) {
            Color(0xFFFFF3E0)           // 浅橙容器，保持可识别性
        } else {
            Color(0xFF2E2E2E)           // 深灰容器，符合黑白灰主题
        }
    }

    @Composable
    fun onWarningContainer(colorScheme: ColorScheme): Color {
        return if (colorScheme.isLight()) {
            Color(0xFFF57C00)           // 深橙文字，保持可识别性
        } else {
            Color(0xFFFFB74D)           // 浅橙文字，保持可识别性
        }
    }
    
    @Composable
    fun infoContainer(colorScheme: ColorScheme): Color {
        return if (colorScheme.isLight()) {
            Color(0xFFF5F5F5)           // 浅灰容器，符合黑白灰主题
        } else {
            Color(0xFF2E2E2E)           // 深灰容器，符合黑白灰主题
        }
    }

    @Composable
    fun onInfoContainer(colorScheme: ColorScheme): Color {
        return if (colorScheme.isLight()) {
            Color(0xFF546E7A)           // 蓝灰文字，符合黑白灰主题
        } else {
            Color(0xFF90A4AE)           // 浅蓝灰文字，符合黑白灰主题
        }
    }
}

/**
 * 获取适合当前主题的卡片elevation颜色
 * 黑白灰主题优化版本
 */
@Composable
fun ColorScheme.cardElevationColor(elevation: Int): Color {
    return if (isLight()) {
        // 浅色模式：使用白色和浅灰色层次
        when (elevation) {
            0 -> Color(0xFFFFFFFF)      // 纯白
            1 -> Color(0xFFFAFAFA)      // 极浅灰
            2 -> Color(0xFFF5F5F5)      // 浅灰
            3 -> Color(0xFFF0F0F0)      // 稍深浅灰
            else -> Color(0xFFEEEEEE)   // 更深浅灰
        }
    } else {
        // 深色模式：使用深灰色层次，避免纯黑
        when (elevation) {
            0 -> Color(0xFF1E1E1E)      // 基础深灰
            1 -> Color(0xFF242424)      // 稍浅深灰
            2 -> Color(0xFF2A2A2A)      // 中深灰
            3 -> Color(0xFF303030)      // 稍浅中灰
            4 -> Color(0xFF363636)      // 浅中灰
            5 -> Color(0xFF3C3C3C)      // 更浅中灰
            else -> Color(0xFF424242)   // 最浅深灰
        }
    }
}

/**
 * 获取适合当前主题的分割线颜色
 */
@Composable
fun ColorScheme.dividerColor(): Color {
    return if (isLight()) {
        outline.copy(alpha = 0.12f)
    } else {
        outline.copy(alpha = 0.2f)
    }
}

/**
 * 获取适合当前主题的禁用状态颜色
 */
@Composable
fun ColorScheme.disabledColor(): Color {
    return onSurface.copy(alpha = 0.38f)
}

@Composable
fun ColorScheme.disabledContainerColor(): Color {
    return onSurface.copy(alpha = 0.12f)
}
