package com.example.gxzhaiwu.ui.management.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000(\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a(\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\b\u0010\b\u001a\u00020\u0001H\u0003\u001a\b\u0010\t\u001a\u00020\u0001H\u0003\u001a\b\u0010\n\u001a\u00020\u0001H\u0003\u001a\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002\u00a8\u0006\u000f"}, d2 = {"ModuleCard", "", "module", "Lcom/example/gxzhaiwu/data/model/ManagementModule;", "onClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "ModuleCardDisabledPreview", "ModuleCardEnabledPreview", "ModuleGridPreview", "getModuleIcon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "iconName", "", "app_debug"})
public final class ModuleCardKt {
    
    /**
     * 管理模块卡片组件
     * 显示单个管理模块的信息和入口
     */
    @androidx.compose.runtime.Composable()
    public static final void ModuleCard(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.ManagementModule module, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 根据图标名称获取对应的图标
     */
    private static final androidx.compose.ui.graphics.vector.ImageVector getModuleIcon(java.lang.String iconName) {
        return null;
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u7ba1\u7406\u6a21\u5757\u5361\u7247 - \u542f\u7528\u72b6\u6001")
    @androidx.compose.runtime.Composable()
    private static final void ModuleCardEnabledPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u7ba1\u7406\u6a21\u5757\u5361\u7247 - \u7981\u7528\u72b6\u6001")
    @androidx.compose.runtime.Composable()
    private static final void ModuleCardDisabledPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u7ba1\u7406\u6a21\u5757\u7f51\u683c\u9884\u89c8")
    @androidx.compose.runtime.Composable()
    private static final void ModuleGridPreview() {
    }
}