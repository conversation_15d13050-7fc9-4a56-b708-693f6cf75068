package com.example.gxzhaiwu.ui.payments;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class PaymentManagementViewModel_Factory implements Factory<PaymentManagementViewModel> {
  @Override
  public PaymentManagementViewModel get() {
    return newInstance();
  }

  public static PaymentManagementViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static PaymentManagementViewModel newInstance() {
    return new PaymentManagementViewModel();
  }

  private static final class InstanceHolder {
    private static final PaymentManagementViewModel_Factory INSTANCE = new PaymentManagementViewModel_Factory();
  }
}
