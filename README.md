# 债务管理系统 Android 客户端

## 项目概述

这是一个基于 Android 的债务管理系统客户端应用，使用现代 Android 开发技术栈构建，包括 Jetpack Compose、MVVM 架构、Hilt 依赖注入等。

## 技术栈

### 核心技术
- **Kotlin** - 主要编程语言
- **Jetpack Compose** - 现代 UI 工具包
- **Material Design 3** - UI 设计系统
- **MVVM + Repository Pattern** - 架构模式

### 网络和数据
- **Retrofit** - HTTP 客户端
- **OkHttp** - 网络请求库
- **Kotlinx Serialization** - JSON 序列化
- **DataStore** - 本地数据存储

### 依赖注入和导航
- **Hilt** - 依赖注入框架
- **Navigation Compose** - 导航组件

### 异步处理
- **Kotlin Coroutines** - 异步编程
- **StateFlow** - 状态管理

## 项目结构

```
app/src/main/java/com/example/gxzhaiwu/
├── data/                          # 数据层
│   ├── api/                       # API 接口定义
│   │   ├── ApiResponse.kt         # 通用 API 响应模型
│   │   ├── AuthApi.kt             # 认证 API 接口
│   │   └── NetworkModule.kt       # 网络模块配置
│   ├── local/                     # 本地存储
│   │   ├── UserPreferences.kt     # 用户偏好设置
│   │   └── PreferencesModule.kt   # 本地存储模块
│   ├── model/                     # 数据模型
│   │   └── User.kt                # 用户相关数据模型
│   └── repository/                # 仓库层
│       ├── AuthRepository.kt      # 认证仓库接口
│       ├── AuthRepositoryImpl.kt  # 认证仓库实现
│       └── RepositoryModule.kt    # 仓库模块
├── ui/                            # UI 层
│   ├── auth/                      # 认证相关 UI
│   │   ├── components/            # 认证组件
│   │   │   ├── AuthButton.kt      # 认证按钮组件
│   │   │   └── AuthTextField.kt   # 认证输入框组件
│   │   └── login/                 # 登录页面
│   │       ├── LoginScreen.kt     # 登录界面
│   │       ├── LoginUiState.kt    # 登录 UI 状态
│   │       └── LoginViewModel.kt  # 登录视图模型
│   ├── components/                # 通用 UI 组件
│   │   ├── ErrorDialog.kt         # 错误对话框
│   │   └── LoadingDialog.kt       # 加载对话框
│   ├── home/                      # 主页
│   │   ├── HomeScreen.kt          # 主页界面
│   │   └── HomeViewModel.kt       # 主页视图模型
│   ├── navigation/                # 导航
│   │   ├── AppNavigation.kt       # 应用导航配置
│   │   ├── AuthNavigationViewModel.kt # 导航视图模型
│   │   └── Screen.kt              # 屏幕路由定义
│   └── theme/                     # 主题配置
│       ├── Color.kt               # 颜色定义
│       ├── Theme.kt               # 主题配置
│       └── Type.kt                # 字体配置
├── utils/                         # 工具类
│   ├── Constants.kt               # 常量定义
│   ├── NetworkUtils.kt            # 网络工具
│   ├── SecurityUtils.kt           # 安全工具
│   ├── TestUtils.kt               # 测试工具
│   ├── UiUtils.kt                 # UI 工具
│   └── ValidationUtils.kt         # 验证工具
├── GxZhaiWuApplication.kt         # 应用程序类
└── MainActivity.kt                # 主活动
```

## 功能特性

### 已实现功能
- ✅ 用户登录
- ✅ 表单验证
- ✅ 错误处理
- ✅ 加载状态管理
- ✅ 本地数据存储
- ✅ 网络请求配置
- ✅ 导航系统
- ✅ 主题配置

### 待实现功能
- 🔄 用户注册
- 🔄 账单管理
- 🔄 客户管理
- 🔄 还款记录
- 🔄 门店管理
- 🔄 数据统计
- 🔄 设置页面

## API 集成

应用与后端 Laravel API 集成，API 基础地址：`https://gxzhaiwu.hrlni.cn/api/`

### 认证端点
- `POST /login` - 用户登录
- `POST /register` - 用户注册
- `POST /logout` - 用户登出
- `GET /user` - 获取当前用户信息

## 安全特性

- JWT Token 认证
- 输入验证和清理
- SQL 注入防护
- XSS 攻击防护
- HTTPS 通信
- 本地数据加密存储

## 测试

### 单元测试
- 验证工具类测试
- 安全工具类测试
- 数据模型测试

### UI 测试
- 登录界面测试
- 组件交互测试
- 导航测试

运行测试：
```bash
./gradlew test                    # 运行单元测试
./gradlew connectedAndroidTest    # 运行 UI 测试
```

## 构建和运行

### 环境要求
- Android Studio Arctic Fox 或更高版本
- JDK 11 或更高版本
- Android SDK API 24 或更高版本

### 构建步骤
1. 克隆项目到本地
2. 使用 Android Studio 打开项目
3. 等待 Gradle 同步完成
4. 运行应用

```bash
./gradlew assembleDebug           # 构建 Debug 版本
./gradlew assembleRelease         # 构建 Release 版本
```

## 配置

### 网络配置
在 `Constants.kt` 中配置 API 基础地址和超时设置：

```kotlin
const val BASE_URL = "https://gxzhaiwu.hrlni.cn/api/"
const val CONNECT_TIMEOUT = 30L
const val READ_TIMEOUT = 30L
const val WRITE_TIMEOUT = 30L
```

### 主题配置
在 `Color.kt` 和 `Theme.kt` 中自定义应用主题和颜色。

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>
