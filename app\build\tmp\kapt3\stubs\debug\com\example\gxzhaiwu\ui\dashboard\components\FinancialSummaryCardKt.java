package com.example.gxzhaiwu.ui.dashboard.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a<\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u000b\u0010\f\u001a,\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\t\u001a\u00020\n2\u0010\b\u0002\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0011H\u0007\u001a\b\u0010\u0012\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0013\u001a\u00020\u0001H\u0003\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0014"}, d2 = {"FinancialItem", "", "label", "", "value", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "color", "Landroidx/compose/ui/graphics/Color;", "modifier", "Landroidx/compose/ui/Modifier;", "FinancialItem-42QJj7c", "(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/graphics/vector/ImageVector;JLandroidx/compose/ui/Modifier;)V", "FinancialSummaryCard", "financialSummary", "Lcom/example/gxzhaiwu/data/model/FinancialSummary;", "onClick", "Lkotlin/Function0;", "FinancialSummaryCardDarkPreview", "FinancialSummaryCardPreview", "app_debug"})
public final class FinancialSummaryCardKt {
    
    /**
     * 财务汇总卡片组件
     * 专门用于显示财务相关的汇总数据
     */
    @androidx.compose.runtime.Composable()
    public static final void FinancialSummaryCard(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.FinancialSummary financialSummary, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u6d45\u8272\u6a21\u5f0f - \u8d22\u52a1\u6c47\u603b\u5361\u7247")
    @androidx.compose.runtime.Composable()
    private static final void FinancialSummaryCardPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u6df1\u8272\u6a21\u5f0f - \u8d22\u52a1\u6c47\u603b\u5361\u7247")
    @androidx.compose.runtime.Composable()
    private static final void FinancialSummaryCardDarkPreview() {
    }
}