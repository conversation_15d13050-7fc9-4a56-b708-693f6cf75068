package com.example.gxzhaiwu.ui.payments.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.ui.payments.*
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import java.math.BigDecimal

/**
 * 还款管理顶部栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PaymentManagementTopBar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    currentFilter: PaymentFilter,
    onFilterChange: (PaymentFilter) -> Unit,
    onClearFilters: () -> Unit,
    viewMode: PaymentViewMode,
    onViewModeChange: (PaymentViewMode) -> Unit,
    modifier: Modifier = Modifier
) {
    var showFilterDialog by remember { mutableStateOf(false) }
    var showViewModeMenu by remember { mutableStateOf(false) }

    TopAppBar(
        title = { Text("还款管理") },
        actions = {
            // 搜索按钮
            IconButton(onClick = { /* TODO: 展开搜索栏 */ }) {
                Icon(Icons.Default.Search, contentDescription = "搜索")
            }
            
            // 视图模式切换
            Box {
                IconButton(onClick = { showViewModeMenu = true }) {
                    Icon(
                        imageVector = when (viewMode) {
                            PaymentViewMode.LIST -> Icons.Default.ViewList
                            PaymentViewMode.SUMMARY -> Icons.Default.Analytics
                            PaymentViewMode.CALENDAR -> Icons.Default.CalendarMonth
                        },
                        contentDescription = "切换视图"
                    )
                }
                
                DropdownMenu(
                    expanded = showViewModeMenu,
                    onDismissRequest = { showViewModeMenu = false }
                ) {
                    PaymentViewMode.values().forEach { mode ->
                        DropdownMenuItem(
                            text = { Text(mode.displayName) },
                            onClick = {
                                showViewModeMenu = false
                                onViewModeChange(mode)
                            },
                            leadingIcon = {
                                Icon(
                                    imageVector = when (mode) {
                                        PaymentViewMode.LIST -> Icons.Default.ViewList
                                        PaymentViewMode.SUMMARY -> Icons.Default.Analytics
                                        PaymentViewMode.CALENDAR -> Icons.Default.CalendarMonth
                                    },
                                    contentDescription = null
                                )
                            }
                        )
                    }
                }
            }
            
            // 过滤按钮
            IconButton(onClick = { showFilterDialog = true }) {
                Badge(
                    containerColor = if (currentFilter.hasActiveFilter()) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.surfaceVariant
                    }
                ) {
                    if (currentFilter.hasActiveFilter()) {
                        Text(
                            text = currentFilter.getActiveFilterCount().toString(),
                            style = MaterialTheme.typography.labelSmall
                        )
                    }
                }
                Icon(Icons.Default.FilterList, contentDescription = "过滤")
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surface,
            titleContentColor = MaterialTheme.colorScheme.onSurface
        ),
        modifier = modifier
    )

    // 过滤对话框
    if (showFilterDialog) {
        PaymentFilterDialog(
            currentFilter = currentFilter,
            onFilterChange = onFilterChange,
            onClearFilters = onClearFilters,
            onDismiss = { showFilterDialog = false }
        )
    }
}

/**
 * 还款统计卡片
 */
@Composable
fun PaymentStatisticsCard(
    statistics: PaymentStatistics,
    modifier: Modifier = Modifier
) {
    Card(
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "还款统计",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    label = "总笔数",
                    value = statistics.totalPayments.toString(),
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                StatisticItem(
                    label = "总金额",
                    value = "¥${statistics.totalAmount}",
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                StatisticItem(
                    label = "已确认",
                    value = "¥${statistics.confirmedAmount}",
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                StatisticItem(
                    label = "待确认",
                    value = "¥${statistics.pendingAmount}",
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }

            // 确认率进度条
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "确认率",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "${(statistics.getConfirmationRate() * 100).toInt()}%",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            
            LinearProgressIndicator(
                progress = statistics.getConfirmationRate(),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 4.dp),
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                trackColor = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.3f)
            )

            // 今日收款
            if (statistics.todayAmount > BigDecimal.ZERO) {
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    AssistChip(
                        onClick = { },
                        label = {
                            Text("今日收款 ¥${statistics.todayAmount}")
                        },
                        leadingIcon = {
                            Icon(
                                Icons.Default.Today,
                                contentDescription = null
                            )
                        },
                        colors = AssistChipDefaults.assistChipColors(
                            containerColor = MaterialTheme.colorScheme.surface,
                            labelColor = MaterialTheme.colorScheme.onSurface
                        )
                    )
                }
            }
        }
    }
}

/**
 * 统计项目组件
 */
@Composable
private fun StatisticItem(
    label: String,
    value: String,
    color: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = color.copy(alpha = 0.8f)
        )
    }
}

/**
 * 还款空状态
 */
@Composable
fun PaymentEmptyState(
    onRecordPayment: () -> Unit,
    canRecordPayment: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Payment,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.outline,
            modifier = Modifier.size(64.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "暂无还款记录",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Text(
            text = "记录第一笔还款开始管理",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 8.dp)
        )
        
        if (canRecordPayment) {
            Spacer(modifier = Modifier.height(24.dp))
            
            Button(
                onClick = onRecordPayment
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("记录还款")
            }
        }
    }
}

/**
 * 还款搜索空状态
 */
@Composable
fun PaymentEmptySearchState(
    searchQuery: String,
    onClearSearch: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.SearchOff,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.outline,
            modifier = Modifier.size(64.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "未找到相关还款记录",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Text(
            text = "搜索\"$searchQuery\"无结果",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 8.dp)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        TextButton(onClick = onClearSearch) {
            Text("清除搜索")
        }
    }
}

/**
 * 还款过滤对话框
 */
@Composable
fun PaymentFilterDialog(
    currentFilter: PaymentFilter,
    onFilterChange: (PaymentFilter) -> Unit,
    onClearFilters: () -> Unit,
    onDismiss: () -> Unit
) {
    var tempFilter by remember { mutableStateOf(currentFilter) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("筛选还款记录") },
        text = {
            Column {
                // 状态过滤
                Text(
                    text = "还款状态",
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // TODO: 实现状态选择器
                Text(
                    text = "状态选择器待实现",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 支付方式过滤
                Text(
                    text = "支付方式",
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // TODO: 实现支付方式选择器
                Text(
                    text = "支付方式选择器待实现",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onFilterChange(tempFilter)
                    onDismiss()
                }
            ) {
                Text("应用")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 支付方式汇总卡片
 */
@Composable
fun PaymentMethodSummaryCard(
    payments: List<Payment>,
    modifier: Modifier = Modifier
) {
    val methodSummary = payments.groupBy { it.paymentMethod }
        .mapValues { (_, paymentList) -> 
            paymentList.sumOf { it.amount }
        }

    Card(
        shape = RoundedCornerShape(12.dp),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "支付方式分布",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            methodSummary.forEach { (method, amount) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = method.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "¥$amount",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * 还款状态汇总卡片
 */
@Composable
fun PaymentStatusSummaryCard(
    payments: List<Payment>,
    modifier: Modifier = Modifier
) {
    val statusSummary = payments.groupBy { it.status }
        .mapValues { (_, paymentList) -> 
            Pair(paymentList.size, paymentList.sumOf { it.amount })
        }

    Card(
        shape = RoundedCornerShape(12.dp),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "状态分布",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            statusSummary.forEach { (status, countAndAmount) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "${status.displayName} (${countAndAmount.first}笔)",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "¥${countAndAmount.second}",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

// 预览组件
@Preview(name = "还款统计卡片")
@Composable
private fun PaymentStatisticsCardPreview() {
    GxZhaiWuTheme {
        Surface {
            PaymentStatisticsCard(
                statistics = PaymentStatistics(
                    totalPayments = 15,
                    totalAmount = BigDecimal("25000.00"),
                    confirmedAmount = BigDecimal("20000.00"),
                    pendingAmount = BigDecimal("3000.00"),
                    refundedAmount = BigDecimal("2000.00"),
                    todayAmount = BigDecimal("5000.00"),
                    thisMonthAmount = BigDecimal("15000.00")
                ),
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}
