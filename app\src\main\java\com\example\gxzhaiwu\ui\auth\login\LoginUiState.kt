package com.example.gxzhaiwu.ui.auth.login

data class LoginUiState(
    val login: String = "",
    val password: String = "",
    val isLoading: Boolean = false,
    val isLoginSuccessful: Boolean = false,
    val errorMessage: String? = null,
    val loginError: String? = null,
    val passwordError: String? = null
) {
    val isFormValid: Boolean
        get() = login.isNotBlank() && 
                password.isNotBlank() && 
                loginError == null && 
                passwordError == null
}
