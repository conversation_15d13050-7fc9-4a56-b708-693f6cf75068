package com.example.gxzhaiwu.ui.auth.register

data class RegisterUiState(
    val name: String = "",
    val username: String = "",
    val email: String = "",
    val password: String = "",
    val passwordConfirmation: String = "",
    val isLoading: Boolean = false,
    val isRegisterSuccessful: Boolean = false,
    val errorMessage: String? = null,
    val nameError: String? = null,
    val usernameError: String? = null,
    val emailError: String? = null,
    val passwordError: String? = null,
    val passwordConfirmationError: String? = null
) {
    val isFormValid: Boolean
        get() = name.isNotBlank() && 
                username.isNotBlank() && 
                email.isNotBlank() && 
                password.isNotBlank() && 
                passwordConfirmation.isNotBlank() &&
                nameError == null &&
                usernameError == null &&
                emailError == null &&
                passwordError == null &&
                passwordConfirmationError == null
}
