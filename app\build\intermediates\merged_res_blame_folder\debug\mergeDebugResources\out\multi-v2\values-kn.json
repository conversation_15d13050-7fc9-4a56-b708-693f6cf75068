{"logs": [{"outputFile": "com.example.gxzhaiwu.app-mergeDebugResources-61:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\34672be5908381f92b2f1efab7609872\\transformed\\material3-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,425,548,647,745,860,1017,1147,1299,1385,1491,1587,1689,1805,1938,2049,2188,2323,2456,2634,2758,2876,2997,3124,3221,3318,3440,3578,3684,3793,3899,4038,4183,4293,4402,4478,4578,4678,4765,4854,4965,5045,5129,5229,5337,5437,5538,5625,5738,5840,5945,6066,6146,6256", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "174,298,420,543,642,740,855,1012,1142,1294,1380,1486,1582,1684,1800,1933,2044,2183,2318,2451,2629,2753,2871,2992,3119,3216,3313,3435,3573,3679,3788,3894,4033,4178,4288,4397,4473,4573,4673,4760,4849,4960,5040,5124,5224,5332,5432,5533,5620,5733,5835,5940,6061,6141,6251,6348"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1493,1617,1741,1863,1986,2085,2183,2298,2455,2585,2737,2823,2929,3025,3127,3243,3376,3487,3626,3761,3894,4072,4196,4314,4435,4562,4659,4756,4878,5016,5122,5231,5337,5476,5621,5731,5840,5916,6016,6116,6203,6292,6403,6483,6567,6667,6775,6875,6976,7063,7176,7278,7383,7504,7584,7694", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "1612,1736,1858,1981,2080,2178,2293,2450,2580,2732,2818,2924,3020,3122,3238,3371,3482,3621,3756,3889,4067,4191,4309,4430,4557,4654,4751,4873,5011,5117,5226,5332,5471,5616,5726,5835,5911,6011,6111,6198,6287,6398,6478,6562,6662,6770,6870,6971,7058,7171,7273,7378,7499,7579,7689,7786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca95cc56ae6c869d643262611d469661\\transformed\\core-1.16.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "2,3,4,5,6,7,8,80", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,407,513,614,722,8444", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "198,301,402,508,609,717,845,8540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98f289bf6c45d262a7223b262b65f304\\transformed\\foundation-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "84,85", "startColumns": "4,4", "startOffsets": "8809,8897", "endColumns": "87,94", "endOffsets": "8892,8987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e7a34afc2b6fd3a244eccb13c4cf553\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,1006,1092,1171,1246,1324,1401,1478,1547", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,74,77,76,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,1001,1087,1166,1241,1319,1396,1473,1542,1660"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,79,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,947,1031,1127,1227,1316,1400,7791,7882,7967,8049,8135,8214,8289,8367,8545,8622,8691", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,74,77,76,76,68,117", "endOffsets": "942,1026,1122,1222,1311,1395,1488,7877,7962,8044,8130,8209,8284,8362,8439,8617,8686,8804"}}]}]}