package com.example.gxzhaiwu.ui.theme

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.ui.auth.components.AuthButton
import com.example.gxzhaiwu.ui.auth.components.AuthTextField

/**
 * 主题预览组件
 * 用于展示所有颜色和组件在浅色和深色模式下的效果
 */
@Composable
fun ThemePreview(
    modifier: Modifier = Modifier
) {
    var isDarkTheme by remember { mutableStateOf(false) }
    
    GxZhaiWuTheme(darkTheme = isDarkTheme) {
        Surface(
            modifier = modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 主题切换按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "黑白灰主题预览",
                        style = MaterialTheme.typography.headlineMedium,
                        color = MaterialTheme.colorScheme.onBackground
                    )
                    
                    Switch(
                        checked = isDarkTheme,
                        onCheckedChange = { isDarkTheme = it }
                    )
                }
                
                Text(
                    text = if (isDarkTheme) "深色模式" else "浅色模式",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                // 颜色系统预览
                ColorSystemPreview()
                
                // 组件预览
                ComponentPreview()
                
                // 扩展颜色预览
                ExtendedColorsPreview()
            }
        }
    }
}

@Composable
private fun ColorSystemPreview() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.cardElevationColor(2)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "色彩系统",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            // Primary colors
            ColorRow("Primary", MaterialTheme.colorScheme.primary, MaterialTheme.colorScheme.onPrimary)
            ColorRow("Secondary", MaterialTheme.colorScheme.secondary, MaterialTheme.colorScheme.onSecondary)
            ColorRow("Error", MaterialTheme.colorScheme.error, MaterialTheme.colorScheme.onError)
            
            // Surface colors
            ColorRow("Surface", MaterialTheme.colorScheme.surface, MaterialTheme.colorScheme.onSurface)
            ColorRow("Background", MaterialTheme.colorScheme.background, MaterialTheme.colorScheme.onBackground)
        }
    }
}

@Composable
private fun ComponentPreview() {
    var textFieldValue by remember { mutableStateOf("") }
    var hasError by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.cardElevationColor(2)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "组件预览",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            // AuthTextField 预览
            AuthTextField(
                value = textFieldValue,
                onValueChange = { textFieldValue = it },
                label = "邮箱",
                errorMessage = if (hasError) "邮箱格式不正确" else null,
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Email,
                        contentDescription = null
                    )
                }
            )
            
            AuthTextField(
                value = "password123",
                onValueChange = { },
                label = "密码",
                isPassword = true,
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Lock,
                        contentDescription = null
                    )
                }
            )
            
            // AuthButton 预览
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                AuthButton(
                    text = "登录",
                    onClick = { },
                    modifier = Modifier.weight(1f)
                )
                
                AuthButton(
                    text = "加载中",
                    onClick = { },
                    isLoading = true,
                    modifier = Modifier.weight(1f)
                )
            }
            
            AuthButton(
                text = "禁用状态",
                onClick = { },
                enabled = false
            )
            
            // 控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                TextButton(
                    onClick = { hasError = !hasError }
                ) {
                    Text("切换错误状态")
                }
                
                TextButton(
                    onClick = { isLoading = !isLoading }
                ) {
                    Text("切换加载状态")
                }
            }
        }
    }
}

@Composable
private fun ExtendedColorsPreview() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.cardElevationColor(2)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "扩展颜色",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            ColorRow("Success", MaterialTheme.colorScheme.success, MaterialTheme.colorScheme.onSuccess)
            ColorRow("Warning", MaterialTheme.colorScheme.warning, MaterialTheme.colorScheme.onWarning)
            ColorRow("Info", MaterialTheme.colorScheme.info, MaterialTheme.colorScheme.onInfo)
            ColorRow("Outline", MaterialTheme.colorScheme.outline, MaterialTheme.colorScheme.onSurface)
        }
    }
}

@Composable
private fun ColorRow(
    name: String,
    backgroundColor: Color,
    contentColor: Color
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = name,
            modifier = Modifier.width(80.dp),
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Box(
            modifier = Modifier
                .size(40.dp)
                .background(backgroundColor),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "Aa",
                color = contentColor,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold
            )
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = "#${backgroundColor.value.toString(16).uppercase().takeLast(6)}",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Preview(name = "浅色模式")
@Composable
private fun ThemePreviewLight() {
    GxZhaiWuTheme(darkTheme = false) {
        ThemePreview()
    }
}

@Preview(name = "深色模式")
@Composable
private fun ThemePreviewDark() {
    GxZhaiWuTheme(darkTheme = true) {
        ThemePreview()
    }
}
