package com.example.gxzhaiwu.ui.navigation;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0015\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0012\u0007\b\t\n\u000b\f\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014\u0015\u0016\u0017\u0018B\u000f\b\u0004\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u0082\u0001\u0012\u0019\u001a\u001b\u001c\u001d\u001e\u001f !\"#$%&\'()*\u00a8\u0006+"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen;", "", "route", "", "(Ljava/lang/String;)V", "getRoute", "()Ljava/lang/String;", "CreateCustomer", "CreateInvoice", "CreatePayment", "CustomerDetail", "CustomerList", "Dashboard", "Home", "InvoiceDetail", "InvoiceList", "Login", "ManagementCenter", "PaymentList", "Profile", "Register", "Settings", "StoreDetail", "StoreList", "UserManagement", "Lcom/example/gxzhaiwu/ui/navigation/Screen$CreateCustomer;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$CreateInvoice;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$CreatePayment;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$CustomerDetail;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$CustomerList;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$Dashboard;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$Home;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$InvoiceDetail;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$InvoiceList;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$Login;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$ManagementCenter;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$PaymentList;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$Profile;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$Register;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$Settings;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$StoreDetail;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$StoreList;", "Lcom/example/gxzhaiwu/ui/navigation/Screen$UserManagement;", "app_debug"})
public abstract class Screen {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String route = null;
    
    private Screen(java.lang.String route) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRoute() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$CreateCustomer;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class CreateCustomer extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.CreateCustomer INSTANCE = null;
        
        private CreateCustomer() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$CreateInvoice;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class CreateInvoice extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.CreateInvoice INSTANCE = null;
        
        private CreateInvoice() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$CreatePayment;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class CreatePayment extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.CreatePayment INSTANCE = null;
        
        private CreatePayment() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$CustomerDetail;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "createRoute", "", "customerId", "", "app_debug"})
    public static final class CustomerDetail extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.CustomerDetail INSTANCE = null;
        
        private CustomerDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(int customerId) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$CustomerList;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class CustomerList extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.CustomerList INSTANCE = null;
        
        private CustomerList() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$Dashboard;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Dashboard extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.Dashboard INSTANCE = null;
        
        private Dashboard() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$Home;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Home extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.Home INSTANCE = null;
        
        private Home() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$InvoiceDetail;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "createRoute", "", "invoiceId", "", "app_debug"})
    public static final class InvoiceDetail extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.InvoiceDetail INSTANCE = null;
        
        private InvoiceDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(int invoiceId) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$InvoiceList;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class InvoiceList extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.InvoiceList INSTANCE = null;
        
        private InvoiceList() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$Login;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Login extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.Login INSTANCE = null;
        
        private Login() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$ManagementCenter;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class ManagementCenter extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.ManagementCenter INSTANCE = null;
        
        private ManagementCenter() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$PaymentList;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class PaymentList extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.PaymentList INSTANCE = null;
        
        private PaymentList() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$Profile;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Profile extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.Profile INSTANCE = null;
        
        private Profile() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$Register;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Register extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.Register INSTANCE = null;
        
        private Register() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$Settings;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Settings extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.Settings INSTANCE = null;
        
        private Settings() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$StoreDetail;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "createRoute", "", "storeId", "", "app_debug"})
    public static final class StoreDetail extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.StoreDetail INSTANCE = null;
        
        private StoreDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(int storeId) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$StoreList;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class StoreList extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.StoreList INSTANCE = null;
        
        private StoreList() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/Screen$UserManagement;", "Lcom/example/gxzhaiwu/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class UserManagement extends com.example.gxzhaiwu.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.navigation.Screen.UserManagement INSTANCE = null;
        
        private UserManagement() {
        }
    }
}