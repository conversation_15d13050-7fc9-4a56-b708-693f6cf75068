package com.example.gxzhaiwu.ui.dashboard.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a4\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a\b\u0010\t\u001a\u00020\u0001H\u0003\u001a\b\u0010\n\u001a\u00020\u0001H\u0003\u001a\b\u0010\u000b\u001a\u00020\u0001H\u0003\u001a(\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u00042\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u000f2\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0003\u001a\u0012\u0010\u0010\u001a\u0004\u0018\u00010\u00112\u0006\u0010\u0012\u001a\u00020\u0013H\u0002\u00a8\u0006\u0014"}, d2 = {"QuickActionCard", "", "actions", "", "Lcom/example/gxzhaiwu/data/model/QuickActionData;", "onActionClick", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "QuickActionCardAdminPreview", "QuickActionCardEmptyPreview", "QuickActionCardStaffPreview", "QuickActionItem", "action", "onClick", "Lkotlin/Function0;", "getActionIcon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "iconName", "", "app_debug"})
public final class QuickActionCardKt {
    
    /**
     * 快速操作卡片组件
     * 提供常用功能的快速访问入口
     */
    @androidx.compose.runtime.Composable()
    public static final void QuickActionCard(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.gxzhaiwu.data.model.QuickActionData> actions, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.gxzhaiwu.data.model.QuickActionData, kotlin.Unit> onActionClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 单个快速操作项组件
     */
    @androidx.compose.runtime.Composable()
    private static final void QuickActionItem(com.example.gxzhaiwu.data.model.QuickActionData action, kotlin.jvm.functions.Function0<kotlin.Unit> onClick, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 根据图标名称获取对应的ImageVector
     */
    private static final androidx.compose.ui.graphics.vector.ImageVector getActionIcon(java.lang.String iconName) {
        return null;
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u5feb\u901f\u64cd\u4f5c\u5361\u7247 - \u7ba1\u7406\u5458")
    @androidx.compose.runtime.Composable()
    private static final void QuickActionCardAdminPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u5feb\u901f\u64cd\u4f5c\u5361\u7247 - \u5e97\u5458")
    @androidx.compose.runtime.Composable()
    private static final void QuickActionCardStaffPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u5feb\u901f\u64cd\u4f5c\u5361\u7247 - \u7a7a\u72b6\u6001")
    @androidx.compose.runtime.Composable()
    private static final void QuickActionCardEmptyPreview() {
    }
}