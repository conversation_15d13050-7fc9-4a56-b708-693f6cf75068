package com.example.gxzhaiwu.ui.navigation;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/gxzhaiwu/ui/navigation/AuthNavigationViewModel;", "Landroidx/lifecycle/ViewModel;", "authRepository", "Lcom/example/gxzhaiwu/data/repository/AuthRepository;", "(Lcom/example/gxzhaiwu/data/repository/AuthRepository;)V", "getAuthRepository", "()Lcom/example/gxzhaiwu/data/repository/AuthRepository;", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class AuthNavigationViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.repository.AuthRepository authRepository = null;
    
    @javax.inject.Inject()
    public AuthNavigationViewModel(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.repository.AuthRepository authRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.gxzhaiwu.data.repository.AuthRepository getAuthRepository() {
        return null;
    }
}