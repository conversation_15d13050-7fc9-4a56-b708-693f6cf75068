package com.example.gxzhaiwu.ui.navigation;

import com.example.gxzhaiwu.utils.UserStateManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class UserStateViewModel_Factory implements Factory<UserStateViewModel> {
  private final Provider<UserStateManager> userStateManagerProvider;

  public UserStateViewModel_Factory(Provider<UserStateManager> userStateManagerProvider) {
    this.userStateManagerProvider = userStateManagerProvider;
  }

  @Override
  public UserStateViewModel get() {
    return newInstance(userStateManagerProvider.get());
  }

  public static UserStateViewModel_Factory create(
      Provider<UserStateManager> userStateManagerProvider) {
    return new UserStateViewModel_Factory(userStateManagerProvider);
  }

  public static UserStateViewModel newInstance(UserStateManager userStateManager) {
    return new UserStateViewModel(userStateManager);
  }
}
