package com.example.gxzhaiwu.ui.usermanagement.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.example.gxzhaiwu.data.model.UserDetail
import com.example.gxzhaiwu.data.model.getRoleDisplayName
import com.example.gxzhaiwu.data.model.getStoreNames
import com.example.gxzhaiwu.ui.theme.cardElevationColor
import java.text.SimpleDateFormat
import java.util.*

/**
 * 用户详情对话框组件
 * 显示用户的完整信息
 */
@Composable
fun UserDetailDialog(
    user: UserDetail,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.cardElevationColor(2)
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
                    .padding(24.dp)
            ) {
                // 标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "用户详情",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 基本信息部分
                UserInfoSection(
                    title = "基本信息",
                    icon = Icons.Default.Person
                ) {
                    UserInfoItem(
                        label = "姓名",
                        value = user.name,
                        icon = Icons.Default.Badge
                    )
                    
                    UserInfoItem(
                        label = "用户名",
                        value = user.username,
                        icon = Icons.Default.AlternateEmail
                    )
                    
                    UserInfoItem(
                        label = "邮箱",
                        value = user.email,
                        icon = Icons.Default.Email
                    )
                    
                    UserInfoItem(
                        label = "邮箱验证状态",
                        value = if (user.email_verified_at != null) "已验证" else "未验证",
                        icon = if (user.email_verified_at != null) Icons.Default.Verified else Icons.Default.Warning
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 角色权限部分
                UserInfoSection(
                    title = "角色权限",
                    icon = Icons.Default.AdminPanelSettings
                ) {
                    UserInfoItem(
                        label = "主要角色",
                        value = user.getRoleDisplayName(),
                        icon = Icons.Default.Security
                    )
                    
                    if (user.roles.isNotEmpty()) {
                        UserInfoItem(
                            label = "所有角色",
                            value = user.roles.joinToString(", ") { it.name },
                            icon = Icons.Default.Group
                        )
                    }
                    
                    user.permissions?.let { permissions ->
                        UserInfoItem(
                            label = "系统管理员",
                            value = if (permissions.is_admin) "是" else "否",
                            icon = Icons.Default.SupervisorAccount
                        )
                        
                        UserInfoItem(
                            label = "店长权限",
                            value = if (permissions.is_store_owner) "是" else "否",
                            icon = Icons.Default.Store
                        )
                        
                        UserInfoItem(
                            label = "店员权限",
                            value = if (permissions.is_store_staff) "是" else "否",
                            icon = Icons.Default.Work
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 门店信息部分
                UserInfoSection(
                    title = "门店信息",
                    icon = Icons.Default.Store
                ) {
                    UserInfoItem(
                        label = "关联门店",
                        value = user.getStoreNames(),
                        icon = Icons.Default.Business
                    )
                    
                    if (user.stores.isNotEmpty()) {
                        user.stores.forEach { store ->
                            UserInfoItem(
                                label = "门店代码",
                                value = "${store.name} (${store.code})",
                                icon = Icons.Default.QrCode
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 时间信息部分
                UserInfoSection(
                    title = "时间信息",
                    icon = Icons.Default.Schedule
                ) {
                    UserInfoItem(
                        label = "创建时间",
                        value = formatDateTime(user.created_at),
                        icon = Icons.Default.DateRange
                    )
                    
                    UserInfoItem(
                        label = "更新时间",
                        value = formatDateTime(user.updated_at),
                        icon = Icons.Default.Update
                    )
                    
                    if (user.email_verified_at != null) {
                        UserInfoItem(
                            label = "邮箱验证时间",
                            value = formatDateTime(user.email_verified_at),
                            icon = Icons.Default.VerifiedUser
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 关闭按钮
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("关闭")
                }
            }
        }
    }
}

/**
 * 用户信息部分组件
 */
@Composable
private fun UserInfoSection(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    content: @Composable ColumnScope.() -> Unit
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(20.dp)
            )
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.cardElevationColor(1)
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                content = content
            )
        }
    }
}

/**
 * 用户信息项组件
 */
@Composable
private fun UserInfoItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(16.dp)
        )
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * 格式化日期时间
 */
private fun formatDateTime(dateString: String): String {
    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", Locale.getDefault())
        val outputFormat = SimpleDateFormat("yyyy年MM月dd日 HH:mm", Locale.getDefault())
        val date = inputFormat.parse(dateString)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        dateString.substringBefore('T').replaceFirst("-", "年").replaceFirst("-", "月") + "日"
    }
}
