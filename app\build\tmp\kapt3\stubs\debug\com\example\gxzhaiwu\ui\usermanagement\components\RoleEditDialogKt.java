package com.example.gxzhaiwu.ui.usermanagement.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\u001aZ\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u0018\u0010\u0007\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u0005\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u0007\u001a6\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\r2\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u0003\u00a8\u0006\u0014"}, d2 = {"RoleEditDialog", "", "user", "Lcom/example/gxzhaiwu/data/model/UserDetail;", "availableRoles", "", "Lcom/example/gxzhaiwu/data/model/Role;", "onConfirm", "Lkotlin/Function1;", "", "onDismiss", "Lkotlin/Function0;", "isLoading", "", "modifier", "Landroidx/compose/ui/Modifier;", "RoleSelectionItem", "role", "isSelected", "onSelectionChange", "app_debug"})
public final class RoleEditDialogKt {
    
    /**
     * 角色编辑对话框组件
     * 允许管理员修改用户的角色权限
     */
    @androidx.compose.runtime.Composable()
    public static final void RoleEditDialog(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail user, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.gxzhaiwu.data.model.Role> availableRoles, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.List<java.lang.Integer>, kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, boolean isLoading, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 角色选择项组件
     */
    @androidx.compose.runtime.Composable()
    private static final void RoleSelectionItem(com.example.gxzhaiwu.data.model.Role role, boolean isSelected, kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onSelectionChange, androidx.compose.ui.Modifier modifier) {
    }
}