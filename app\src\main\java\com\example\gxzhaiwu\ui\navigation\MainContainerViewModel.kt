package com.example.gxzhaiwu.ui.navigation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 主容器ViewModel
 * 管理底部导航的状态和用户权限
 */
@HiltViewModel
class MainContainerViewModel @Inject constructor(
    // TODO: 注入用户仓库和认证服务
) : ViewModel() {

    private val _uiState = MutableStateFlow(MainContainerUiState())
    val uiState: StateFlow<MainContainerUiState> = _uiState.asStateFlow()

    /**
     * 初始化用户角色
     * TODO: 从认证服务或本地存储获取用户角色
     */
    fun initializeUserRoles() {
        viewModelScope.launch {
            try {
                // 临时使用模拟数据，实际应从认证服务获取
                val userRoles = getMockUserRoles()
                
                _uiState.update { currentState ->
                    currentState.copy(
                        userRoles = userRoles,
                        availableNavItems = BottomNavItem.getAvailableItems(userRoles),
                        isLoading = false
                    )
                }
            } catch (e: Exception) {
                _uiState.update { currentState ->
                    currentState.copy(
                        isLoading = false,
                        hasError = true,
                        errorMessage = "加载用户信息失败: ${e.message}"
                    )
                }
            }
        }
    }

    /**
     * 设置底部导航栏可见性
     */
    fun setBottomNavVisibility(isVisible: Boolean) {
        _uiState.update { it.copy(isBottomNavVisible = isVisible) }
    }

    /**
     * 更新当前路由
     */
    fun updateCurrentRoute(route: String) {
        _uiState.update { it.copy(currentRoute = route) }
    }

    /**
     * 检查用户是否有权限访问指定路由
     */
    fun hasPermissionForRoute(route: String): Boolean {
        val navItem = BottomNavItem.fromRoute(route)
        return navItem?.requiredPermission?.let { permission ->
            com.example.gxzhaiwu.utils.RoleUtils.hasPermission(_uiState.value.userRoles, permission)
        } ?: true
    }

    /**
     * 获取可用的导航项数量
     */
    fun getAvailableNavItemCount(): Int {
        return _uiState.value.availableNavItems.size
    }

    /**
     * 检查是否有管理权限
     */
    fun hasManagementAccess(): Boolean {
        return _uiState.value.availableNavItems.any { it is BottomNavItem.Management }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.update { it.copy(hasError = false, errorMessage = null) }
    }

    /**
     * 模拟获取用户角色
     * TODO: 替换为真实的用户认证逻辑
     */
    private suspend fun getMockUserRoles(): List<String> {
        // 模拟网络延迟
        kotlinx.coroutines.delay(500)
        
        // 返回模拟角色，实际应从认证服务获取
        return listOf("admin") // 可以是 "admin", "store_owner", "store_staff"
    }
}

/**
 * 主容器UI状态
 */
data class MainContainerUiState(
    val userRoles: List<String> = emptyList(),
    val availableNavItems: List<BottomNavItem> = emptyList(),
    val currentRoute: String = BottomNavItem.Dashboard.route,
    val isBottomNavVisible: Boolean = true,
    val isLoading: Boolean = true,
    val hasError: Boolean = false,
    val errorMessage: String? = null
) {
    /**
     * 获取当前选中的导航项
     */
    fun getCurrentNavItem(): BottomNavItem? {
        return availableNavItems.find { it.route == currentRoute }
    }

    /**
     * 检查指定路由是否为当前选中项
     */
    fun isRouteSelected(route: String): Boolean {
        return currentRoute == route
    }

    /**
     * 检查是否为管理员用户
     */
    fun isAdmin(): Boolean {
        return com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles)
    }

    /**
     * 检查是否为店长用户
     */
    fun isStoreOwner(): Boolean {
        return com.example.gxzhaiwu.utils.RoleUtils.isStoreOwner(userRoles)
    }

    /**
     * 检查是否为店员用户
     */
    fun isStoreStaff(): Boolean {
        return com.example.gxzhaiwu.utils.RoleUtils.isStoreStaff(userRoles)
    }

    /**
     * 获取用户主要角色显示名称
     */
    fun getPrimaryRoleDisplayName(): String {
        return com.example.gxzhaiwu.utils.RoleUtils.getRoleDisplayName(userRoles)
    }
}
