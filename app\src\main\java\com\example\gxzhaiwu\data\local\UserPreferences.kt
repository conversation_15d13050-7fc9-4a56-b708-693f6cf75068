package com.example.gxzhaiwu.data.local

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.example.gxzhaiwu.data.model.User
import com.example.gxzhaiwu.utils.Constants
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserPreferences @Inject constructor(
    private val dataStore: DataStore<Preferences>
) {
    
    private object PreferencesKeys {
        val AUTH_TOKEN = stringPreferencesKey(Constants.KEY_AUTH_TOKEN)
        val USER_ID = intPreferencesKey(Constants.KEY_USER_ID)
        val USER_NAME = stringPreferencesKey(Constants.KEY_USER_NAME)
        val USER_EMAIL = stringPreferencesKey(Constants.KEY_USER_EMAIL)
        val USER_ROLES = stringPreferencesKey(Constants.KEY_USER_ROLES)
    }
    
    val authToken: Flow<String?> = dataStore.data.map { preferences ->
        preferences[PreferencesKeys.AUTH_TOKEN]
    }
    
    val isLoggedIn: Flow<Boolean> = dataStore.data.map { preferences ->
        !preferences[PreferencesKeys.AUTH_TOKEN].isNullOrEmpty()
    }
    
    val currentUser: Flow<User?> = dataStore.data.map { preferences ->
        val id = preferences[PreferencesKeys.USER_ID]
        val name = preferences[PreferencesKeys.USER_NAME]
        val email = preferences[PreferencesKeys.USER_EMAIL]
        val rolesString = preferences[PreferencesKeys.USER_ROLES]
        
        if (id != null && name != null && email != null && rolesString != null) {
            val roles = rolesString.split(",").filter { it.isNotEmpty() }
            User(
                id = id,
                name = name,
                email = email,
                created_at = "",
                updated_at = "",
                roles = roles
            )
        } else {
            null
        }
    }
    
    suspend fun saveAuthToken(token: String) {
        dataStore.edit { preferences ->
            preferences[PreferencesKeys.AUTH_TOKEN] = token
        }
    }
    
    suspend fun saveUser(user: User) {
        dataStore.edit { preferences ->
            preferences[PreferencesKeys.USER_ID] = user.id
            preferences[PreferencesKeys.USER_NAME] = user.name
            preferences[PreferencesKeys.USER_EMAIL] = user.email
            preferences[PreferencesKeys.USER_ROLES] = user.roles.joinToString(",")
        }
    }
    
    suspend fun saveLoginData(token: String, user: User) {
        dataStore.edit { preferences ->
            preferences[PreferencesKeys.AUTH_TOKEN] = token
            preferences[PreferencesKeys.USER_ID] = user.id
            preferences[PreferencesKeys.USER_NAME] = user.name
            preferences[PreferencesKeys.USER_EMAIL] = user.email
            preferences[PreferencesKeys.USER_ROLES] = user.roles.joinToString(",")
        }
    }
    
    suspend fun clearAll() {
        dataStore.edit { preferences ->
            preferences.clear()
        }
    }
}
