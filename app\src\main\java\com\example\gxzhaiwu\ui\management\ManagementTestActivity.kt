package com.example.gxzhaiwu.ui.management

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Cancel
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.ManagementModule
import com.example.gxzhaiwu.ui.management.components.ModuleCard
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import com.example.gxzhaiwu.utils.Permission
import com.example.gxzhaiwu.utils.RoleUtils
import dagger.hilt.android.AndroidEntryPoint

/**
 * 管理中心测试活动
 * 用于测试管理中心组件和权限控制
 */
@AndroidEntryPoint
class ManagementTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            GxZhaiWuTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    ManagementTestScreen()
                }
            }
        }
    }
}

@Composable
private fun ManagementTestScreen() {
    var selectedRole by remember { mutableStateOf("admin") }
    
    val testRoles = listOf(
        "admin" to "系统管理员",
        "store_owner" to "店长",
        "store_staff" to "店员"
    )
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "管理中心功能测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        // 角色选择器
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "选择测试角色：",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                testRoles.forEach { (role, displayName) ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedRole == role,
                            onClick = { selectedRole = role }
                        )
                        Text(
                            text = displayName,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }
            }
        }
        
        // 权限测试结果
        PermissionTestCard(userRoles = listOf(selectedRole))
        
        // 管理模块测试
        ManagementModulesTestCard(userRoles = listOf(selectedRole))
    }
}

@Composable
private fun PermissionTestCard(userRoles: List<String>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "权限测试结果",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            val permissions = listOf(
                Permission.ACCESS_MANAGEMENT_CENTER to "访问管理中心",
                Permission.MANAGE_USERS to "管理用户",
                Permission.MANAGE_STORES to "管理门店",
                Permission.MANAGE_BILLS to "管理账单",
                Permission.MANAGE_PAYMENTS to "管理还款",
                Permission.VIEW_ADVANCED_REPORTS to "查看高级报表"
            )
            
            permissions.forEach { (permission, description) ->
                val hasPermission = RoleUtils.hasPermission(userRoles, permission)
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = if (hasPermission) {
                            Icons.Default.CheckCircle
                        } else {
                            Icons.Default.Cancel
                        },
                        contentDescription = null,
                        tint = if (hasPermission) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.error
                        }
                    )
                    Text(
                        text = description,
                        modifier = Modifier.padding(start = 8.dp),
                        color = if (hasPermission) {
                            MaterialTheme.colorScheme.onSurface
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun ManagementModulesTestCard(userRoles: List<String>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "可用管理模块",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            val allModules = getAllTestModules()
            val availableModules = allModules.filter { module ->
                RoleUtils.hasPermission(userRoles, module.requiredPermission)
            }
            
            if (availableModules.isEmpty()) {
                Text(
                    text = "当前角色无权限访问任何管理模块",
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.height(200.dp)
                ) {
                    items(availableModules) { module ->
                        ModuleCard(
                            module = module,
                            onClick = { },
                            modifier = Modifier.height(120.dp)
                        )
                    }
                }
            }
        }
    }
}

private fun getAllTestModules(): List<ManagementModule> {
    return listOf(
        ManagementModule(
            id = "user_management",
            title = "用户管理",
            description = "管理系统用户和权限",
            icon = "people",
            requiredPermission = Permission.MANAGE_USERS,
            route = "user_management"
        ),
        ManagementModule(
            id = "store_management",
            title = "门店管理",
            description = "管理门店信息和设置",
            icon = "store",
            requiredPermission = Permission.MANAGE_STORES,
            route = "store_management"
        ),
        ManagementModule(
            id = "bill_management",
            title = "账单管理",
            description = "管理客户账单和记录",
            icon = "receipt",
            requiredPermission = Permission.MANAGE_BILLS,
            route = "bill_management"
        ),
        ManagementModule(
            id = "payment_management",
            title = "还款管理",
            description = "管理还款记录和状态",
            icon = "payment",
            requiredPermission = Permission.MANAGE_PAYMENTS,
            route = "payment_management"
        ),
        ManagementModule(
            id = "advanced_reports",
            title = "高级报表",
            description = "查看详细的业务分析报表",
            icon = "analytics",
            requiredPermission = Permission.VIEW_ADVANCED_REPORTS,
            route = "advanced_reports"
        )
    )
}
