package com.example.gxzhaiwu.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0003\b\t\nB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0006\u001a\u00020\u0007\u00a8\u0006\u000b"}, d2 = {"Lcom/example/gxzhaiwu/utils/TestUtils;", "", "()V", "getTestCredentials", "Lkotlin/Pair;", "", "scenario", "Lcom/example/gxzhaiwu/utils/TestUtils$TestScenario;", "TestCredentials", "TestResponses", "TestScenario", "app_debug"})
public final class TestUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.gxzhaiwu.utils.TestUtils INSTANCE = null;
    
    private TestUtils() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.String, java.lang.String> getTestCredentials(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.utils.TestUtils.TestScenario scenario) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\f\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0011\u0010\b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u000b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\nR\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/example/gxzhaiwu/utils/TestUtils$TestCredentials;", "", "()V", "EMPTY_LOGIN", "", "EMPTY_PASSWORD", "INVALID_EMAIL", "INVALID_PASSWORD", "LONG_EMAIL", "getLONG_EMAIL", "()Ljava/lang/String;", "LONG_USERNAME", "getLONG_USERNAME", "VALID_EMAIL", "VALID_PASSWORD", "VALID_USERNAME", "app_debug"})
    public static final class TestCredentials {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VALID_EMAIL = "<EMAIL>";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VALID_USERNAME = "testuser";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VALID_PASSWORD = "password123";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String INVALID_EMAIL = "invalid-email";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String INVALID_PASSWORD = "123";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String EMPTY_LOGIN = "";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String EMPTY_PASSWORD = "";
        @org.jetbrains.annotations.NotNull()
        private static final java.lang.String LONG_EMAIL = null;
        @org.jetbrains.annotations.NotNull()
        private static final java.lang.String LONG_USERNAME = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.utils.TestUtils.TestCredentials INSTANCE = null;
        
        private TestCredentials() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getLONG_EMAIL() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getLONG_USERNAME() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/example/gxzhaiwu/utils/TestUtils$TestResponses;", "", "()V", "ERROR_INVALID_CREDENTIALS_JSON", "", "ERROR_NETWORK_JSON", "ERROR_VALIDATION_JSON", "SUCCESS_LOGIN_JSON", "app_debug"})
    public static final class TestResponses {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SUCCESS_LOGIN_JSON = "\n        {\n            \"success\": true,\n            \"data\": {\n                \"user\": {\n                    \"id\": 1,\n                    \"name\": \"\u6d4b\u8bd5\u7528\u6237\",\n                    \"username\": \"testuser\",\n                    \"email\": \"<EMAIL>\",\n                    \"email_verified_at\": null,\n                    \"created_at\": \"2024-01-01T00:00:00.000000Z\",\n                    \"updated_at\": \"2024-01-01T00:00:00.000000Z\",\n                    \"roles\": [\"user\"],\n                    \"stores\": []\n                },\n                \"token\": \"test-jwt-token-12345\"\n            },\n            \"message\": \"\u767b\u5f55\u6210\u529f\"\n        }\n        ";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ERROR_INVALID_CREDENTIALS_JSON = "\n        {\n            \"success\": false,\n            \"message\": \"\u7528\u6237\u540d\u6216\u5bc6\u7801\u9519\u8bef\",\n            \"errors\": {\n                \"login\": [\"\u7528\u6237\u540d\u6216\u5bc6\u7801\u9519\u8bef\"]\n            }\n        }\n        ";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ERROR_VALIDATION_JSON = "\n        {\n            \"success\": false,\n            \"message\": \"\u9a8c\u8bc1\u5931\u8d25\",\n            \"errors\": {\n                \"login\": [\"\u8bf7\u8f93\u5165\u90ae\u7bb1\u6216\u7528\u6237\u540d\"],\n                \"password\": [\"\u5bc6\u7801\u957f\u5ea6\u4e0d\u80fd\u5c11\u4e8e6\u4f4d\"]\n            }\n        }\n        ";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ERROR_NETWORK_JSON = "\n        {\n            \"success\": false,\n            \"message\": \"\u7f51\u7edc\u8fde\u63a5\u5931\u8d25\"\n        }\n        ";
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.utils.TestUtils.TestResponses INSTANCE = null;
        
        private TestResponses() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/example/gxzhaiwu/utils/TestUtils$TestScenario;", "", "(Ljava/lang/String;I)V", "VALID_LOGIN", "INVALID_CREDENTIALS", "VALIDATION_ERROR", "NETWORK_ERROR", "EMPTY_FIELDS", "LONG_INPUT", "app_debug"})
    public static enum TestScenario {
        /*public static final*/ VALID_LOGIN /* = new VALID_LOGIN() */,
        /*public static final*/ INVALID_CREDENTIALS /* = new INVALID_CREDENTIALS() */,
        /*public static final*/ VALIDATION_ERROR /* = new VALIDATION_ERROR() */,
        /*public static final*/ NETWORK_ERROR /* = new NETWORK_ERROR() */,
        /*public static final*/ EMPTY_FIELDS /* = new EMPTY_FIELDS() */,
        /*public static final*/ LONG_INPUT /* = new LONG_INPUT() */;
        
        TestScenario() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.gxzhaiwu.utils.TestUtils.TestScenario> getEntries() {
            return null;
        }
    }
}