package com.example.gxzhaiwu.ui.dashboard

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.ui.dashboard.components.QuickActionCard
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * 仪表盘测试Activity
 * 用于独立测试仪表盘功能
 */
@AndroidEntryPoint
class DashboardTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            GxZhaiWuTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    // 测试QuickActionCard组件
                    TestQuickActionCard()
                }
            }
        }
    }
}

@Composable
private fun TestQuickActionCard() {
    val testActions = listOf(
        QuickActionData(
            title = "创建账单",
            description = "为客户创建新的账单",
            icon = "receipt",
            action = QuickActionType.CREATE_INVOICE,
            enabled = true
        ),
        QuickActionData(
            title = "记录还款",
            description = "记录客户的还款信息",
            icon = "payment",
            action = QuickActionType.RECORD_PAYMENT,
            enabled = true
        )
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "仪表盘组件测试",
            style = MaterialTheme.typography.headlineMedium
        )

        Spacer(modifier = Modifier.height(32.dp))

        QuickActionCard(
            actions = testActions,
            onActionClick = { action ->
                println("点击了操作: ${action.title}")
            }
        )
    }
}
