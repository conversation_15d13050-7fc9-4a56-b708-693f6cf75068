package com.example.gxzhaiwu.ui.dashboard

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * DashboardScreen UI测试
 */
@RunWith(AndroidJUnit4::class)
class DashboardScreenTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun dashboardScreen_显示加载状态() {
        // Given
        val loadingUiState = DashboardUiState(
            isLoading = true
        )

        // When
        composeTestRule.setContent {
            GxZhaiWuTheme {
                DashboardContent(
                    uiState = loadingUiState,
                    onEvent = { }
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("正在加载仪表盘数据...").assertIsDisplayed()
        composeTestRule.onNode(hasTestTag("loading_indicator")).assertIsDisplayed()
    }

    @Test
    fun dashboardScreen_显示错误状态() {
        // Given
        val errorUiState = DashboardUiState(
            hasError = true,
            errorMessage = "网络连接失败"
        )

        // When
        composeTestRule.setContent {
            GxZhaiWuTheme {
                DashboardContent(
                    uiState = errorUiState,
                    onEvent = { }
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("网络连接失败").assertIsDisplayed()
        composeTestRule.onNodeWithText("重试").assertIsDisplayed()
    }

    @Test
    fun dashboardScreen_显示空状态() {
        // Given
        val emptyUiState = DashboardUiState(
            showEmptyState = true
        )

        // When
        composeTestRule.setContent {
            GxZhaiWuTheme {
                DashboardContent(
                    uiState = emptyUiState,
                    onEvent = { }
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("暂无仪表盘数据").assertIsDisplayed()
    }

    @Test
    fun dashboardScreen_显示完整数据内容() {
        // Given
        val fullUiState = createFullDashboardUiState()

        // When
        composeTestRule.setContent {
            GxZhaiWuTheme {
                DashboardContent(
                    uiState = fullUiState,
                    onEvent = { }
                )
            }
        }

        // Then
        // 验证财务汇总卡片
        composeTestRule.onNodeWithText("财务概览").assertIsDisplayed()
        composeTestRule.onNodeWithText("¥100,000.00").assertIsDisplayed()
        composeTestRule.onNodeWithText("¥80,000.00").assertIsDisplayed()

        // 验证统计卡片
        composeTestRule.onNodeWithText("数据概览").assertIsDisplayed()
        composeTestRule.onNodeWithText("总客户数").assertIsDisplayed()
        composeTestRule.onNodeWithText("100").assertIsDisplayed()

        // 验证快速操作
        composeTestRule.onNodeWithText("快速操作").assertIsDisplayed()
        composeTestRule.onNodeWithText("创建账单").assertIsDisplayed()
        composeTestRule.onNodeWithText("记录还款").assertIsDisplayed()
    }

    @Test
    fun dashboardScreen_管理员权限显示高级统计() {
        // Given
        val adminUiState = createAdminDashboardUiState()

        // When
        composeTestRule.setContent {
            GxZhaiWuTheme {
                DashboardContent(
                    uiState = adminUiState,
                    onEvent = { }
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("高级统计").assertIsDisplayed()
        composeTestRule.onNodeWithText("门店数据对比").assertIsDisplayed()
        composeTestRule.onNodeWithText("门店1").assertIsDisplayed()
        composeTestRule.onNodeWithText("门店2").assertIsDisplayed()
    }

    @Test
    fun dashboardScreen_店员权限不显示财务详情() {
        // Given
        val staffUiState = createStaffDashboardUiState()

        // When
        composeTestRule.setContent {
            GxZhaiWuTheme {
                DashboardContent(
                    uiState = staffUiState,
                    onEvent = { }
                )
            }
        }

        // Then
        // 财务汇总卡片不应该显示
        composeTestRule.onNodeWithText("财务概览").assertDoesNotExist()
        
        // 高级统计不应该显示
        composeTestRule.onNodeWithText("高级统计").assertDoesNotExist()
        
        // 但基础统计应该显示
        composeTestRule.onNodeWithText("数据概览").assertIsDisplayed()
        composeTestRule.onNodeWithText("总客户数").assertIsDisplayed()
    }

    @Test
    fun dashboardScreen_点击统计卡片触发事件() {
        // Given
        val uiState = createFullDashboardUiState()
        var clickedCard: StatisticsCardData? = null

        // When
        composeTestRule.setContent {
            GxZhaiWuTheme {
                DashboardContent(
                    uiState = uiState,
                    onEvent = { event ->
                        if (event is DashboardEvent.StatisticsCardClicked) {
                            clickedCard = event.card
                        }
                    }
                )
            }
        }

        // 点击客户统计卡片
        composeTestRule.onNodeWithText("总客户数").performClick()

        // Then
        assert(clickedCard != null)
        assert(clickedCard?.title == "总客户数")
    }

    @Test
    fun dashboardScreen_点击快速操作触发事件() {
        // Given
        val uiState = createFullDashboardUiState()
        var clickedAction: QuickActionData? = null

        // When
        composeTestRule.setContent {
            GxZhaiWuTheme {
                DashboardContent(
                    uiState = uiState,
                    onEvent = { event ->
                        if (event is DashboardEvent.QuickActionClicked) {
                            clickedAction = event.action
                        }
                    }
                )
            }
        }

        // 点击创建账单按钮
        composeTestRule.onNodeWithText("创建账单").performClick()

        // Then
        assert(clickedAction != null)
        assert(clickedAction?.action == QuickActionType.CREATE_INVOICE)
    }

    @Test
    fun dashboardScreen_错误状态点击重试触发事件() {
        // Given
        val errorUiState = DashboardUiState(
            hasError = true,
            errorMessage = "网络连接失败"
        )
        var retryClicked = false

        // When
        composeTestRule.setContent {
            GxZhaiWuTheme {
                DashboardContent(
                    uiState = errorUiState,
                    onEvent = { event ->
                        if (event is DashboardEvent.LoadData) {
                            retryClicked = true
                        }
                    }
                )
            }
        }

        // 点击重试按钮
        composeTestRule.onNodeWithText("重试").performClick()

        // Then
        assert(retryClicked)
    }

    @Test
    fun dashboardScreen_用户角色徽章显示正确() {
        // Given
        val adminUiState = createAdminDashboardUiState()

        // When
        composeTestRule.setContent {
            GxZhaiWuTheme {
                DashboardScreen(
                    onNavigateToScreen = { }
                )
            }
        }

        // Then
        // 注意：这个测试需要模拟ViewModel，在实际项目中需要使用Hilt测试或者提供测试用的ViewModel
        // 这里只是展示测试结构
    }

    private fun createFullDashboardUiState(): DashboardUiState {
        return DashboardUiState(
            isLoading = false,
            overview = DashboardOverview(
                summary = SystemSummary(
                    total_customers = 100,
                    total_invoices = 500,
                    total_payments = 300,
                    total_stores = 5
                ),
                financial = FinancialSummary(
                    total_invoice_amount = "100000.00",
                    total_paid_amount = "80000.00",
                    total_outstanding_amount = "20000.00",
                    total_payment_amount = "80000.00"
                ),
                invoice_status_distribution = InvoiceStatusDistribution(
                    unpaid = 50,
                    partially_paid = 30,
                    paid = 20,
                    overdue = 10
                )
            ),
            userRoles = listOf("admin"),
            permissions = DashboardPermissions(
                canViewAllStores = true,
                canViewFinancialDetails = true,
                canViewStoreComparison = true,
                canAccessAdvancedStatistics = true
            ),
            statisticsCards = listOf(
                StatisticsCardData(
                    title = "总客户数",
                    value = "100",
                    icon = "people",
                    trend = TrendData(value = 5.2, isPositive = true)
                )
            ),
            quickActions = listOf(
                QuickActionData(
                    title = "创建账单",
                    description = "为客户创建新的账单",
                    icon = "add_circle",
                    action = QuickActionType.CREATE_INVOICE,
                    enabled = true
                ),
                QuickActionData(
                    title = "记录还款",
                    description = "记录客户的还款信息",
                    icon = "payment",
                    action = QuickActionType.RECORD_PAYMENT,
                    enabled = true
                )
            ),
            statistics = DashboardStatistics(
                period = StatisticsPeriod(start_date = "2024-01-01", end_date = "2024-01-31"),
                customers = CustomerStatistics(100, 80),
                invoices = InvoiceStatistics(500, "100000.00", "80000.00", "200.00"),
                payments = PaymentStatistics(300, "80000.00", "266.67"),
                stores = listOf(
                    StoreStatistics(1, "门店1", 100, "20000.00", "15000.00"),
                    StoreStatistics(2, "门店2", 150, "30000.00", "25000.00")
                )
            )
        )
    }

    private fun createAdminDashboardUiState(): DashboardUiState {
        return createFullDashboardUiState().copy(
            userRoles = listOf("admin"),
            permissions = DashboardPermissions(
                canViewAllStores = true,
                canViewFinancialDetails = true,
                canViewStoreComparison = true,
                canAccessAdvancedStatistics = true
            )
        )
    }

    private fun createStaffDashboardUiState(): DashboardUiState {
        return createFullDashboardUiState().copy(
            userRoles = listOf("store_staff"),
            permissions = DashboardPermissions(
                canViewAllStores = false,
                canViewFinancialDetails = false,
                canViewStoreComparison = false,
                canAccessAdvancedStatistics = false
            ),
            quickActions = listOf(
                QuickActionData(
                    title = "创建账单",
                    icon = "add_circle",
                    action = QuickActionType.CREATE_INVOICE
                ),
                QuickActionData(
                    title = "记录还款",
                    icon = "payment",
                    action = QuickActionType.RECORD_PAYMENT
                )
            )
        )
    }
}
