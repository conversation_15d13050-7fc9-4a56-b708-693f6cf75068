package com.example.gxzhaiwu.ui.dashboard;

import com.example.gxzhaiwu.data.repository.AuthRepository;
import com.example.gxzhaiwu.data.repository.DashboardRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DashboardViewModel_Factory implements Factory<DashboardViewModel> {
  private final Provider<DashboardRepository> dashboardRepositoryProvider;

  private final Provider<AuthRepository> authRepositoryProvider;

  public DashboardViewModel_Factory(Provider<DashboardRepository> dashboardRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider) {
    this.dashboardRepositoryProvider = dashboardRepositoryProvider;
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public DashboardViewModel get() {
    return newInstance(dashboardRepositoryProvider.get(), authRepositoryProvider.get());
  }

  public static DashboardViewModel_Factory create(
      Provider<DashboardRepository> dashboardRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider) {
    return new DashboardViewModel_Factory(dashboardRepositoryProvider, authRepositoryProvider);
  }

  public static DashboardViewModel newInstance(DashboardRepository dashboardRepository,
      AuthRepository authRepository) {
    return new DashboardViewModel(dashboardRepository, authRepository);
  }
}
