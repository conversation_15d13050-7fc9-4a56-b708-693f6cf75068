package com.example.gxzhaiwu.data.repository;

import com.example.gxzhaiwu.data.api.DashboardApi;
import com.example.gxzhaiwu.data.local.UserPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DashboardRepositoryImpl_Factory implements Factory<DashboardRepositoryImpl> {
  private final Provider<DashboardApi> dashboardApiProvider;

  private final Provider<UserPreferences> userPreferencesProvider;

  public DashboardRepositoryImpl_Factory(Provider<DashboardApi> dashboardApiProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    this.dashboardApiProvider = dashboardApiProvider;
    this.userPreferencesProvider = userPreferencesProvider;
  }

  @Override
  public DashboardRepositoryImpl get() {
    return newInstance(dashboardApiProvider.get(), userPreferencesProvider.get());
  }

  public static DashboardRepositoryImpl_Factory create(Provider<DashboardApi> dashboardApiProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    return new DashboardRepositoryImpl_Factory(dashboardApiProvider, userPreferencesProvider);
  }

  public static DashboardRepositoryImpl newInstance(DashboardApi dashboardApi,
      UserPreferences userPreferences) {
    return new DashboardRepositoryImpl(dashboardApi, userPreferences);
  }
}
