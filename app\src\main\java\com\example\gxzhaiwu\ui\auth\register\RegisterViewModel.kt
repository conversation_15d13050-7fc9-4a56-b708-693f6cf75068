package com.example.gxzhaiwu.ui.auth.register

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gxzhaiwu.data.model.RegisterRequest
import com.example.gxzhaiwu.data.repository.AuthRepository
import com.example.gxzhaiwu.utils.ValidationUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class RegisterViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(RegisterUiState())
    val uiState: StateFlow<RegisterUiState> = _uiState.asStateFlow()

    fun updateName(name: String) {
        _uiState.value = _uiState.value.copy(
            name = name,
            nameError = ValidationUtils.validateName(name),
            errorMessage = null
        )
    }

    fun updateUsername(username: String) {
        _uiState.value = _uiState.value.copy(
            username = username,
            usernameError = ValidationUtils.validateUsername(username),
            errorMessage = null
        )
    }

    fun updateEmail(email: String) {
        _uiState.value = _uiState.value.copy(
            email = email,
            emailError = ValidationUtils.validateEmail(email),
            errorMessage = null
        )
    }

    fun updatePassword(password: String) {
        val currentState = _uiState.value
        _uiState.value = currentState.copy(
            password = password,
            passwordError = ValidationUtils.validatePassword(password),
            passwordConfirmationError = if (currentState.passwordConfirmation.isNotEmpty()) {
                ValidationUtils.validatePasswordConfirmation(password, currentState.passwordConfirmation)
            } else null,
            errorMessage = null
        )
    }

    fun updatePasswordConfirmation(confirmation: String) {
        val currentState = _uiState.value
        _uiState.value = currentState.copy(
            passwordConfirmation = confirmation,
            passwordConfirmationError = ValidationUtils.validatePasswordConfirmation(
                currentState.password, 
                confirmation
            ),
            errorMessage = null
        )
    }

    fun register() {
        val currentState = _uiState.value
        
        // 验证所有表单字段
        val nameError = ValidationUtils.validateName(currentState.name)
        val usernameError = ValidationUtils.validateUsername(currentState.username)
        val emailError = ValidationUtils.validateEmail(currentState.email)
        val passwordError = ValidationUtils.validatePassword(currentState.password)
        val passwordConfirmationError = ValidationUtils.validatePasswordConfirmation(
            currentState.password, 
            currentState.passwordConfirmation
        )
        
        if (nameError != null || usernameError != null || emailError != null || 
            passwordError != null || passwordConfirmationError != null) {
            _uiState.value = currentState.copy(
                nameError = nameError,
                usernameError = usernameError,
                emailError = emailError,
                passwordError = passwordError,
                passwordConfirmationError = passwordConfirmationError
            )
            return
        }

        // 开始注册
        _uiState.value = currentState.copy(
            isLoading = true,
            errorMessage = null
        )

        viewModelScope.launch {
            val registerRequest = RegisterRequest(
                name = currentState.name.trim(),
                username = currentState.username.trim(),
                email = currentState.email.trim(),
                password = currentState.password,
                password_confirmation = currentState.passwordConfirmation
            )

            authRepository.register(registerRequest)
                .onSuccess { (token, user) ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isRegisterSuccessful = true,
                        errorMessage = null
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isRegisterSuccessful = false,
                        errorMessage = exception.message ?: "注册失败，请重试"
                    )
                }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun resetRegisterSuccess() {
        _uiState.value = _uiState.value.copy(isRegisterSuccessful = false)
    }
}
