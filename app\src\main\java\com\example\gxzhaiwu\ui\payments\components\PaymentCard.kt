package com.example.gxzhaiwu.ui.payments.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.ui.payments.PaymentAction
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * 还款记录卡片组件
 * 显示还款基本信息和操作按钮
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PaymentCard(
    payment: Payment,
    availableActions: List<PaymentAction>,
    onPaymentClick: () -> Unit,
    onActionClick: (PaymentAction) -> Unit,
    modifier: Modifier = Modifier
) {
    var showActionMenu by remember { mutableStateOf(false) }

    Card(
        onClick = onPaymentClick,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp,
            pressedElevation = 4.dp
        ),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 头部信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = payment.paymentNumber,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    Text(
                        text = "账单: ${payment.billNumber}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }

                // 状态标签
                PaymentStatusChip(
                    status = payment.status,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 客户和金额信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = payment.customerName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(top = 4.dp)
                    ) {
                        Icon(
                            imageVector = getPaymentMethodIcon(payment.paymentMethod),
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(16.dp)
                        )
                        
                        Text(
                            text = payment.getPaymentMethodDisplayText(),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(start = 4.dp)
                        )
                    }
                }

                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = "¥${payment.amount}",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = when (payment.status) {
                            PaymentStatus.CONFIRMED -> MaterialTheme.colorScheme.primary
                            PaymentStatus.PENDING -> MaterialTheme.colorScheme.tertiary
                            PaymentStatus.CANCELLED -> MaterialTheme.colorScheme.outline
                            PaymentStatus.REFUNDED -> MaterialTheme.colorScheme.error
                        }
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 时间和操作信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = payment.storeName,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Text(
                        text = payment.paymentDate.format(DateTimeFormatter.ofPattern("MM/dd HH:mm")),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                    
                    payment.operatorName?.let { operatorName ->
                        Text(
                            text = "操作员: $operatorName",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(top = 2.dp)
                        )
                    }
                }

                // 操作菜单
                Box {
                    IconButton(
                        onClick = { showActionMenu = true }
                    ) {
                        Icon(
                            imageVector = Icons.Default.MoreVert,
                            contentDescription = "更多操作"
                        )
                    }

                    DropdownMenu(
                        expanded = showActionMenu,
                        onDismissRequest = { showActionMenu = false }
                    ) {
                        availableActions.forEach { action ->
                            DropdownMenuItem(
                                text = { Text(action.displayName) },
                                onClick = {
                                    showActionMenu = false
                                    onActionClick(action)
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = getActionIcon(action),
                                        contentDescription = null
                                    )
                                }
                            )
                        }
                    }
                }
            }

            // 备注信息（如果有）
            payment.description?.let { description ->
                if (description.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        ),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = description,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.padding(8.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 还款状态芯片
 */
@Composable
fun PaymentStatusChip(
    status: PaymentStatus,
    modifier: Modifier = Modifier
) {
    val colors = when (status) {
        PaymentStatus.PENDING -> AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.tertiaryContainer,
            labelColor = MaterialTheme.colorScheme.onTertiaryContainer
        )
        PaymentStatus.CONFIRMED -> AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer,
            labelColor = MaterialTheme.colorScheme.onPrimaryContainer
        )
        PaymentStatus.CANCELLED -> AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant,
            labelColor = MaterialTheme.colorScheme.onSurfaceVariant
        )
        PaymentStatus.REFUNDED -> AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.errorContainer,
            labelColor = MaterialTheme.colorScheme.onErrorContainer
        )
    }

    AssistChip(
        onClick = { },
        label = {
            Text(
                text = status.displayName,
                style = MaterialTheme.typography.labelSmall
            )
        },
        colors = colors,
        modifier = modifier
    )
}

/**
 * 获取支付方式图标
 */
private fun getPaymentMethodIcon(paymentMethod: PaymentMethod) = when (paymentMethod) {
    PaymentMethod.CASH -> Icons.Default.Money
    PaymentMethod.BANK_TRANSFER -> Icons.Default.AccountBalance
    PaymentMethod.ALIPAY -> Icons.Default.Payment
    PaymentMethod.WECHAT_PAY -> Icons.Default.Payment
    PaymentMethod.CREDIT_CARD -> Icons.Default.CreditCard
    PaymentMethod.OTHER -> Icons.Default.MoreHoriz
}

/**
 * 获取操作图标
 */
private fun getActionIcon(action: PaymentAction) = when (action) {
    PaymentAction.VIEW -> Icons.Default.Visibility
    PaymentAction.EDIT -> Icons.Default.Edit
    PaymentAction.DELETE -> Icons.Default.Delete
    PaymentAction.CONFIRM -> Icons.Default.CheckCircle
    PaymentAction.CANCEL -> Icons.Default.Cancel
    PaymentAction.REFUND -> Icons.Default.Undo
    PaymentAction.VIEW_BILL -> Icons.Default.Receipt
    PaymentAction.DUPLICATE -> Icons.Default.ContentCopy
    PaymentAction.EXPORT -> Icons.Default.FileDownload
}

// 预览组件
@Preview(name = "还款记录卡片 - 已确认")
@Composable
private fun PaymentCardConfirmedPreview() {
    GxZhaiWuTheme {
        Surface {
            PaymentCard(
                payment = Payment(
                    id = 1,
                    paymentNumber = "PAY-2024-001",
                    billId = 1,
                    billNumber = "BILL-2024-001",
                    customerId = 1,
                    customerName = "张三",
                    storeId = 1,
                    storeName = "总店",
                    amount = BigDecimal("500.00"),
                    paymentMethod = PaymentMethod.ALIPAY,
                    status = PaymentStatus.CONFIRMED,
                    paymentDate = LocalDateTime.now().minusDays(1),
                    createdAt = LocalDateTime.now().minusDays(1),
                    updatedAt = LocalDateTime.now().minusDays(1),
                    description = "支付宝转账，已确认收款",
                    operatorName = "管理员"
                ),
                availableActions = listOf(PaymentAction.VIEW, PaymentAction.VIEW_BILL, PaymentAction.REFUND),
                onPaymentClick = {},
                onActionClick = {},
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Preview(name = "还款记录卡片 - 待确认")
@Composable
private fun PaymentCardPendingPreview() {
    GxZhaiWuTheme {
        Surface {
            PaymentCard(
                payment = Payment(
                    id = 2,
                    paymentNumber = "PAY-2024-002",
                    billId = 2,
                    billNumber = "BILL-2024-002",
                    customerId = 2,
                    customerName = "李四",
                    storeId = 1,
                    storeName = "总店",
                    amount = BigDecimal("1000.00"),
                    paymentMethod = PaymentMethod.CASH,
                    status = PaymentStatus.PENDING,
                    paymentDate = LocalDateTime.now(),
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                    operatorName = "店员A"
                ),
                availableActions = listOf(PaymentAction.VIEW, PaymentAction.EDIT, PaymentAction.CONFIRM, PaymentAction.CANCEL),
                onPaymentClick = {},
                onActionClick = {},
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}
