package com.example.gxzhaiwu.data.model;

/**
 * 快速操作类型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/example/gxzhaiwu/data/model/QuickActionType;", "", "(Lja<PERSON>/lang/String;I)V", "CREATE_INVOICE", "RECORD_PAYMENT", "ADD_CUSTOMER", "VIEW_REPORTS", "MANAGE_USERS", "MANAGEMENT_CENTER", "app_debug"})
public enum QuickActionType {
    /*public static final*/ CREATE_INVOICE /* = new CREATE_INVOICE() */,
    /*public static final*/ RECORD_PAYMENT /* = new RECORD_PAYMENT() */,
    /*public static final*/ ADD_CUSTOMER /* = new ADD_CUSTOMER() */,
    /*public static final*/ VIEW_REPORTS /* = new VIEW_REPORTS() */,
    /*public static final*/ MANAGE_USERS /* = new MANAGE_USERS() */,
    /*public static final*/ MANAGEMENT_CENTER /* = new MANAGEMENT_CENTER() */;
    
    QuickActionType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.gxzhaiwu.data.model.QuickActionType> getEntries() {
        return null;
    }
}