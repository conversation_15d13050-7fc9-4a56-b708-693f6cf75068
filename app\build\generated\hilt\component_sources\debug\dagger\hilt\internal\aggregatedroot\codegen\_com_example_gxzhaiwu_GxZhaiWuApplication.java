package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.gxzhaiwu.GxZhaiWuApplication",
    rootPackage = "com.example.gxzhaiwu",
    originatingRoot = "com.example.gxzhaiwu.GxZhaiWuApplication",
    originatingRootPackage = "com.example.gxzhaiwu",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "GxZhaiWuApplication",
    originatingRootSimpleNames = "GxZhaiWuApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_gxzhaiwu_GxZhaiWuApplication {
}
