package com.example.gxzhaiwu.data.repository

import com.example.gxzhaiwu.data.api.UserManagementApi
import com.example.gxzhaiwu.data.local.TokenManager
import com.example.gxzhaiwu.data.model.*
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import okhttp3.ResponseBody.Companion.toResponseBody
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import retrofit2.Response

/**
 * 用户管理Repository单元测试
 */
@OptIn(ExperimentalCoroutinesApi::class)
class UserManagementRepositoryTest {

    private lateinit var repository: UserManagementRepositoryImpl
    private lateinit var api: UserManagementApi
    private lateinit var tokenManager: TokenManager

    private val testToken = "test_token"
    private val authHeader = "Bearer $testToken"

    // 测试数据
    private val testUser = User(
        id = 1,
        name = "张三",
        username = "zhang<PERSON>",
        email = "<EMAIL>",
        created_at = "2024-01-01T00:00:00.000000Z",
        updated_at = "2024-01-01T00:00:00.000000Z",
        roles = listOf(
            Role(id = 1, name = "系统管理员", slug = "admin")
        )
    )

    private val testUsers = listOf(testUser)
    
    private val testPaginatedData = PaginatedUserData(
        current_page = 1,
        data = testUsers,
        first_page_url = "http://example.com/api/users?page=1",
        from = 1,
        last_page = 1,
        last_page_url = "http://example.com/api/users?page=1",
        next_page_url = null,
        path = "http://example.com/api/users",
        per_page = 15,
        prev_page_url = null,
        to = 1,
        total = 1
    )

    private val testRoles = listOf(
        Role(id = 1, name = "系统管理员", slug = "admin", is_system = true),
        Role(id = 2, name = "店长", slug = "store_owner", is_system = false)
    )

    @Before
    fun setup() {
        api = mockk()
        tokenManager = mockk()
        
        coEvery { tokenManager.getToken() } returns testToken
        
        repository = UserManagementRepositoryImpl(api, tokenManager)
    }

    @Test
    fun `获取用户列表成功应该返回分页数据`() = runTest {
        val response = UserListResponse(
            success = true,
            data = testPaginatedData
        )
        
        coEvery { 
            api.getUsers(authHeader, null, null, 1, 15) 
        } returns Response.success(response)

        val result = repository.getUsers()

        assertTrue(result.isSuccess)
        assertEquals(testPaginatedData, result.getOrNull())
        
        coVerify { api.getUsers(authHeader, null, null, 1, 15) }
    }

    @Test
    fun `获取用户列表失败应该返回错误`() = runTest {
        coEvery { 
            api.getUsers(authHeader, null, null, 1, 15) 
        } returns Response.error(404, "Not Found".toResponseBody())

        val result = repository.getUsers()

        assertTrue(result.isFailure)
        assertTrue(result.exceptionOrNull()?.message?.contains("网络请求失败") == true)
    }

    @Test
    fun `获取用户详情成功应该返回用户数据`() = runTest {
        val response = UserDetailResponse(
            success = true,
            data = testUser
        )
        
        coEvery { 
            api.getUserDetail(authHeader, 1) 
        } returns Response.success(response)

        val result = repository.getUserDetail(1)

        assertTrue(result.isSuccess)
        assertEquals(testUser, result.getOrNull())
        
        coVerify { api.getUserDetail(authHeader, 1) }
    }

    @Test
    fun `更新用户角色成功应该返回更新后的用户`() = runTest {
        val roleIds = listOf(1, 2)
        val request = UpdateUserRolesRequest(roleIds)
        val updatedUser = testUser.copy(
            roles = testRoles
        )
        val response = UserDetailResponse(
            success = true,
            data = updatedUser
        )
        
        coEvery { 
            api.updateUserRoles(authHeader, 1, request) 
        } returns Response.success(response)

        val result = repository.updateUserRoles(1, roleIds)

        assertTrue(result.isSuccess)
        assertEquals(updatedUser, result.getOrNull())
        
        coVerify { api.updateUserRoles(authHeader, 1, request) }
    }

    @Test
    fun `更新用户门店权限成功应该返回更新后的用户`() = runTest {
        val storeIds = listOf(1, 2)
        val request = UpdateUserStoresRequest(storeIds)
        val response = UserDetailResponse(
            success = true,
            data = testUser
        )
        
        coEvery { 
            api.updateUserStores(authHeader, 1, request) 
        } returns Response.success(response)

        val result = repository.updateUserStores(1, storeIds)

        assertTrue(result.isSuccess)
        assertEquals(testUser, result.getOrNull())
        
        coVerify { api.updateUserStores(authHeader, 1, request) }
    }

    @Test
    fun `获取角色列表成功应该返回角色数据并缓存`() = runTest {
        val response = RoleListResponse(
            success = true,
            data = testRoles
        )
        
        coEvery { 
            api.getRoles(authHeader) 
        } returns Response.success(response)

        // 第一次调用
        val result1 = repository.getRoles()
        assertTrue(result1.isSuccess)
        assertEquals(testRoles, result1.getOrNull())

        // 第二次调用应该使用缓存
        val result2 = repository.getRoles()
        assertTrue(result2.isSuccess)
        assertEquals(testRoles, result2.getOrNull())

        // 验证API只被调用一次（使用缓存）
        coVerify(exactly = 1) { api.getRoles(authHeader) }
    }

    @Test
    fun `API响应success为false应该返回错误`() = runTest {
        val response = UserListResponse(
            success = false,
            data = testPaginatedData,
            message = "权限不足"
        )
        
        coEvery { 
            api.getUsers(authHeader, null, null, 1, 15) 
        } returns Response.success(response)

        val result = repository.getUsers()

        assertTrue(result.isFailure)
        assertEquals("权限不足", result.exceptionOrNull()?.message)
    }

    @Test
    fun `网络异常应该返回错误`() = runTest {
        coEvery { 
            api.getUsers(authHeader, null, null, 1, 15) 
        } throws Exception("网络连接失败")

        val result = repository.getUsers()

        assertTrue(result.isFailure)
        assertEquals("网络连接失败", result.exceptionOrNull()?.message)
    }

    @Test
    fun `搜索参数应该正确传递给API`() = runTest {
        val response = UserListResponse(
            success = true,
            data = testPaginatedData
        )
        
        coEvery { 
            api.getUsers(authHeader, "张三", "admin", 2, 10) 
        } returns Response.success(response)

        val result = repository.getUsers(
            search = "张三",
            role = "admin", 
            page = 2,
            perPage = 10
        )

        assertTrue(result.isSuccess)
        coVerify { api.getUsers(authHeader, "张三", "admin", 2, 10) }
    }

    @Test
    fun `清除缓存应该重置所有缓存数据`() = runTest {
        // 先加载一些数据到缓存
        val rolesResponse = RoleListResponse(success = true, data = testRoles)
        coEvery { api.getRoles(authHeader) } returns Response.success(rolesResponse)
        
        repository.getRoles()
        
        // 清除缓存
        repository.clearCache()
        
        // 再次获取角色应该重新调用API
        repository.getRoles()
        
        // 验证API被调用了两次
        coVerify(exactly = 2) { api.getRoles(authHeader) }
    }
}
