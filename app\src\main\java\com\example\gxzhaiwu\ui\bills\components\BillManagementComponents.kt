package com.example.gxzhaiwu.ui.bills.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.ui.bills.BillFilter
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import java.math.BigDecimal

/**
 * 账单管理顶部栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BillManagementTopBar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    currentFilter: BillFilter,
    onFilterChange: (BillFilter) -> Unit,
    onClearFilters: () -> Unit,
    modifier: Modifier = Modifier
) {
    var showFilterDialog by remember { mutableStateOf(false) }

    TopAppBar(
        title = { Text("账单管理") },
        actions = {
            // 搜索按钮
            IconButton(onClick = { /* TODO: 展开搜索栏 */ }) {
                Icon(Icons.Default.Search, contentDescription = "搜索")
            }
            
            // 过滤按钮
            IconButton(onClick = { showFilterDialog = true }) {
                Badge(
                    containerColor = if (currentFilter.hasActiveFilter()) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.surfaceVariant
                    }
                ) {
                    if (currentFilter.hasActiveFilter()) {
                        Text(
                            text = currentFilter.getActiveFilterCount().toString(),
                            style = MaterialTheme.typography.labelSmall
                        )
                    }
                }
                Icon(Icons.Default.FilterList, contentDescription = "过滤")
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surface,
            titleContentColor = MaterialTheme.colorScheme.onSurface
        ),
        modifier = modifier
    )

    // 过滤对话框
    if (showFilterDialog) {
        BillFilterDialog(
            currentFilter = currentFilter,
            onFilterChange = onFilterChange,
            onClearFilters = onClearFilters,
            onDismiss = { showFilterDialog = false }
        )
    }
}

/**
 * 账单统计卡片
 */
@Composable
fun BillStatisticsCard(
    statistics: BillStatistics,
    modifier: Modifier = Modifier
) {
    Card(
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "账单统计",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    label = "总账单",
                    value = statistics.totalBills.toString(),
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                StatisticItem(
                    label = "总金额",
                    value = "¥${statistics.totalAmount}",
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                StatisticItem(
                    label = "已收款",
                    value = "¥${statistics.paidAmount}",
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                StatisticItem(
                    label = "待收款",
                    value = "¥${statistics.pendingAmount}",
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }

            if (statistics.overdueCount > 0) {
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    AssistChip(
                        onClick = { },
                        label = {
                            Text("${statistics.overdueCount}笔逾期 ¥${statistics.overdueAmount}")
                        },
                        leadingIcon = {
                            Icon(
                                Icons.Default.Warning,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.error
                            )
                        },
                        colors = AssistChipDefaults.assistChipColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer,
                            labelColor = MaterialTheme.colorScheme.onErrorContainer
                        )
                    )
                }
            }
        }
    }
}

/**
 * 统计项目组件
 */
@Composable
private fun StatisticItem(
    label: String,
    value: String,
    color: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = color.copy(alpha = 0.8f)
        )
    }
}

/**
 * 账单空状态
 */
@Composable
fun BillEmptyState(
    onCreateBill: () -> Unit,
    canCreateBill: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Receipt,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.outline,
            modifier = Modifier.size(64.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "暂无账单",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Text(
            text = "创建第一个账单开始管理",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 8.dp)
        )
        
        if (canCreateBill) {
            Spacer(modifier = Modifier.height(24.dp))
            
            Button(
                onClick = onCreateBill
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("创建账单")
            }
        }
    }
}

/**
 * 账单搜索空状态
 */
@Composable
fun BillEmptySearchState(
    searchQuery: String,
    onClearSearch: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.SearchOff,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.outline,
            modifier = Modifier.size(64.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "未找到相关账单",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Text(
            text = "搜索\"$searchQuery\"无结果",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 8.dp)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        TextButton(onClick = onClearSearch) {
            Text("清除搜索")
        }
    }
}

/**
 * 账单过滤对话框
 */
@Composable
fun BillFilterDialog(
    currentFilter: BillFilter,
    onFilterChange: (BillFilter) -> Unit,
    onClearFilters: () -> Unit,
    onDismiss: () -> Unit
) {
    var tempFilter by remember { mutableStateOf(currentFilter) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("筛选账单") },
        text = {
            Column {
                // 状态过滤
                Text(
                    text = "账单状态",
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // TODO: 实现状态选择器
                Text(
                    text = "状态选择器待实现",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onFilterChange(tempFilter)
                    onDismiss()
                }
            ) {
                Text("应用")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

// 预览组件
@Preview(name = "账单统计卡片")
@Composable
private fun BillStatisticsCardPreview() {
    GxZhaiWuTheme {
        Surface {
            BillStatisticsCard(
                statistics = BillStatistics(
                    totalBills = 25,
                    totalAmount = BigDecimal("50000.00"),
                    paidAmount = BigDecimal("30000.00"),
                    pendingAmount = BigDecimal("15000.00"),
                    overdueAmount = BigDecimal("5000.00"),
                    overdueCount = 3
                ),
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}
