package com.example.gxzhaiwu.utils

import java.security.MessageDigest
import java.util.regex.Pattern

object SecurityUtils {
    
    // 密码强度验证
    private val PASSWORD_PATTERN = Pattern.compile(
        "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,}$"
    )
    
    // 邮箱验证
    private val EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    )
    
    // 用户名验证（只允许字母、数字、下划线）
    private val USERNAME_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_]{3,20}$"
    )
    
    /**
     * 验证密码强度
     * @param password 密码
     * @return 是否符合强密码要求
     */
    fun isStrongPassword(password: String): <PERSON><PERSON><PERSON> {
        return PASSWORD_PATTERN.matcher(password).matches()
    }
    
    /**
     * 验证邮箱格式
     * @param email 邮箱
     * @return 是否为有效邮箱格式
     */
    fun isValidEmail(email: String): Boolean {
        return EMAIL_PATTERN.matcher(email).matches()
    }
    
    /**
     * 验证用户名格式
     * @param username 用户名
     * @return 是否为有效用户名格式
     */
    fun isValidUsername(username: String): Boolean {
        return USERNAME_PATTERN.matcher(username).matches()
    }
    
    /**
     * 清理输入字符串，防止XSS攻击
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    fun sanitizeInput(input: String): String {
        return input.trim()
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("\"", "&quot;")
            .replace("'", "&#x27;")
            .replace("/", "&#x2F;")
    }
    
    /**
     * 生成MD5哈希（仅用于非敏感数据）
     * @param input 输入字符串
     * @return MD5哈希值
     */
    fun md5Hash(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val digest = md.digest(input.toByteArray())
        return digest.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * 验证JWT Token格式
     * @param token JWT Token
     * @return 是否为有效的JWT格式
     */
    fun isValidJwtFormat(token: String): Boolean {
        val parts = token.split(".")
        return parts.size == 3 && parts.all { it.isNotEmpty() }
    }
    
    /**
     * 检查输入是否包含SQL注入关键词
     * @param input 输入字符串
     * @return 是否包含可疑的SQL关键词
     */
    fun containsSqlInjection(input: String): Boolean {
        val sqlKeywords = listOf(
            "select", "insert", "update", "delete", "drop", "create", "alter",
            "union", "or", "and", "where", "from", "table", "database",
            "script", "javascript", "vbscript", "onload", "onerror"
        )
        val lowerInput = input.lowercase()
        return sqlKeywords.any { lowerInput.contains(it) }
    }
    
    /**
     * 生成安全的随机字符串
     * @param length 字符串长度
     * @return 随机字符串
     */
    fun generateSecureRandomString(length: Int): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        return (1..length)
            .map { chars.random() }
            .joinToString("")
    }
}
