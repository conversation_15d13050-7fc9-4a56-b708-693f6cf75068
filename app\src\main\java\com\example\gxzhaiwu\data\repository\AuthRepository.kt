package com.example.gxzhaiwu.data.repository

import com.example.gxzhaiwu.data.model.LoginRequest
import com.example.gxzhaiwu.data.model.RegisterRequest
import com.example.gxzhaiwu.data.model.User
import kotlinx.coroutines.flow.Flow

interface AuthRepository {
    
    suspend fun login(loginRequest: LoginRequest): Result<Pair<String, User>>
    
    suspend fun register(registerRequest: RegisterRequest): Result<Pair<String, User>>
    
    suspend fun logout(): Result<Unit>
    
    suspend fun getCurrentUser(): Result<User>
    
    fun getAuthToken(): Flow<String?>
    
    fun isLoggedIn(): Flow<Boolean>
    
    fun getCurrentUserFlow(): Flow<User?>

    fun getCurrentUserRoles(): List<String>

    suspend fun clearUserData()
}
