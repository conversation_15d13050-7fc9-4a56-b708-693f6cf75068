package com.example.gxzhaiwu.ui.dashboard;

/**
 * 仪表盘UI状态数据类
 * 管理仪表盘页面的所有状态信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b0\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u00ad\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\n0\r\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\r\u0012\u000e\b\u0002\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\r\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0019\u00a2\u0006\u0002\u0010\u001aJ\t\u00107\u001a\u00020\u0003H\u00c6\u0003J\u000f\u00108\u001a\b\u0012\u0004\u0012\u00020\u00130\rH\u00c6\u0003J\u000b\u00109\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003J\t\u0010:\u001a\u00020\u0003H\u00c6\u0003J\t\u0010;\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010<\u001a\u0004\u0018\u00010\u0019H\u00c6\u0003\u00a2\u0006\u0002\u0010$J\t\u0010=\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010>\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010?\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010@\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\t\u0010A\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010B\u001a\b\u0012\u0004\u0012\u00020\n0\rH\u00c6\u0003J\t\u0010C\u001a\u00020\u000fH\u00c6\u0003J\u000f\u0010D\u001a\b\u0012\u0004\u0012\u00020\u00110\rH\u00c6\u0003J\u00b6\u0001\u0010E\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00032\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\n0\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\r2\u000e\b\u0002\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\r2\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u00032\b\b\u0002\u0010\u0017\u001a\u00020\u00032\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0019H\u00c6\u0001\u00a2\u0006\u0002\u0010FJ\u0013\u0010G\u001a\u00020\u00032\b\u0010H\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010I\u001a\u00020JH\u00d6\u0001J\t\u0010K\u001a\u00020\nH\u00d6\u0001R\u0011\u0010\u001b\u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b\u001c\u0010\u001dR\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010 \u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b!\u0010\u001dR\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001dR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u001dR\u0011\u0010\u0017\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u001dR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u001dR\u0015\u0010\u0018\u001a\u0004\u0018\u00010\u0019\u00a2\u0006\n\n\u0002\u0010%\u001a\u0004\b#\u0010$R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0013\u0010*\u001a\u0004\u0018\u00010\n8F\u00a2\u0006\u0006\u001a\u0004\b+\u0010\u001fR\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\r\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0013\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0011\u0010\u0016\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010\u001dR\u0011\u00101\u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b2\u0010\u001dR\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u00104R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\r\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010-R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\n0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010-\u00a8\u0006L"}, d2 = {"Lcom/example/gxzhaiwu/ui/dashboard/DashboardUiState;", "", "isLoading", "", "isRefreshing", "overview", "Lcom/example/gxzhaiwu/data/model/DashboardOverview;", "statistics", "Lcom/example/gxzhaiwu/data/model/DashboardStatistics;", "errorMessage", "", "hasError", "userRoles", "", "permissions", "Lcom/example/gxzhaiwu/data/model/DashboardPermissions;", "statisticsCards", "Lcom/example/gxzhaiwu/data/model/StatisticsCardData;", "quickActions", "Lcom/example/gxzhaiwu/data/model/QuickActionData;", "selectedDateRange", "Lcom/example/gxzhaiwu/ui/dashboard/DateRange;", "showAdvancedStatistics", "isNetworkAvailable", "lastUpdated", "", "(ZZLcom/example/gxzhaiwu/data/model/DashboardOverview;Lcom/example/gxzhaiwu/data/model/DashboardStatistics;Ljava/lang/String;ZLjava/util/List;Lcom/example/gxzhaiwu/data/model/DashboardPermissions;Ljava/util/List;Ljava/util/List;Lcom/example/gxzhaiwu/ui/dashboard/DateRange;ZZLjava/lang/Long;)V", "canRefresh", "getCanRefresh", "()Z", "getErrorMessage", "()Ljava/lang/String;", "hasData", "getHasData", "getHasError", "getLastUpdated", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getOverview", "()Lcom/example/gxzhaiwu/data/model/DashboardOverview;", "getPermissions", "()Lcom/example/gxzhaiwu/data/model/DashboardPermissions;", "primaryErrorMessage", "getPrimaryErrorMessage", "getQuickActions", "()Ljava/util/List;", "getSelectedDateRange", "()Lcom/example/gxzhaiwu/ui/dashboard/DateRange;", "getShowAdvancedStatistics", "showEmptyState", "getShowEmptyState", "getStatistics", "()Lcom/example/gxzhaiwu/data/model/DashboardStatistics;", "getStatisticsCards", "getUserRoles", "component1", "component10", "component11", "component12", "component13", "component14", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(ZZLcom/example/gxzhaiwu/data/model/DashboardOverview;Lcom/example/gxzhaiwu/data/model/DashboardStatistics;Ljava/lang/String;ZLjava/util/List;Lcom/example/gxzhaiwu/data/model/DashboardPermissions;Ljava/util/List;Ljava/util/List;Lcom/example/gxzhaiwu/ui/dashboard/DateRange;ZZLjava/lang/Long;)Lcom/example/gxzhaiwu/ui/dashboard/DashboardUiState;", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class DashboardUiState {
    private final boolean isLoading = false;
    private final boolean isRefreshing = false;
    @org.jetbrains.annotations.Nullable()
    private final com.example.gxzhaiwu.data.model.DashboardOverview overview = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.gxzhaiwu.data.model.DashboardStatistics statistics = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    private final boolean hasError = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> userRoles = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.model.DashboardPermissions permissions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.gxzhaiwu.data.model.StatisticsCardData> statisticsCards = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.gxzhaiwu.data.model.QuickActionData> quickActions = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.gxzhaiwu.ui.dashboard.DateRange selectedDateRange = null;
    private final boolean showAdvancedStatistics = false;
    private final boolean isNetworkAvailable = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long lastUpdated = null;
    
    public DashboardUiState(boolean isLoading, boolean isRefreshing, @org.jetbrains.annotations.Nullable()
    com.example.gxzhaiwu.data.model.DashboardOverview overview, @org.jetbrains.annotations.Nullable()
    com.example.gxzhaiwu.data.model.DashboardStatistics statistics, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, boolean hasError, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> userRoles, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.DashboardPermissions permissions, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.gxzhaiwu.data.model.StatisticsCardData> statisticsCards, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.gxzhaiwu.data.model.QuickActionData> quickActions, @org.jetbrains.annotations.Nullable()
    com.example.gxzhaiwu.ui.dashboard.DateRange selectedDateRange, boolean showAdvancedStatistics, boolean isNetworkAvailable, @org.jetbrains.annotations.Nullable()
    java.lang.Long lastUpdated) {
        super();
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isRefreshing() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.gxzhaiwu.data.model.DashboardOverview getOverview() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.gxzhaiwu.data.model.DashboardStatistics getStatistics() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    public final boolean getHasError() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getUserRoles() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.gxzhaiwu.data.model.DashboardPermissions getPermissions() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.gxzhaiwu.data.model.StatisticsCardData> getStatisticsCards() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.gxzhaiwu.data.model.QuickActionData> getQuickActions() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.gxzhaiwu.ui.dashboard.DateRange getSelectedDateRange() {
        return null;
    }
    
    public final boolean getShowAdvancedStatistics() {
        return false;
    }
    
    public final boolean isNetworkAvailable() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getLastUpdated() {
        return null;
    }
    
    public final boolean getHasData() {
        return false;
    }
    
    public final boolean getShowEmptyState() {
        return false;
    }
    
    public final boolean getCanRefresh() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPrimaryErrorMessage() {
        return null;
    }
    
    public DashboardUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.gxzhaiwu.data.model.QuickActionData> component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.gxzhaiwu.ui.dashboard.DateRange component11() {
        return null;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component14() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.gxzhaiwu.data.model.DashboardOverview component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.gxzhaiwu.data.model.DashboardStatistics component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.gxzhaiwu.data.model.DashboardPermissions component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.gxzhaiwu.data.model.StatisticsCardData> component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.gxzhaiwu.ui.dashboard.DashboardUiState copy(boolean isLoading, boolean isRefreshing, @org.jetbrains.annotations.Nullable()
    com.example.gxzhaiwu.data.model.DashboardOverview overview, @org.jetbrains.annotations.Nullable()
    com.example.gxzhaiwu.data.model.DashboardStatistics statistics, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, boolean hasError, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> userRoles, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.DashboardPermissions permissions, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.gxzhaiwu.data.model.StatisticsCardData> statisticsCards, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.gxzhaiwu.data.model.QuickActionData> quickActions, @org.jetbrains.annotations.Nullable()
    com.example.gxzhaiwu.ui.dashboard.DateRange selectedDateRange, boolean showAdvancedStatistics, boolean isNetworkAvailable, @org.jetbrains.annotations.Nullable()
    java.lang.Long lastUpdated) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}