package com.example.gxzhaiwu.ui.navigation

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme

/**
 * 底部导航栏组件
 * 支持权限驱动的动态显示和Material Design 3样式
 */
@Composable
fun BottomNavigationBar(
    navController: NavController,
    availableItems: List<BottomNavItem>,
    isVisible: Boolean = true,
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = isVisible && availableItems.isNotEmpty(),
        enter = slideInVertically(initialOffsetY = { it }),
        exit = slideOutVertically(targetOffsetY = { it }),
        modifier = modifier
    ) {
        NavigationBar(
            containerColor = MaterialTheme.colorScheme.surface,
            contentColor = MaterialTheme.colorScheme.onSurface,
            tonalElevation = 8.dp,
            modifier = Modifier.fillMaxWidth()
        ) {
            val navBackStackEntry by navController.currentBackStackEntryAsState()
            val currentRoute = navBackStackEntry?.destination?.route

            availableItems.forEach { item ->
                val isSelected = currentRoute == item.route
                
                NavigationBarItem(
                    icon = {
                        Icon(
                            imageVector = if (isSelected) item.selectedIcon else item.icon,
                            contentDescription = item.title,
                            modifier = Modifier.size(24.dp)
                        )
                    },
                    label = {
                        Text(
                            text = item.title,
                            style = MaterialTheme.typography.labelSmall,
                            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    },
                    selected = isSelected,
                    onClick = {
                        if (currentRoute != item.route) {
                            navController.navigate(item.route) {
                                // 清除回退栈到根页面，避免堆积
                                popUpTo(navController.graph.startDestinationId) {
                                    saveState = true
                                }
                                // 避免重复创建相同页面
                                launchSingleTop = true
                                // 恢复状态
                                restoreState = true
                            }
                        }
                    },
                    colors = NavigationBarItemDefaults.colors(
                        selectedIconColor = MaterialTheme.colorScheme.primary,
                        selectedTextColor = MaterialTheme.colorScheme.primary,
                        unselectedIconColor = MaterialTheme.colorScheme.onSurfaceVariant,
                        unselectedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,
                        indicatorColor = MaterialTheme.colorScheme.primaryContainer
                    )
                )
            }
        }
    }
}

/**
 * 底部导航栏容器组件
 * 集成权限检查和状态管理
 */
@Composable
fun BottomNavigationContainer(
    navController: NavController,
    userRoles: List<String>,
    isVisible: Boolean = true,
    modifier: Modifier = Modifier
) {
    val availableItems = remember(userRoles) {
        BottomNavItem.getAvailableItems(userRoles)
    }

    BottomNavigationBar(
        navController = navController,
        availableItems = availableItems,
        isVisible = isVisible,
        modifier = modifier
    )
}

/**
 * 获取当前导航状态
 */
@Composable
fun rememberBottomNavState(
    navController: NavController,
    userRoles: List<String>
): BottomNavState {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route ?: BottomNavItem.Dashboard.route
    
    val availableItems = remember(userRoles) {
        BottomNavItem.getAvailableItems(userRoles)
    }

    return remember(currentRoute, availableItems) {
        BottomNavState(
            currentRoute = currentRoute,
            availableItems = availableItems,
            isVisible = true
        )
    }
}

// 预览组件
@Preview(name = "底部导航栏 - 管理员")
@Composable
private fun BottomNavigationBarAdminPreview() {
    GxZhaiWuTheme {
        Surface {
            BottomNavigationBar(
                navController = rememberNavController(),
                availableItems = BottomNavItem.getAvailableItems(listOf("admin")),
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Preview(name = "底部导航栏 - 店员")
@Composable
private fun BottomNavigationBarStaffPreview() {
    GxZhaiWuTheme {
        Surface {
            BottomNavigationBar(
                navController = rememberNavController(),
                availableItems = BottomNavItem.getAvailableItems(listOf("store_staff")),
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Preview(name = "底部导航栏 - 店长")
@Composable
private fun BottomNavigationBarOwnerPreview() {
    GxZhaiWuTheme {
        Surface {
            BottomNavigationBar(
                navController = rememberNavController(),
                availableItems = BottomNavItem.getAvailableItems(listOf("store_owner")),
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}
