package com.example.gxzhaiwu.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a*\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0007\u0010\b\u001a\b\u0010\t\u001a\u00020\u0001H\u0003\u001a\b\u0010\n\u001a\u00020\u0001H\u0003\u001a\b\u0010\u000b\u001a\u00020\u0001H\u0003\u001a\u0012\u0010\f\u001a\u00020\u00012\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a\b\u0010\u000f\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0010\u001a\u00020\u0001H\u0003\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0011"}, d2 = {"ColorRow", "", "name", "", "backgroundColor", "Landroidx/compose/ui/graphics/Color;", "contentColor", "ColorRow-WkMS-hQ", "(Ljava/lang/String;JJ)V", "ColorSystemPreview", "ComponentPreview", "ExtendedColorsPreview", "ThemePreview", "modifier", "Landroidx/compose/ui/Modifier;", "ThemePreviewDark", "ThemePreviewLight", "app_debug"})
public final class ThemePreviewKt {
    
    /**
     * 主题预览组件
     * 用于展示所有颜色和组件在浅色和深色模式下的效果
     */
    @androidx.compose.runtime.Composable()
    public static final void ThemePreview(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ColorSystemPreview() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ComponentPreview() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ExtendedColorsPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u6d45\u8272\u6a21\u5f0f")
    @androidx.compose.runtime.Composable()
    private static final void ThemePreviewLight() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u6df1\u8272\u6a21\u5f0f")
    @androidx.compose.runtime.Composable()
    private static final void ThemePreviewDark() {
    }
}