package com.example.gxzhaiwu.data.model

import kotlinx.serialization.json.Json
import org.junit.Test
import org.junit.Assert.*

/**
 * StatisticsPeriod数据模型测试
 * 验证JSON解析和null值处理
 */
class StatisticsPeriodTest {

    private val json = Json { ignoreUnknownKeys = true }

    @Test
    fun `测试正常的日期范围JSON解析`() {
        val jsonString = """
            {
                "start_date": "2024-01-01",
                "end_date": "2024-01-31"
            }
        """.trimIndent()

        val period = json.decodeFromString<StatisticsPeriod>(jsonString)
        
        assertEquals("2024-01-01", period.start_date)
        assertEquals("2024-01-31", period.end_date)
        assertEquals("2024-01-01 至 2024-01-31", period.getDisplayRange())
    }

    @Test
    fun `测试null值的JSON解析`() {
        val jsonString = """
            {
                "start_date": null,
                "end_date": null
            }
        """.trimIndent()

        val period = json.decodeFromString<StatisticsPeriod>(jsonString)
        
        assertNull(period.start_date)
        assertNull(period.end_date)
        assertEquals("全部时间", period.getDisplayRange())
    }

    @Test
    fun `测试部分null值的JSON解析`() {
        val jsonString1 = """
            {
                "start_date": "2024-01-01",
                "end_date": null
            }
        """.trimIndent()

        val period1 = json.decodeFromString<StatisticsPeriod>(jsonString1)
        assertEquals("从 2024-01-01 开始", period1.getDisplayRange())

        val jsonString2 = """
            {
                "start_date": null,
                "end_date": "2024-01-31"
            }
        """.trimIndent()

        val period2 = json.decodeFromString<StatisticsPeriod>(jsonString2)
        assertEquals("截至 2024-01-31", period2.getDisplayRange())
    }

    @Test
    fun `测试缺失字段的JSON解析`() {
        val jsonString = """
            {
            }
        """.trimIndent()

        val period = json.decodeFromString<StatisticsPeriod>(jsonString)
        
        assertNull(period.start_date)
        assertNull(period.end_date)
        assertEquals("全部时间", period.getDisplayRange())
    }

    @Test
    fun `测试显示方法`() {
        val period = StatisticsPeriod()
        
        assertEquals("未指定", period.getDisplayStartDate())
        assertEquals("未指定", period.getDisplayEndDate())
        assertEquals("全部时间", period.getDisplayRange())
    }
}
