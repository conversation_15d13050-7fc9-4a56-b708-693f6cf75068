android.app.Application#androidx.activity.ComponentActivity2kotlinx.serialization.internal.GeneratedSerializer3com.example.gxzhaiwu.data.repository.AuthRepositoryandroidx.lifecycle.ViewModel)com.example.gxzhaiwu.ui.navigation.Screenkotlin.Enum8com.example.gxzhaiwu.data.repository.DashboardRepository0com.example.gxzhaiwu.ui.dashboard.DashboardEvent5com.example.gxzhaiwu.ui.dashboard.DashboardSideEffect3com.example.gxzhaiwu.data.model.UserOperationResult=com.example.gxzhaiwu.data.repository.UserManagementRepository                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        