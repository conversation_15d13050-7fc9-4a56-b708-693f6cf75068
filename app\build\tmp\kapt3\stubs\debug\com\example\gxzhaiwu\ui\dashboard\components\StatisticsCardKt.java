package com.example.gxzhaiwu.ui.dashboard.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000(\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a,\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0007H\u0007\u001a\b\u0010\b\u001a\u00020\u0001H\u0003\u001a\b\u0010\t\u001a\u00020\u0001H\u0003\u001a\b\u0010\n\u001a\u00020\u0001H\u0003\u001a\u0012\u0010\u000b\u001a\u0004\u0018\u00010\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002\u00a8\u0006\u000f"}, d2 = {"StatisticsCard", "", "data", "Lcom/example/gxzhaiwu/data/model/StatisticsCardData;", "modifier", "Landroidx/compose/ui/Modifier;", "onClick", "Lkotlin/Function0;", "StatisticsCardDarkPreview", "StatisticsCardNoTrendPreview", "StatisticsCardPreview", "getIconByName", "Landroidx/compose/ui/graphics/vector/ImageVector;", "iconName", "", "app_debug"})
public final class StatisticsCardKt {
    
    /**
     * 统计卡片组件
     * 用于显示各种统计数据，支持趋势指示和图标
     */
    @androidx.compose.runtime.Composable()
    public static final void StatisticsCard(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.StatisticsCardData data, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    /**
     * 根据图标名称获取对应的ImageVector
     */
    private static final androidx.compose.ui.graphics.vector.ImageVector getIconByName(java.lang.String iconName) {
        return null;
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u6d45\u8272\u6a21\u5f0f - \u57fa\u7840\u7edf\u8ba1\u5361\u7247")
    @androidx.compose.runtime.Composable()
    private static final void StatisticsCardPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u6df1\u8272\u6a21\u5f0f - \u8d22\u52a1\u7edf\u8ba1\u5361\u7247")
    @androidx.compose.runtime.Composable()
    private static final void StatisticsCardDarkPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u65e0\u8d8b\u52bf\u6570\u636e\u5361\u7247")
    @androidx.compose.runtime.Composable()
    private static final void StatisticsCardNoTrendPreview() {
    }
}