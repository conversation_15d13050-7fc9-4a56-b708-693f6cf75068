package com.example.gxzhaiwu.data.repository

import com.example.gxzhaiwu.data.api.DashboardApi
import com.example.gxzhaiwu.data.local.UserPreferences
import com.example.gxzhaiwu.data.model.*
import io.mockk.*
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import okhttp3.ResponseBody.Companion.toResponseBody
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import retrofit2.Response

/**
 * DashboardRepository单元测试
 */
class DashboardRepositoryTest {

    private lateinit var repository: DashboardRepositoryImpl
    private lateinit var dashboardApi: DashboardApi
    private lateinit var userPreferences: UserPreferences

    @Before
    fun setup() {
        dashboardApi = mockk()
        userPreferences = mockk()
        repository = DashboardRepositoryImpl(dashboardApi, userPreferences)
    }

    @Test
    fun `获取概览数据成功时应该返回正确结果`() = runTest {
        // Given
        val token = "test_token"
        val mockOverview = createMockDashboardOverview()
        val mockResponse = DashboardOverviewResponse(
            success = true,
            data = mockOverview
        )

        every { userPreferences.authToken } returns flowOf(token)
        coEvery { dashboardApi.getOverview("Bearer $token") } returns Response.success(mockResponse)

        // When
        val result = repository.getOverview()

        // Then
        assertTrue(result.isSuccess)
        assertEquals(mockOverview, result.getOrNull())
        coVerify { dashboardApi.getOverview("Bearer $token") }
    }

    @Test
    fun `用户未登录时应该返回错误`() = runTest {
        // Given
        every { userPreferences.authToken } returns flowOf(null)

        // When
        val result = repository.getOverview()

        // Then
        assertTrue(result.isFailure)
        assertEquals("用户未登录", result.exceptionOrNull()?.message)
    }

    @Test
    fun `API返回401时应该返回认证失败错误`() = runTest {
        // Given
        val token = "invalid_token"
        every { userPreferences.authToken } returns flowOf(token)
        coEvery { dashboardApi.getOverview("Bearer $token") } returns Response.error(
            401,
            "Unauthorized".toResponseBody()
        )

        // When
        val result = repository.getOverview()

        // Then
        assertTrue(result.isFailure)
        assertEquals("认证失败，请重新登录", result.exceptionOrNull()?.message)
    }

    @Test
    fun `API返回403时应该返回权限不足错误`() = runTest {
        // Given
        val token = "valid_token"
        every { userPreferences.authToken } returns flowOf(token)
        coEvery { dashboardApi.getOverview("Bearer $token") } returns Response.error(
            403,
            "Forbidden".toResponseBody()
        )

        // When
        val result = repository.getOverview()

        // Then
        assertTrue(result.isFailure)
        assertEquals("权限不足", result.exceptionOrNull()?.message)
    }

    @Test
    fun `API返回500时应该返回服务器错误`() = runTest {
        // Given
        val token = "valid_token"
        every { userPreferences.authToken } returns flowOf(token)
        coEvery { dashboardApi.getOverview("Bearer $token") } returns Response.error(
            500,
            "Internal Server Error".toResponseBody()
        )

        // When
        val result = repository.getOverview()

        // Then
        assertTrue(result.isFailure)
        assertEquals("服务器内部错误", result.exceptionOrNull()?.message)
    }

    @Test
    fun `获取统计数据时应该传递正确的日期参数`() = runTest {
        // Given
        val token = "test_token"
        val startDate = "2024-01-01"
        val endDate = "2024-01-31"
        val mockStatistics = createMockDashboardStatistics()
        val mockResponse = DashboardStatisticsResponse(
            success = true,
            data = mockStatistics
        )

        every { userPreferences.authToken } returns flowOf(token)
        coEvery { 
            dashboardApi.getStatistics("Bearer $token", startDate, endDate) 
        } returns Response.success(mockResponse)

        // When
        val result = repository.getStatistics(startDate, endDate)

        // Then
        assertTrue(result.isSuccess)
        assertEquals(mockStatistics, result.getOrNull())
        coVerify { dashboardApi.getStatistics("Bearer $token", startDate, endDate) }
    }

    @Test
    fun `API响应success为false时应该返回错误`() = runTest {
        // Given
        val token = "test_token"
        val errorMessage = "数据获取失败"
        val mockResponse = DashboardOverviewResponse(
            success = false,
            data = createMockDashboardOverview(),
            message = errorMessage
        )

        every { userPreferences.authToken } returns flowOf(token)
        coEvery { dashboardApi.getOverview("Bearer $token") } returns Response.success(mockResponse)

        // When
        val result = repository.getOverview()

        // Then
        assertTrue(result.isFailure)
        assertEquals(errorMessage, result.exceptionOrNull()?.message)
    }

    @Test
    fun `网络异常时应该返回错误`() = runTest {
        // Given
        val token = "test_token"
        val exception = Exception("网络连接超时")

        every { userPreferences.authToken } returns flowOf(token)
        coEvery { dashboardApi.getOverview("Bearer $token") } throws exception

        // When
        val result = repository.getOverview()

        // Then
        assertTrue(result.isFailure)
        assertEquals(exception, result.exceptionOrNull())
    }

    private fun createMockDashboardOverview(): DashboardOverview {
        return DashboardOverview(
            summary = SystemSummary(
                total_customers = 100,
                total_invoices = 500,
                total_payments = 300,
                total_stores = 5
            ),
            financial = FinancialSummary(
                total_invoice_amount = "100000.00",
                total_paid_amount = "80000.00",
                total_outstanding_amount = "20000.00",
                total_payment_amount = "80000.00"
            ),
            invoice_status_distribution = InvoiceStatusDistribution(
                unpaid = 50,
                partially_paid = 30,
                paid = 20,
                overdue = 10
            )
        )
    }

    private fun createMockDashboardStatistics(): DashboardStatistics {
        return DashboardStatistics(
            period = StatisticsPeriod(start_date = "2024-01-01", end_date = "2024-01-31"),
            customers = CustomerStatistics(100, 80),
            invoices = InvoiceStatistics(500, "100000.00", "80000.00", "200.00"),
            payments = PaymentStatistics(300, "80000.00", "266.67"),
            stores = listOf(
                StoreStatistics(1, "门店1", 100, "20000.00", "15000.00"),
                StoreStatistics(2, "门店2", 150, "30000.00", "25000.00")
            )
        )
    }
}
