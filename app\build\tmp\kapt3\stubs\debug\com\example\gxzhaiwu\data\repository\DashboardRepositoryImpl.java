package com.example.gxzhaiwu.data.repository;

/**
 * 仪表盘数据仓库实现类
 * 负责从API获取仪表盘数据并处理相关业务逻辑
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\n\u0010\u000bJ0\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\b2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u000fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0011\u0010\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0013"}, d2 = {"Lcom/example/gxzhaiwu/data/repository/DashboardRepositoryImpl;", "Lcom/example/gxzhaiwu/data/repository/DashboardRepository;", "dashboardApi", "Lcom/example/gxzhaiwu/data/api/DashboardApi;", "userPreferences", "Lcom/example/gxzhaiwu/data/local/UserPreferences;", "(Lcom/example/gxzhaiwu/data/api/DashboardApi;Lcom/example/gxzhaiwu/data/local/UserPreferences;)V", "getOverview", "Lkotlin/Result;", "Lcom/example/gxzhaiwu/data/model/DashboardOverview;", "getOverview-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getStatistics", "Lcom/example/gxzhaiwu/data/model/DashboardStatistics;", "startDate", "", "endDate", "getStatistics-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class DashboardRepositoryImpl implements com.example.gxzhaiwu.data.repository.DashboardRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.api.DashboardApi dashboardApi = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.local.UserPreferences userPreferences = null;
    
    @javax.inject.Inject()
    public DashboardRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.api.DashboardApi dashboardApi, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.local.UserPreferences userPreferences) {
        super();
    }
}