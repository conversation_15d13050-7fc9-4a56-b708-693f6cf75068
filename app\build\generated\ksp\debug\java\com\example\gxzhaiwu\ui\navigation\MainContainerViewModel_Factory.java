package com.example.gxzhaiwu.ui.navigation;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class MainContainerViewModel_Factory implements Factory<MainContainerViewModel> {
  @Override
  public MainContainerViewModel get() {
    return newInstance();
  }

  public static MainContainerViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MainContainerViewModel newInstance() {
    return new MainContainerViewModel();
  }

  private static final class InstanceHolder {
    private static final MainContainerViewModel_Factory INSTANCE = new MainContainerViewModel_Factory();
  }
}
