package com.example.gxzhaiwu.data.model

import kotlinx.serialization.Serializable

@Serializable
data class User(
    val id: Int,
    val name: String,
    val username: String? = null,
    val email: String,
    val email_verified_at: String? = null,
    val created_at: String? = null,
    val updated_at: String? = null,
    val roles: List<String>,
    val stores: List<Store>? = null
)

@Serializable
data class Store(
    val id: Int,
    val name: String,
    val code: String? = null
)

@Serializable
data class LoginRequest(
    val login: String, // 可以是邮箱或用户名
    val password: String
)

@Serializable
data class LoginResponse(
    val user: User,
    val token: String
)

@Serializable
data class RegisterRequest(
    val name: String,
    val username: String,
    val email: String,
    val password: String,
    val password_confirmation: String
)

@Serializable
data class RegisterResponse(
    val user: User,
    val token: String
)
