package com.example.gxzhaiwu.data.api;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideUserManagementApiFactory implements Factory<UserManagementApi> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideUserManagementApiFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public UserManagementApi get() {
    return provideUserManagementApi(retrofitProvider.get());
  }

  public static NetworkModule_ProvideUserManagementApiFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideUserManagementApiFactory(retrofitProvider);
  }

  public static UserManagementApi provideUserManagementApi(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideUserManagementApi(retrofit));
  }
}
