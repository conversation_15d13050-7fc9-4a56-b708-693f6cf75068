package com.example.gxzhaiwu.ui.dashboard

import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.data.repository.AuthRepository
import com.example.gxzhaiwu.data.repository.DashboardRepository
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * DashboardViewModel单元测试
 */
@OptIn(ExperimentalCoroutinesApi::class)
class DashboardViewModelTest {

    private lateinit var viewModel: DashboardViewModel
    private lateinit var dashboardRepository: DashboardRepository
    private lateinit var authRepository: AuthRepository
    private val testDispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        
        dashboardRepository = mockk()
        authRepository = mockk()
        
        // 模拟用户已登录
        every { authRepository.getCurrentUserFlow() } returns flowOf(
            User(
                id = 1,
                name = "测试用户",
                email = "<EMAIL>",
                roles = listOf("admin"),
                stores = listOf(
                    Store(id = 1, name = "测试门店", address = "测试地址")
                )
            )
        )
        
        viewModel = DashboardViewModel(dashboardRepository, authRepository)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
        clearAllMocks()
    }

    @Test
    fun `初始化时应该加载用户权限和仪表盘数据`() = runTest {
        // Given
        val mockOverview = createMockDashboardOverview()
        coEvery { dashboardRepository.getOverview() } returns Result.success(mockOverview)
        coEvery { dashboardRepository.getStatistics(any(), any()) } returns Result.success(createMockDashboardStatistics())

        // When
        advanceUntilIdle()

        // Then
        val uiState = viewModel.uiState.value
        assertEquals(listOf("admin"), uiState.userRoles)
        assertTrue(uiState.permissions.canViewAllStores)
        assertTrue(uiState.permissions.canViewFinancialDetails)
        assertNotNull(uiState.overview)
        assertFalse(uiState.isLoading)
    }

    @Test
    fun `加载数据失败时应该显示错误状态`() = runTest {
        // Given
        val errorMessage = "网络连接失败"
        coEvery { dashboardRepository.getOverview() } returns Result.failure(Exception(errorMessage))

        // When
        viewModel.onEvent(DashboardEvent.LoadData)
        advanceUntilIdle()

        // Then
        val uiState = viewModel.uiState.value
        assertTrue(uiState.hasError)
        assertTrue(uiState.errorMessage?.contains(errorMessage) == true)
        assertFalse(uiState.isLoading)
    }

    @Test
    fun `刷新数据应该更新UI状态`() = runTest {
        // Given
        val mockOverview = createMockDashboardOverview()
        coEvery { dashboardRepository.getOverview() } returns Result.success(mockOverview)

        // When
        viewModel.onEvent(DashboardEvent.RefreshData)
        advanceUntilIdle()

        // Then
        val uiState = viewModel.uiState.value
        assertNotNull(uiState.overview)
        assertFalse(uiState.isRefreshing)
        assertNotNull(uiState.lastUpdated)
    }

    @Test
    fun `清除错误应该重置错误状态`() = runTest {
        // Given - 先设置错误状态
        coEvery { dashboardRepository.getOverview() } returns Result.failure(Exception("测试错误"))
        viewModel.onEvent(DashboardEvent.LoadData)
        advanceUntilIdle()

        // When
        viewModel.onEvent(DashboardEvent.ClearError)

        // Then
        val uiState = viewModel.uiState.value
        assertFalse(uiState.hasError)
        assertNull(uiState.errorMessage)
    }

    @Test
    fun `选择日期范围应该重新加载数据`() = runTest {
        // Given
        val dateRange = DateRange("2024-01-01", "2024-01-31", "一月")
        val mockOverview = createMockDashboardOverview()
        coEvery { dashboardRepository.getOverview() } returns Result.success(mockOverview)
        coEvery { dashboardRepository.getStatistics("2024-01-01", "2024-01-31") } returns Result.success(createMockDashboardStatistics())

        // When
        viewModel.onEvent(DashboardEvent.SelectDateRange(dateRange))
        advanceUntilIdle()

        // Then
        val uiState = viewModel.uiState.value
        assertEquals(dateRange, uiState.selectedDateRange)
        coVerify { dashboardRepository.getStatistics("2024-01-01", "2024-01-31") }
    }

    @Test
    fun `用户未登录时应该导航到登录页面`() = runTest {
        // Given
        every { authRepository.getCurrentUserFlow() } returns flowOf(null)
        val sideEffects = mutableListOf<DashboardSideEffect>()
        
        // When
        val newViewModel = DashboardViewModel(dashboardRepository, authRepository)
        newViewModel.sideEffect.collect { sideEffects.add(it) }
        advanceUntilIdle()

        // Then
        assertTrue(sideEffects.any { it is DashboardSideEffect.NavigateToLogin })
    }

    @Test
    fun `管理员角色应该有完整权限`() = runTest {
        // Given
        every { authRepository.getCurrentUserFlow() } returns flowOf(
            User(
                id = 1,
                name = "管理员",
                email = "<EMAIL>",
                roles = listOf("admin"),
                stores = emptyList()
            )
        )

        // When
        val newViewModel = DashboardViewModel(dashboardRepository, authRepository)
        advanceUntilIdle()

        // Then
        val uiState = newViewModel.uiState.value
        assertTrue(uiState.permissions.canViewAllStores)
        assertTrue(uiState.permissions.canViewFinancialDetails)
        assertTrue(uiState.permissions.canViewStoreComparison)
        assertTrue(uiState.permissions.canAccessAdvancedStatistics)
    }

    @Test
    fun `店员角色应该有限制权限`() = runTest {
        // Given
        every { authRepository.getCurrentUserFlow() } returns flowOf(
            User(
                id = 1,
                name = "店员",
                email = "<EMAIL>",
                roles = listOf("store_staff"),
                stores = listOf(Store(id = 1, name = "门店1", address = "地址1"))
            )
        )

        // When
        val newViewModel = DashboardViewModel(dashboardRepository, authRepository)
        advanceUntilIdle()

        // Then
        val uiState = newViewModel.uiState.value
        assertFalse(uiState.permissions.canViewAllStores)
        assertFalse(uiState.permissions.canViewFinancialDetails)
        assertFalse(uiState.permissions.canViewStoreComparison)
        assertFalse(uiState.permissions.canAccessAdvancedStatistics)
    }

    private fun createMockDashboardOverview(): DashboardOverview {
        return DashboardOverview(
            summary = SystemSummary(
                total_customers = 100,
                total_invoices = 500,
                total_payments = 300,
                total_stores = 5
            ),
            financial = FinancialSummary(
                total_invoice_amount = "100000.00",
                total_paid_amount = "80000.00",
                total_outstanding_amount = "20000.00",
                total_payment_amount = "80000.00"
            ),
            invoice_status_distribution = InvoiceStatusDistribution(
                unpaid = 50,
                partially_paid = 30,
                paid = 20,
                overdue = 10
            )
        )
    }

    private fun createMockDashboardStatistics(): DashboardStatistics {
        return DashboardStatistics(
            period = StatisticsPeriod("2024-01-01", "2024-01-31"),
            customers = CustomerStatistics(100, 80),
            invoices = InvoiceStatistics(500, "100000.00", "80000.00", "200.00"),
            payments = PaymentStatistics(300, "80000.00", "266.67"),
            stores = listOf(
                StoreStatistics(1, "门店1", 100, "20000.00", "15000.00"),
                StoreStatistics(2, "门店2", 150, "30000.00", "25000.00")
            )
        )
    }
}
