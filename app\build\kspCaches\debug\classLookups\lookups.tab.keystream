  Activity android.app  Context android.content  ContextWrapper android.content  ContextThemeWrapper android.view  ComponentActivity androidx.activity  ComponentActivity androidx.core.app  	ViewModel androidx.lifecycle  MainActivity com.example.gxzhaiwu  AuthApi com.example.gxzhaiwu.data.api  DashboardApi com.example.gxzhaiwu.data.api  
NetworkModule com.example.gxzhaiwu.data.api  UserManagementApi com.example.gxzhaiwu.data.api  PreferencesModule com.example.gxzhaiwu.data.local  UserPreferences com.example.gxzhaiwu.data.local  AuthRepository $com.example.gxzhaiwu.data.repository  AuthRepositoryImpl $com.example.gxzhaiwu.data.repository  DashboardRepository $com.example.gxzhaiwu.data.repository  DashboardRepositoryImpl $com.example.gxzhaiwu.data.repository  RepositoryModule $com.example.gxzhaiwu.data.repository  UserManagementRepository $com.example.gxzhaiwu.data.repository  UserManagementRepositoryImpl $com.example.gxzhaiwu.data.repository  LoginUiState "com.example.gxzhaiwu.ui.auth.login  LoginViewModel "com.example.gxzhaiwu.ui.auth.login  RegisterUiState %com.example.gxzhaiwu.ui.auth.register  RegisterViewModel %com.example.gxzhaiwu.ui.auth.register  BillManagementViewModel com.example.gxzhaiwu.ui.bills  DashboardTestActivity !com.example.gxzhaiwu.ui.dashboard  DashboardUiState !com.example.gxzhaiwu.ui.dashboard  DashboardViewModel !com.example.gxzhaiwu.ui.dashboard  
HomeViewModel com.example.gxzhaiwu.ui.home  ManagementCenterViewModel "com.example.gxzhaiwu.ui.management  ManagementTestActivity "com.example.gxzhaiwu.ui.management  AuthNavigationViewModel "com.example.gxzhaiwu.ui.navigation  BottomNavigationTestActivity "com.example.gxzhaiwu.ui.navigation  MainContainerViewModel "com.example.gxzhaiwu.ui.navigation  PaymentManagementViewModel  com.example.gxzhaiwu.ui.payments  UserManagementViewModel &com.example.gxzhaiwu.ui.usermanagement  	MediaType okhttp3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         