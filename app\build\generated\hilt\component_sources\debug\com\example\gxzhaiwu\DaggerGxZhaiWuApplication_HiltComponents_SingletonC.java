package com.example.gxzhaiwu;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.example.gxzhaiwu.data.api.AuthApi;
import com.example.gxzhaiwu.data.api.DashboardApi;
import com.example.gxzhaiwu.data.api.NetworkModule_ProvideAuthApiFactory;
import com.example.gxzhaiwu.data.api.NetworkModule_ProvideDashboardApiFactory;
import com.example.gxzhaiwu.data.api.NetworkModule_ProvideHttpLoggingInterceptorFactory;
import com.example.gxzhaiwu.data.api.NetworkModule_ProvideJsonFactory;
import com.example.gxzhaiwu.data.api.NetworkModule_ProvideOkHttpClientFactory;
import com.example.gxzhaiwu.data.api.NetworkModule_ProvideRetrofitFactory;
import com.example.gxzhaiwu.data.api.NetworkModule_ProvideUserManagementApiFactory;
import com.example.gxzhaiwu.data.api.UserManagementApi;
import com.example.gxzhaiwu.data.local.PreferencesModule_ProvideDataStoreFactory;
import com.example.gxzhaiwu.data.local.UserPreferences;
import com.example.gxzhaiwu.data.repository.AuthRepositoryImpl;
import com.example.gxzhaiwu.data.repository.DashboardRepositoryImpl;
import com.example.gxzhaiwu.data.repository.UserManagementRepositoryImpl;
import com.example.gxzhaiwu.ui.auth.login.LoginViewModel;
import com.example.gxzhaiwu.ui.auth.login.LoginViewModel_HiltModules;
import com.example.gxzhaiwu.ui.auth.register.RegisterViewModel;
import com.example.gxzhaiwu.ui.auth.register.RegisterViewModel_HiltModules;
import com.example.gxzhaiwu.ui.bills.BillManagementViewModel;
import com.example.gxzhaiwu.ui.bills.BillManagementViewModel_HiltModules;
import com.example.gxzhaiwu.ui.dashboard.DashboardTestActivity;
import com.example.gxzhaiwu.ui.dashboard.DashboardViewModel;
import com.example.gxzhaiwu.ui.dashboard.DashboardViewModel_HiltModules;
import com.example.gxzhaiwu.ui.home.HomeViewModel;
import com.example.gxzhaiwu.ui.home.HomeViewModel_HiltModules;
import com.example.gxzhaiwu.ui.management.ManagementCenterViewModel;
import com.example.gxzhaiwu.ui.management.ManagementCenterViewModel_HiltModules;
import com.example.gxzhaiwu.ui.management.ManagementTestActivity;
import com.example.gxzhaiwu.ui.navigation.AuthNavigationViewModel;
import com.example.gxzhaiwu.ui.navigation.AuthNavigationViewModel_HiltModules;
import com.example.gxzhaiwu.ui.navigation.BottomNavigationTestActivity;
import com.example.gxzhaiwu.ui.navigation.MainContainerViewModel;
import com.example.gxzhaiwu.ui.navigation.MainContainerViewModel_HiltModules;
import com.example.gxzhaiwu.ui.payments.PaymentManagementViewModel;
import com.example.gxzhaiwu.ui.payments.PaymentManagementViewModel_HiltModules;
import com.example.gxzhaiwu.ui.usermanagement.UserManagementViewModel;
import com.example.gxzhaiwu.ui.usermanagement.UserManagementViewModel_HiltModules;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.IdentifierNameString;
import dagger.internal.KeepFieldType;
import dagger.internal.LazyClassKeyMap;
import dagger.internal.MapBuilder;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;
import kotlinx.serialization.json.Json;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DaggerGxZhaiWuApplication_HiltComponents_SingletonC {
  private DaggerGxZhaiWuApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public GxZhaiWuApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements GxZhaiWuApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public GxZhaiWuApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements GxZhaiWuApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public GxZhaiWuApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements GxZhaiWuApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public GxZhaiWuApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements GxZhaiWuApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public GxZhaiWuApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements GxZhaiWuApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public GxZhaiWuApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements GxZhaiWuApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public GxZhaiWuApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements GxZhaiWuApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public GxZhaiWuApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends GxZhaiWuApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends GxZhaiWuApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends GxZhaiWuApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends GxZhaiWuApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity arg0) {
    }

    @Override
    public void injectDashboardTestActivity(DashboardTestActivity arg0) {
    }

    @Override
    public void injectManagementTestActivity(ManagementTestActivity arg0) {
    }

    @Override
    public void injectBottomNavigationTestActivity(BottomNavigationTestActivity arg0) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Map<Class<?>, Boolean> getViewModelKeys() {
      return LazyClassKeyMap.<Boolean>of(MapBuilder.<String, Boolean>newMapBuilder(10).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_navigation_AuthNavigationViewModel, AuthNavigationViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_bills_BillManagementViewModel, BillManagementViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_dashboard_DashboardViewModel, DashboardViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_home_HomeViewModel, HomeViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_auth_login_LoginViewModel, LoginViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_navigation_MainContainerViewModel, MainContainerViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_management_ManagementCenterViewModel, ManagementCenterViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_payments_PaymentManagementViewModel, PaymentManagementViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_auth_register_RegisterViewModel, RegisterViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_usermanagement_UserManagementViewModel, UserManagementViewModel_HiltModules.KeyModule.provide()).build());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @IdentifierNameString
    private static final class LazyClassKeyProvider {
      static String com_example_gxzhaiwu_ui_navigation_MainContainerViewModel = "com.example.gxzhaiwu.ui.navigation.MainContainerViewModel";

      static String com_example_gxzhaiwu_ui_usermanagement_UserManagementViewModel = "com.example.gxzhaiwu.ui.usermanagement.UserManagementViewModel";

      static String com_example_gxzhaiwu_ui_dashboard_DashboardViewModel = "com.example.gxzhaiwu.ui.dashboard.DashboardViewModel";

      static String com_example_gxzhaiwu_ui_auth_register_RegisterViewModel = "com.example.gxzhaiwu.ui.auth.register.RegisterViewModel";

      static String com_example_gxzhaiwu_ui_bills_BillManagementViewModel = "com.example.gxzhaiwu.ui.bills.BillManagementViewModel";

      static String com_example_gxzhaiwu_ui_management_ManagementCenterViewModel = "com.example.gxzhaiwu.ui.management.ManagementCenterViewModel";

      static String com_example_gxzhaiwu_ui_auth_login_LoginViewModel = "com.example.gxzhaiwu.ui.auth.login.LoginViewModel";

      static String com_example_gxzhaiwu_ui_payments_PaymentManagementViewModel = "com.example.gxzhaiwu.ui.payments.PaymentManagementViewModel";

      static String com_example_gxzhaiwu_ui_navigation_AuthNavigationViewModel = "com.example.gxzhaiwu.ui.navigation.AuthNavigationViewModel";

      static String com_example_gxzhaiwu_ui_home_HomeViewModel = "com.example.gxzhaiwu.ui.home.HomeViewModel";

      @KeepFieldType
      MainContainerViewModel com_example_gxzhaiwu_ui_navigation_MainContainerViewModel2;

      @KeepFieldType
      UserManagementViewModel com_example_gxzhaiwu_ui_usermanagement_UserManagementViewModel2;

      @KeepFieldType
      DashboardViewModel com_example_gxzhaiwu_ui_dashboard_DashboardViewModel2;

      @KeepFieldType
      RegisterViewModel com_example_gxzhaiwu_ui_auth_register_RegisterViewModel2;

      @KeepFieldType
      BillManagementViewModel com_example_gxzhaiwu_ui_bills_BillManagementViewModel2;

      @KeepFieldType
      ManagementCenterViewModel com_example_gxzhaiwu_ui_management_ManagementCenterViewModel2;

      @KeepFieldType
      LoginViewModel com_example_gxzhaiwu_ui_auth_login_LoginViewModel2;

      @KeepFieldType
      PaymentManagementViewModel com_example_gxzhaiwu_ui_payments_PaymentManagementViewModel2;

      @KeepFieldType
      AuthNavigationViewModel com_example_gxzhaiwu_ui_navigation_AuthNavigationViewModel2;

      @KeepFieldType
      HomeViewModel com_example_gxzhaiwu_ui_home_HomeViewModel2;
    }
  }

  private static final class ViewModelCImpl extends GxZhaiWuApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<AuthNavigationViewModel> authNavigationViewModelProvider;

    private Provider<BillManagementViewModel> billManagementViewModelProvider;

    private Provider<DashboardViewModel> dashboardViewModelProvider;

    private Provider<HomeViewModel> homeViewModelProvider;

    private Provider<LoginViewModel> loginViewModelProvider;

    private Provider<MainContainerViewModel> mainContainerViewModelProvider;

    private Provider<ManagementCenterViewModel> managementCenterViewModelProvider;

    private Provider<PaymentManagementViewModel> paymentManagementViewModelProvider;

    private Provider<RegisterViewModel> registerViewModelProvider;

    private Provider<UserManagementViewModel> userManagementViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.authNavigationViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.billManagementViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.dashboardViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.homeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.loginViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.mainContainerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.managementCenterViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.paymentManagementViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.registerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.userManagementViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
    }

    @Override
    public Map<Class<?>, javax.inject.Provider<ViewModel>> getHiltViewModelMap() {
      return LazyClassKeyMap.<javax.inject.Provider<ViewModel>>of(MapBuilder.<String, javax.inject.Provider<ViewModel>>newMapBuilder(10).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_navigation_AuthNavigationViewModel, ((Provider) authNavigationViewModelProvider)).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_bills_BillManagementViewModel, ((Provider) billManagementViewModelProvider)).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_dashboard_DashboardViewModel, ((Provider) dashboardViewModelProvider)).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_home_HomeViewModel, ((Provider) homeViewModelProvider)).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_auth_login_LoginViewModel, ((Provider) loginViewModelProvider)).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_navigation_MainContainerViewModel, ((Provider) mainContainerViewModelProvider)).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_management_ManagementCenterViewModel, ((Provider) managementCenterViewModelProvider)).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_payments_PaymentManagementViewModel, ((Provider) paymentManagementViewModelProvider)).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_auth_register_RegisterViewModel, ((Provider) registerViewModelProvider)).put(LazyClassKeyProvider.com_example_gxzhaiwu_ui_usermanagement_UserManagementViewModel, ((Provider) userManagementViewModelProvider)).build());
    }

    @Override
    public Map<Class<?>, Object> getHiltViewModelAssistedMap() {
      return Collections.<Class<?>, Object>emptyMap();
    }

    @IdentifierNameString
    private static final class LazyClassKeyProvider {
      static String com_example_gxzhaiwu_ui_dashboard_DashboardViewModel = "com.example.gxzhaiwu.ui.dashboard.DashboardViewModel";

      static String com_example_gxzhaiwu_ui_bills_BillManagementViewModel = "com.example.gxzhaiwu.ui.bills.BillManagementViewModel";

      static String com_example_gxzhaiwu_ui_auth_login_LoginViewModel = "com.example.gxzhaiwu.ui.auth.login.LoginViewModel";

      static String com_example_gxzhaiwu_ui_navigation_AuthNavigationViewModel = "com.example.gxzhaiwu.ui.navigation.AuthNavigationViewModel";

      static String com_example_gxzhaiwu_ui_home_HomeViewModel = "com.example.gxzhaiwu.ui.home.HomeViewModel";

      static String com_example_gxzhaiwu_ui_payments_PaymentManagementViewModel = "com.example.gxzhaiwu.ui.payments.PaymentManagementViewModel";

      static String com_example_gxzhaiwu_ui_usermanagement_UserManagementViewModel = "com.example.gxzhaiwu.ui.usermanagement.UserManagementViewModel";

      static String com_example_gxzhaiwu_ui_navigation_MainContainerViewModel = "com.example.gxzhaiwu.ui.navigation.MainContainerViewModel";

      static String com_example_gxzhaiwu_ui_auth_register_RegisterViewModel = "com.example.gxzhaiwu.ui.auth.register.RegisterViewModel";

      static String com_example_gxzhaiwu_ui_management_ManagementCenterViewModel = "com.example.gxzhaiwu.ui.management.ManagementCenterViewModel";

      @KeepFieldType
      DashboardViewModel com_example_gxzhaiwu_ui_dashboard_DashboardViewModel2;

      @KeepFieldType
      BillManagementViewModel com_example_gxzhaiwu_ui_bills_BillManagementViewModel2;

      @KeepFieldType
      LoginViewModel com_example_gxzhaiwu_ui_auth_login_LoginViewModel2;

      @KeepFieldType
      AuthNavigationViewModel com_example_gxzhaiwu_ui_navigation_AuthNavigationViewModel2;

      @KeepFieldType
      HomeViewModel com_example_gxzhaiwu_ui_home_HomeViewModel2;

      @KeepFieldType
      PaymentManagementViewModel com_example_gxzhaiwu_ui_payments_PaymentManagementViewModel2;

      @KeepFieldType
      UserManagementViewModel com_example_gxzhaiwu_ui_usermanagement_UserManagementViewModel2;

      @KeepFieldType
      MainContainerViewModel com_example_gxzhaiwu_ui_navigation_MainContainerViewModel2;

      @KeepFieldType
      RegisterViewModel com_example_gxzhaiwu_ui_auth_register_RegisterViewModel2;

      @KeepFieldType
      ManagementCenterViewModel com_example_gxzhaiwu_ui_management_ManagementCenterViewModel2;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.gxzhaiwu.ui.navigation.AuthNavigationViewModel 
          return (T) new AuthNavigationViewModel(singletonCImpl.authRepositoryImplProvider.get());

          case 1: // com.example.gxzhaiwu.ui.bills.BillManagementViewModel 
          return (T) new BillManagementViewModel();

          case 2: // com.example.gxzhaiwu.ui.dashboard.DashboardViewModel 
          return (T) new DashboardViewModel(singletonCImpl.dashboardRepositoryImplProvider.get(), singletonCImpl.authRepositoryImplProvider.get());

          case 3: // com.example.gxzhaiwu.ui.home.HomeViewModel 
          return (T) new HomeViewModel(singletonCImpl.authRepositoryImplProvider.get());

          case 4: // com.example.gxzhaiwu.ui.auth.login.LoginViewModel 
          return (T) new LoginViewModel(singletonCImpl.authRepositoryImplProvider.get());

          case 5: // com.example.gxzhaiwu.ui.navigation.MainContainerViewModel 
          return (T) new MainContainerViewModel();

          case 6: // com.example.gxzhaiwu.ui.management.ManagementCenterViewModel 
          return (T) new ManagementCenterViewModel();

          case 7: // com.example.gxzhaiwu.ui.payments.PaymentManagementViewModel 
          return (T) new PaymentManagementViewModel();

          case 8: // com.example.gxzhaiwu.ui.auth.register.RegisterViewModel 
          return (T) new RegisterViewModel(singletonCImpl.authRepositoryImplProvider.get());

          case 9: // com.example.gxzhaiwu.ui.usermanagement.UserManagementViewModel 
          return (T) new UserManagementViewModel(singletonCImpl.userManagementRepositoryImplProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends GxZhaiWuApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends GxZhaiWuApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends GxZhaiWuApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<HttpLoggingInterceptor> provideHttpLoggingInterceptorProvider;

    private Provider<OkHttpClient> provideOkHttpClientProvider;

    private Provider<Json> provideJsonProvider;

    private Provider<Retrofit> provideRetrofitProvider;

    private Provider<AuthApi> provideAuthApiProvider;

    private Provider<DataStore<Preferences>> provideDataStoreProvider;

    private Provider<UserPreferences> userPreferencesProvider;

    private Provider<AuthRepositoryImpl> authRepositoryImplProvider;

    private Provider<DashboardApi> provideDashboardApiProvider;

    private Provider<DashboardRepositoryImpl> dashboardRepositoryImplProvider;

    private Provider<UserManagementApi> provideUserManagementApiProvider;

    private Provider<UserManagementRepositoryImpl> userManagementRepositoryImplProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideHttpLoggingInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<HttpLoggingInterceptor>(singletonCImpl, 4));
      this.provideOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 3));
      this.provideJsonProvider = DoubleCheck.provider(new SwitchingProvider<Json>(singletonCImpl, 5));
      this.provideRetrofitProvider = DoubleCheck.provider(new SwitchingProvider<Retrofit>(singletonCImpl, 2));
      this.provideAuthApiProvider = DoubleCheck.provider(new SwitchingProvider<AuthApi>(singletonCImpl, 1));
      this.provideDataStoreProvider = DoubleCheck.provider(new SwitchingProvider<DataStore<Preferences>>(singletonCImpl, 7));
      this.userPreferencesProvider = DoubleCheck.provider(new SwitchingProvider<UserPreferences>(singletonCImpl, 6));
      this.authRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<AuthRepositoryImpl>(singletonCImpl, 0));
      this.provideDashboardApiProvider = DoubleCheck.provider(new SwitchingProvider<DashboardApi>(singletonCImpl, 9));
      this.dashboardRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<DashboardRepositoryImpl>(singletonCImpl, 8));
      this.provideUserManagementApiProvider = DoubleCheck.provider(new SwitchingProvider<UserManagementApi>(singletonCImpl, 11));
      this.userManagementRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<UserManagementRepositoryImpl>(singletonCImpl, 10));
    }

    @Override
    public void injectGxZhaiWuApplication(GxZhaiWuApplication arg0) {
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return Collections.<Boolean>emptySet();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.gxzhaiwu.data.repository.AuthRepositoryImpl 
          return (T) new AuthRepositoryImpl(singletonCImpl.provideAuthApiProvider.get(), singletonCImpl.userPreferencesProvider.get());

          case 1: // com.example.gxzhaiwu.data.api.AuthApi 
          return (T) NetworkModule_ProvideAuthApiFactory.provideAuthApi(singletonCImpl.provideRetrofitProvider.get());

          case 2: // retrofit2.Retrofit 
          return (T) NetworkModule_ProvideRetrofitFactory.provideRetrofit(singletonCImpl.provideOkHttpClientProvider.get(), singletonCImpl.provideJsonProvider.get());

          case 3: // okhttp3.OkHttpClient 
          return (T) NetworkModule_ProvideOkHttpClientFactory.provideOkHttpClient(singletonCImpl.provideHttpLoggingInterceptorProvider.get());

          case 4: // okhttp3.logging.HttpLoggingInterceptor 
          return (T) NetworkModule_ProvideHttpLoggingInterceptorFactory.provideHttpLoggingInterceptor();

          case 5: // kotlinx.serialization.json.Json 
          return (T) NetworkModule_ProvideJsonFactory.provideJson();

          case 6: // com.example.gxzhaiwu.data.local.UserPreferences 
          return (T) new UserPreferences(singletonCImpl.provideDataStoreProvider.get());

          case 7: // androidx.datastore.core.DataStore<androidx.datastore.preferences.core.Preferences> 
          return (T) PreferencesModule_ProvideDataStoreFactory.provideDataStore(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 8: // com.example.gxzhaiwu.data.repository.DashboardRepositoryImpl 
          return (T) new DashboardRepositoryImpl(singletonCImpl.provideDashboardApiProvider.get(), singletonCImpl.userPreferencesProvider.get());

          case 9: // com.example.gxzhaiwu.data.api.DashboardApi 
          return (T) NetworkModule_ProvideDashboardApiFactory.provideDashboardApi(singletonCImpl.provideRetrofitProvider.get());

          case 10: // com.example.gxzhaiwu.data.repository.UserManagementRepositoryImpl 
          return (T) new UserManagementRepositoryImpl(singletonCImpl.provideUserManagementApiProvider.get(), singletonCImpl.userPreferencesProvider.get());

          case 11: // com.example.gxzhaiwu.data.api.UserManagementApi 
          return (T) NetworkModule_ProvideUserManagementApiFactory.provideUserManagementApi(singletonCImpl.provideRetrofitProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
