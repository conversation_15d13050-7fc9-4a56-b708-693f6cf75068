package com.example.gxzhaiwu.data.model

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 账单数据模型
 * 对应API文档中的账单相关接口
 */
data class Bill(
    val id: Int,
    val billNumber: String,                    // 账单编号
    val customerId: Int,                       // 客户ID
    val customerName: String,                  // 客户姓名
    val storeId: Int,                         // 门店ID
    val storeName: String,                    // 门店名称
    val totalAmount: BigDecimal,              // 总金额
    val paidAmount: BigDecimal,               // 已付金额
    val remainingAmount: BigDecimal,          // 剩余金额
    val status: BillStatus,                   // 账单状态
    val dueDate: LocalDateTime?,              // 到期日期
    val createdAt: LocalDateTime,             // 创建时间
    val updatedAt: LocalDateTime,             // 更新时间
    val description: String? = null,          // 账单描述
    val items: List<BillItem> = emptyList()   // 账单项目
) {
    /**
     * 计算付款进度百分比
     */
    fun getPaymentProgress(): Float {
        return if (totalAmount > BigDecimal.ZERO) {
            (paidAmount.toFloat() / totalAmount.toFloat()).coerceIn(0f, 1f)
        } else {
            0f
        }
    }

    /**
     * 检查是否已完全付清
     */
    fun isFullyPaid(): Boolean {
        return remainingAmount <= BigDecimal.ZERO
    }

    /**
     * 检查是否逾期
     */
    fun isOverdue(): Boolean {
        return dueDate?.let { due ->
            LocalDateTime.now().isAfter(due) && !isFullyPaid()
        } ?: false
    }

    /**
     * 获取状态显示文本
     */
    fun getStatusDisplayText(): String {
        return status.displayName
    }

    /**
     * 获取状态颜色标识
     */
    fun getStatusColorKey(): String {
        return when (status) {
            BillStatus.PENDING -> "warning"
            BillStatus.PARTIAL_PAID -> "info"
            BillStatus.PAID -> "success"
            BillStatus.OVERDUE -> "error"
            BillStatus.CANCELLED -> "disabled"
        }
    }
}

/**
 * 账单状态枚举
 */
enum class BillStatus(val value: String, val displayName: String) {
    PENDING("pending", "待付款"),
    PARTIAL_PAID("partial_paid", "部分付款"),
    PAID("paid", "已付清"),
    OVERDUE("overdue", "逾期"),
    CANCELLED("cancelled", "已取消");

    companion object {
        fun fromValue(value: String): BillStatus {
            return values().find { it.value == value } ?: PENDING
        }

        fun getActiveStatuses(): List<BillStatus> {
            return listOf(PENDING, PARTIAL_PAID, OVERDUE)
        }
    }
}

/**
 * 账单项目
 */
data class BillItem(
    val id: Int,
    val billId: Int,
    val productName: String,                  // 商品名称
    val quantity: Int,                        // 数量
    val unitPrice: BigDecimal,                // 单价
    val totalPrice: BigDecimal,               // 小计
    val description: String? = null           // 项目描述
) {
    /**
     * 计算小计（数量 × 单价）
     */
    fun calculateTotal(): BigDecimal {
        return unitPrice.multiply(BigDecimal(quantity))
    }
}

/**
 * 创建账单请求
 */
data class CreateBillRequest(
    val customerId: Int,
    val storeId: Int,
    val totalAmount: BigDecimal,
    val dueDate: LocalDateTime? = null,
    val description: String? = null,
    val items: List<CreateBillItemRequest> = emptyList()
)

/**
 * 创建账单项目请求
 */
data class CreateBillItemRequest(
    val productName: String,
    val quantity: Int,
    val unitPrice: BigDecimal,
    val description: String? = null
)

/**
 * 更新账单请求
 */
data class UpdateBillRequest(
    val totalAmount: BigDecimal? = null,
    val dueDate: LocalDateTime? = null,
    val description: String? = null,
    val status: BillStatus? = null
)

/**
 * 账单查询参数
 */
data class BillQueryParams(
    val customerId: Int? = null,
    val storeId: Int? = null,
    val status: BillStatus? = null,
    val startDate: LocalDateTime? = null,
    val endDate: LocalDateTime? = null,
    val page: Int = 1,
    val pageSize: Int = 20,
    val sortBy: String = "created_at",
    val sortOrder: String = "desc"
)

/**
 * 账单统计信息
 */
data class BillStatistics(
    val totalBills: Int,
    val totalAmount: BigDecimal,
    val paidAmount: BigDecimal,
    val pendingAmount: BigDecimal,
    val overdueAmount: BigDecimal,
    val overdueCount: Int
) {
    /**
     * 计算付款率
     */
    fun getPaymentRate(): Float {
        return if (totalAmount > BigDecimal.ZERO) {
            (paidAmount.toFloat() / totalAmount.toFloat()).coerceIn(0f, 1f)
        } else {
            0f
        }
    }

    /**
     * 计算逾期率
     */
    fun getOverdueRate(): Float {
        return if (totalBills > 0) {
            (overdueCount.toFloat() / totalBills.toFloat()).coerceIn(0f, 1f)
        } else {
            0f
        }
    }
}
