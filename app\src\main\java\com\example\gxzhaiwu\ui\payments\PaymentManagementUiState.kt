package com.example.gxzhaiwu.ui.payments

import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.ui.bills.DateRange
import com.example.gxzhaiwu.ui.bills.AmountRange

/**
 * 还款管理UI状态
 */
data class PaymentManagementUiState(
    val payments: List<Payment> = emptyList(),
    val filteredPayments: List<Payment> = emptyList(),
    val statistics: PaymentStatistics? = null,
    val selectedPayment: Payment? = null,
    val currentFilter: PaymentFilter = PaymentFilter(),
    val searchQuery: String = "",
    val viewMode: PaymentViewMode = PaymentViewMode.LIST,
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val hasError: Boolean = false,
    val errorMessage: String? = null,
    val hasMoreData: Boolean = true,
    val currentPage: Int = 1,
    val userRoles: List<String> = emptyList()
) {
    /**
     * 检查是否有数据
     */
    fun hasData(): Boolean = payments.isNotEmpty()

    /**
     * 检查是否为空状态
     */
    fun isEmpty(): Boolean = !isLoading && payments.isEmpty()

    /**
     * 检查是否显示空搜索结果
     */
    fun isEmptySearch(): Boolean = !isLoading && searchQuery.isNotEmpty() && filteredPayments.isEmpty()

    /**
     * 获取显示的还款列表
     */
    fun getDisplayPayments(): List<Payment> {
        return if (searchQuery.isNotEmpty() || currentFilter.hasActiveFilter()) {
            filteredPayments
        } else {
            payments
        }
    }

    /**
     * 检查用户是否有记录还款权限
     */
    fun canRecordPayment(): Boolean {
        return com.example.gxzhaiwu.utils.RoleUtils.hasPermission(userRoles, com.example.gxzhaiwu.utils.Permission.RECORD_PAYMENT)
    }

    /**
     * 检查用户是否有确认还款权限
     */
    fun canConfirmPayment(): Boolean {
        return com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles) || 
               com.example.gxzhaiwu.utils.RoleUtils.isStoreOwner(userRoles)
    }

    /**
     * 检查用户是否有退款权限
     */
    fun canRefundPayment(): Boolean {
        return com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles)
    }

    /**
     * 检查用户是否有删除权限
     */
    fun canDeletePayment(): Boolean {
        return com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles)
    }
}

/**
 * 还款过滤器
 */
data class PaymentFilter(
    val status: PaymentStatus? = null,
    val paymentMethod: PaymentMethod? = null,
    val storeId: Int? = null,
    val customerId: Int? = null,
    val billId: Int? = null,
    val dateRange: DateRange? = null,
    val amountRange: AmountRange? = null
) {
    /**
     * 检查是否有激活的过滤条件
     */
    fun hasActiveFilter(): Boolean {
        return status != null || paymentMethod != null || storeId != null || 
               customerId != null || billId != null || dateRange != null || amountRange != null
    }

    /**
     * 获取激活的过滤条件数量
     */
    fun getActiveFilterCount(): Int {
        var count = 0
        if (status != null) count++
        if (paymentMethod != null) count++
        if (storeId != null) count++
        if (customerId != null) count++
        if (billId != null) count++
        if (dateRange != null) count++
        if (amountRange != null) count++
        return count
    }

    /**
     * 清除所有过滤条件
     */
    fun clear(): PaymentFilter {
        return PaymentFilter()
    }
}

/**
 * 还款查看模式
 */
enum class PaymentViewMode(val displayName: String) {
    LIST("列表视图"),
    SUMMARY("汇总视图"),
    CALENDAR("日历视图");

    companion object {
        fun getDefault(): PaymentViewMode = LIST
    }
}

/**
 * 还款管理副作用
 */
sealed class PaymentManagementSideEffect {
    data class ShowError(val message: String) : PaymentManagementSideEffect()
    data class ShowSuccess(val message: String) : PaymentManagementSideEffect()
    data class NavigateToPaymentDetail(val paymentId: Int) : PaymentManagementSideEffect()
    data class NavigateToRecordPayment(val billId: Int? = null) : PaymentManagementSideEffect()
    data class NavigateToEditPayment(val paymentId: Int) : PaymentManagementSideEffect()
    data class NavigateToBillDetail(val billId: Int) : PaymentManagementSideEffect()
    object NavigateBack : PaymentManagementSideEffect()
    data class ShowConfirmDialog(val message: String, val action: () -> Unit) : PaymentManagementSideEffect()
}

/**
 * 还款排序选项
 */
enum class PaymentSortOption(val displayName: String, val sortBy: String, val sortOrder: String) {
    DATE_DESC("最新还款", "payment_date", "desc"),
    DATE_ASC("最早还款", "payment_date", "asc"),
    AMOUNT_DESC("金额从高到低", "amount", "desc"),
    AMOUNT_ASC("金额从低到高", "amount", "asc"),
    STATUS("状态", "status", "asc"),
    CUSTOMER("客户", "customer_name", "asc");

    companion object {
        fun getDefault(): PaymentSortOption = DATE_DESC
    }
}

/**
 * 还款操作类型
 */
enum class PaymentAction(val displayName: String) {
    VIEW("查看详情"),
    EDIT("编辑"),
    DELETE("删除"),
    CONFIRM("确认还款"),
    CANCEL("取消还款"),
    REFUND("申请退款"),
    VIEW_BILL("查看账单"),
    DUPLICATE("复制记录"),
    EXPORT("导出");

    companion object {
        fun getAvailableActions(userRoles: List<String>, payment: Payment): List<PaymentAction> {
            val actions = mutableListOf<PaymentAction>()
            
            // 所有用户都可以查看
            actions.add(VIEW)
            actions.add(VIEW_BILL)
            
            // 根据状态和权限添加操作
            when (payment.status) {
                PaymentStatus.PENDING -> {
                    // 管理员和店长可以确认和取消
                    if (com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles) || 
                        com.example.gxzhaiwu.utils.RoleUtils.isStoreOwner(userRoles)) {
                        actions.add(CONFIRM)
                        actions.add(CANCEL)
                        actions.add(EDIT)
                    }
                }
                PaymentStatus.CONFIRMED -> {
                    // 只有管理员可以退款
                    if (com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles)) {
                        actions.add(REFUND)
                    }
                }
                PaymentStatus.CANCELLED, PaymentStatus.REFUNDED -> {
                    // 已取消或已退款的记录只能查看
                }
            }
            
            // 管理员和店长可以编辑和复制
            if (com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles) || 
                com.example.gxzhaiwu.utils.RoleUtils.isStoreOwner(userRoles)) {
                if (payment.status != PaymentStatus.CANCELLED && payment.status != PaymentStatus.REFUNDED) {
                    if (!actions.contains(EDIT)) {
                        actions.add(EDIT)
                    }
                }
                actions.add(DUPLICATE)
            }
            
            // 只有管理员可以删除
            if (com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles)) {
                actions.add(DELETE)
            }
            
            // 所有用户都可以导出
            actions.add(EXPORT)
            
            return actions
        }
    }
}

/**
 * 还款统计时间范围
 */
enum class PaymentStatisticsPeriod(val displayName: String, val days: Int) {
    TODAY("今日", 1),
    WEEK("本周", 7),
    MONTH("本月", 30),
    QUARTER("本季度", 90),
    YEAR("本年", 365),
    ALL("全部", -1);

    companion object {
        fun getDefault(): PaymentStatisticsPeriod = MONTH
    }
}

/**
 * 批量操作类型
 */
enum class BatchPaymentAction(val displayName: String) {
    CONFIRM("批量确认"),
    CANCEL("批量取消"),
    EXPORT("批量导出"),
    DELETE("批量删除");

    companion object {
        fun getAvailableActions(userRoles: List<String>): List<BatchPaymentAction> {
            val actions = mutableListOf<BatchPaymentAction>()
            
            // 管理员和店长可以批量确认和取消
            if (com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles) || 
                com.example.gxzhaiwu.utils.RoleUtils.isStoreOwner(userRoles)) {
                actions.add(CONFIRM)
                actions.add(CANCEL)
            }
            
            // 所有用户都可以批量导出
            actions.add(EXPORT)
            
            // 只有管理员可以批量删除
            if (com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles)) {
                actions.add(DELETE)
            }
            
            return actions
        }
    }
}
