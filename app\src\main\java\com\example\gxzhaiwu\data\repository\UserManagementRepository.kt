package com.example.gxzhaiwu.data.repository

import com.example.gxzhaiwu.data.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 用户管理Repository接口
 * 定义用户管理相关的数据操作接口
 */
interface UserManagementRepository {

    /**
     * 获取用户列表
     */
    suspend fun getUsers(
        search: String? = null,
        role: String? = null,
        page: Int = 1,
        perPage: Int = 15
    ): Result<PaginatedUserData>

    /**
     * 获取用户详情
     */
    suspend fun getUserDetail(userId: Int): Result<UserDetail>

    /**
     * 更新用户角色
     */
    suspend fun updateUserRoles(userId: Int, roleIds: List<Int>): Result<UserDetail>

    /**
     * 更新用户门店权限
     */
    suspend fun updateUserStores(userId: Int, storeIds: List<Int>): Result<UserDetail>

    /**
     * 获取角色列表
     */
    suspend fun getRoles(): Result<List<Role>>

    /**
     * 获取门店列表
     */
    suspend fun getStores(): Result<List<com.example.gxzhaiwu.data.api.Store>>

    /**
     * 搜索用户（本地缓存搜索）
     */
    fun searchUsers(query: String): Flow<List<UserDetail>>

    /**
     * 获取缓存的用户列表
     */
    fun getCachedUsers(): Flow<List<UserDetail>>

    /**
     * 清除缓存
     */
    suspend fun clearCache()
}
