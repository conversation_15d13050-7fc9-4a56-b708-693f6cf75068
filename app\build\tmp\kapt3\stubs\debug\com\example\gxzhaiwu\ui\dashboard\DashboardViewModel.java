package com.example.gxzhaiwu.ui.dashboard;

/**
 * 仪表盘ViewModel
 * 管理仪表盘页面的业务逻辑和状态
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\b\u0010\u0015\u001a\u00020\u0016H\u0002J\u0016\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00190\u00182\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u001a\u0010\u001c\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\b\b\u0002\u0010\u001f\u001a\u00020 H\u0002J\u0010\u0010!\u001a\u00020\u00162\u0006\u0010\"\u001a\u00020#H\u0002J\u0010\u0010$\u001a\u00020\u00162\u0006\u0010%\u001a\u00020\u0019H\u0002J\b\u0010&\u001a\u00020\u0016H\u0002J\u000e\u0010\'\u001a\u00020\u00162\u0006\u0010(\u001a\u00020)J\b\u0010*\u001a\u00020\u0016H\u0002J\u0012\u0010+\u001a\u00020\u00162\b\u0010,\u001a\u0004\u0018\u00010-H\u0002J\u0010\u0010.\u001a\u00020\u00162\u0006\u0010/\u001a\u00020 H\u0002J\u0016\u00100\u001a\u00020\u00162\f\u00101\u001a\b\u0012\u0004\u0012\u00020\u001e0\u0018H\u0002R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\f0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u00062"}, d2 = {"Lcom/example/gxzhaiwu/ui/dashboard/DashboardViewModel;", "Landroidx/lifecycle/ViewModel;", "dashboardRepository", "Lcom/example/gxzhaiwu/data/repository/DashboardRepository;", "authRepository", "Lcom/example/gxzhaiwu/data/repository/AuthRepository;", "(Lcom/example/gxzhaiwu/data/repository/DashboardRepository;Lcom/example/gxzhaiwu/data/repository/AuthRepository;)V", "_sideEffect", "Lkotlinx/coroutines/flow/MutableSharedFlow;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardSideEffect;", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardUiState;", "sideEffect", "Lkotlinx/coroutines/flow/SharedFlow;", "getSideEffect", "()Lkotlinx/coroutines/flow/SharedFlow;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "clearError", "", "createStatisticsCards", "", "Lcom/example/gxzhaiwu/data/model/StatisticsCardData;", "overview", "Lcom/example/gxzhaiwu/data/model/DashboardOverview;", "handleError", "message", "", "isMinor", "", "handleQuickAction", "action", "Lcom/example/gxzhaiwu/data/model/QuickActionData;", "handleStatisticsCardClick", "card", "loadDashboardData", "onEvent", "event", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent;", "refreshDashboardData", "selectDateRange", "dateRange", "Lcom/example/gxzhaiwu/ui/dashboard/DateRange;", "toggleAdvancedStatistics", "show", "updateUserPermissions", "roles", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class DashboardViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.repository.DashboardRepository dashboardRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.gxzhaiwu.data.repository.AuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.gxzhaiwu.ui.dashboard.DashboardUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.gxzhaiwu.ui.dashboard.DashboardUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableSharedFlow<com.example.gxzhaiwu.ui.dashboard.DashboardSideEffect> _sideEffect = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.SharedFlow<com.example.gxzhaiwu.ui.dashboard.DashboardSideEffect> sideEffect = null;
    
    @javax.inject.Inject()
    public DashboardViewModel(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.repository.DashboardRepository dashboardRepository, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.repository.AuthRepository authRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.gxzhaiwu.ui.dashboard.DashboardUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.SharedFlow<com.example.gxzhaiwu.ui.dashboard.DashboardSideEffect> getSideEffect() {
        return null;
    }
    
    /**
     * 处理仪表盘事件
     */
    public final void onEvent(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.ui.dashboard.DashboardEvent event) {
    }
    
    /**
     * 加载仪表盘数据
     */
    private final void loadDashboardData() {
    }
    
    /**
     * 刷新仪表盘数据
     */
    private final void refreshDashboardData() {
    }
    
    /**
     * 更新用户权限
     */
    private final void updateUserPermissions(java.util.List<java.lang.String> roles) {
    }
    
    /**
     * 创建统计卡片数据
     */
    private final java.util.List<com.example.gxzhaiwu.data.model.StatisticsCardData> createStatisticsCards(com.example.gxzhaiwu.data.model.DashboardOverview overview) {
        return null;
    }
    
    /**
     * 处理错误
     */
    private final void handleError(java.lang.String message, boolean isMinor) {
    }
    
    /**
     * 清除错误状态
     */
    private final void clearError() {
    }
    
    /**
     * 选择日期范围
     */
    private final void selectDateRange(com.example.gxzhaiwu.ui.dashboard.DateRange dateRange) {
    }
    
    /**
     * 切换高级统计显示
     */
    private final void toggleAdvancedStatistics(boolean show) {
    }
    
    /**
     * 处理快速操作点击
     */
    private final void handleQuickAction(com.example.gxzhaiwu.data.model.QuickActionData action) {
    }
    
    /**
     * 处理统计卡片点击
     */
    private final void handleStatisticsCardClick(com.example.gxzhaiwu.data.model.StatisticsCardData card) {
    }
}