package com.example.gxzhaiwu.ui.navigation;

import com.example.gxzhaiwu.data.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AuthNavigationViewModel_Factory implements Factory<AuthNavigationViewModel> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public AuthNavigationViewModel_Factory(Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public AuthNavigationViewModel get() {
    return newInstance(authRepositoryProvider.get());
  }

  public static AuthNavigationViewModel_Factory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new AuthNavigationViewModel_Factory(authRepositoryProvider);
  }

  public static AuthNavigationViewModel newInstance(AuthRepository authRepository) {
    return new AuthNavigationViewModel(authRepository);
  }
}
