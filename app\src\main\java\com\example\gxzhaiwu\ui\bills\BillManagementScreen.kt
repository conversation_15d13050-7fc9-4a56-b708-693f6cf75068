package com.example.gxzhaiwu.ui.bills

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.ui.bills.components.*
import com.example.gxzhaiwu.ui.components.*
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme

/**
 * 账单管理主屏幕
 * 显示账单列表、搜索、过滤和操作功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BillManagementScreen(
    onNavigateToScreen: (String) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: BillManagementViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val snackbarHostState = remember { SnackbarHostState() }

    // 处理副作用
    LaunchedEffect(viewModel) {
        viewModel.sideEffect.collect { effect ->
            when (effect) {
                is BillManagementSideEffect.ShowError -> {
                    snackbarHostState.showSnackbar(
                        message = effect.message,
                        actionLabel = "确定"
                    )
                }
                is BillManagementSideEffect.ShowSuccess -> {
                    snackbarHostState.showSnackbar(effect.message)
                }
                is BillManagementSideEffect.NavigateToBillDetail -> {
                    onNavigateToScreen("bill_detail/${effect.billId}")
                }
                is BillManagementSideEffect.NavigateToCreateBill -> {
                    val route = if (effect.customerId != null) {
                        "create_bill?customerId=${effect.customerId}"
                    } else {
                        "create_bill"
                    }
                    onNavigateToScreen(route)
                }
                is BillManagementSideEffect.NavigateToEditBill -> {
                    onNavigateToScreen("edit_bill/${effect.billId}")
                }
                is BillManagementSideEffect.NavigateBack -> {
                    // 处理返回导航
                }
            }
        }
    }

    Scaffold(
        topBar = {
            BillManagementTopBar(
                searchQuery = uiState.searchQuery,
                onSearchQueryChange = viewModel::searchBills,
                currentFilter = uiState.currentFilter,
                onFilterChange = viewModel::applyFilter,
                onClearFilters = viewModel::clearFilters
            )
        },
        floatingActionButton = {
            if (uiState.canCreateBill()) {
                FloatingActionButton(
                    onClick = { viewModel.createBill() },
                    containerColor = MaterialTheme.colorScheme.primary
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "创建账单"
                    )
                }
            }
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        modifier = modifier.fillMaxSize()
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 统计信息卡片
            uiState.statistics?.let { statistics ->
                BillStatisticsCard(
                    statistics = statistics,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }

            // 账单列表
            when {
                uiState.isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                
                uiState.isEmpty() -> {
                    BillEmptyState(
                        onCreateBill = { viewModel.createBill() },
                        canCreateBill = uiState.canCreateBill(),
                        modifier = Modifier.fillMaxSize()
                    )
                }
                
                uiState.isEmptySearch() -> {
                    BillEmptySearchState(
                        searchQuery = uiState.searchQuery,
                        onClearSearch = { viewModel.searchBills("") },
                        modifier = Modifier.fillMaxSize()
                    )
                }
                
                else -> {
                    RefreshableContent(
                        isRefreshing = uiState.isRefreshing,
                        onRefresh = { viewModel.loadBills(refresh = true) }
                    ) {
                        LazyColumn(
                            contentPadding = PaddingValues(16.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier.fillMaxSize()
                        ) {
                            items(
                                items = uiState.getDisplayBills(),
                                key = { it.id }
                            ) { bill ->
                                BillCard(
                                    bill = bill,
                                    availableActions = BillAction.getAvailableActions(uiState.userRoles, bill),
                                    onBillClick = { viewModel.selectBill(bill) },
                                    onActionClick = { action -> 
                                        viewModel.handleBillAction(action, bill) 
                                    },
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }

                            // 加载更多指示器
                            if (uiState.hasMoreData && !uiState.isLoading) {
                                item {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(16.dp),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Button(
                                            onClick = { viewModel.loadBills() }
                                        ) {
                                            Text("加载更多")
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 错误处理
    if (uiState.hasError) {
        LaunchedEffect(uiState.errorMessage) {
            uiState.errorMessage?.let { message ->
                snackbarHostState.showSnackbar(
                    message = message,
                    actionLabel = "重试"
                )
            }
            viewModel.clearError()
        }
    }
}

// 预览组件
@Preview(name = "账单管理屏幕")
@Composable
private fun BillManagementScreenPreview() {
    GxZhaiWuTheme {
        Surface {
            BillManagementScreen(
                onNavigateToScreen = {}
            )
        }
    }
}
