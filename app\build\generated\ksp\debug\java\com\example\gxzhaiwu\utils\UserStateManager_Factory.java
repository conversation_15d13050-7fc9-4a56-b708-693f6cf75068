package com.example.gxzhaiwu.utils;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class UserStateManager_Factory implements Factory<UserStateManager> {
  @Override
  public UserStateManager get() {
    return newInstance();
  }

  public static UserStateManager_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static UserStateManager newInstance() {
    return new UserStateManager();
  }

  private static final class InstanceHolder {
    private static final UserStateManager_Factory INSTANCE = new UserStateManager_Factory();
  }
}
