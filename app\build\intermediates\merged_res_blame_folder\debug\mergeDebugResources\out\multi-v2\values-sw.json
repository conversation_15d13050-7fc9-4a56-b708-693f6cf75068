{"logs": [{"outputFile": "com.example.gxzhaiwu.app-mergeDebugResources-61:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e7a34afc2b6fd3a244eccb13c4cf553\\transformed\\ui-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,280,381,482,568,649,750,841,923,1008,1095,1169,1244,1321,1398,1475,1545", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,74,76,76,76,69,120", "endOffsets": "194,275,376,477,563,644,745,836,918,1003,1090,1164,1239,1316,1393,1470,1540,1661"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,79,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "828,922,1003,1104,1205,1291,1372,7618,7709,7791,7876,7963,8037,8112,8189,8367,8444,8514", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,74,76,76,76,69,120", "endOffsets": "917,998,1099,1200,1286,1367,1468,7704,7786,7871,7958,8032,8107,8184,8261,8439,8509,8630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca95cc56ae6c869d643262611d469661\\transformed\\core-1.16.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,80", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,499,606,713,8266", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "194,296,393,494,601,708,823,8362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98f289bf6c45d262a7223b262b65f304\\transformed\\foundation-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "84,85", "startColumns": "4,4", "startOffsets": "8635,8736", "endColumns": "100,102", "endOffsets": "8731,8834"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\34672be5908381f92b2f1efab7609872\\transformed\\material3-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,402,516,616,715,831,972,1088,1239,1325,1425,1518,1620,1738,1865,1970,2100,2229,2365,2530,2659,2783,2912,3021,3115,3211,3334,3462,3559,3671,3781,3913,4054,4166,4266,4345,4441,4538,4625,4710,4824,4904,4987,5086,5186,5281,5380,5468,5573,5673,5776,5892,5972,6090", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "166,281,397,511,611,710,826,967,1083,1234,1320,1420,1513,1615,1733,1860,1965,2095,2224,2360,2525,2654,2778,2907,3016,3110,3206,3329,3457,3554,3666,3776,3908,4049,4161,4261,4340,4436,4533,4620,4705,4819,4899,4982,5081,5181,5276,5375,5463,5568,5668,5771,5887,5967,6085,6195"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1473,1589,1704,1820,1934,2034,2133,2249,2390,2506,2657,2743,2843,2936,3038,3156,3283,3388,3518,3647,3783,3948,4077,4201,4330,4439,4533,4629,4752,4880,4977,5089,5199,5331,5472,5584,5684,5763,5859,5956,6043,6128,6242,6322,6405,6504,6604,6699,6798,6886,6991,7091,7194,7310,7390,7508", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "1584,1699,1815,1929,2029,2128,2244,2385,2501,2652,2738,2838,2931,3033,3151,3278,3383,3513,3642,3778,3943,4072,4196,4325,4434,4528,4624,4747,4875,4972,5084,5194,5326,5467,5579,5679,5758,5854,5951,6038,6123,6237,6317,6400,6499,6599,6694,6793,6881,6986,7086,7189,7305,7385,7503,7613"}}]}]}