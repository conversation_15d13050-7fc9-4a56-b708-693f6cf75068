package com.example.gxzhaiwu.ui.usermanagement

import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.data.repository.UserManagementRepository
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * 用户管理ViewModel单元测试
 */
@OptIn(ExperimentalCoroutinesApi::class)
class UserManagementViewModelTest {

    private lateinit var viewModel: UserManagementViewModel
    private lateinit var repository: UserManagementRepository
    private val testDispatcher = StandardTestDispatcher()

    // 测试数据
    private val testUsers = listOf(
        User(
            id = 1,
            name = "张三",
            username = "zhang<PERSON>",
            email = "<EMAIL>",
            created_at = "2024-01-01T00:00:00.000000Z",
            updated_at = "2024-01-01T00:00:00.000000Z",
            roles = listOf(
                Role(id = 1, name = "系统管理员", slug = "admin")
            )
        ),
        User(
            id = 2,
            name = "李四",
            username = "lisi",
            email = "<EMAIL>",
            created_at = "2024-01-02T00:00:00.000000Z",
            updated_at = "2024-01-02T00:00:00.000000Z",
            roles = listOf(
                Role(id = 2, name = "店长", slug = "store_owner")
            )
        )
    )

    private val testRoles = listOf(
        Role(id = 1, name = "系统管理员", slug = "admin", is_system = true),
        Role(id = 2, name = "店长", slug = "store_owner", is_system = false),
        Role(id = 3, name = "店员", slug = "store_staff", is_system = false)
    )

    private val testPaginatedData = PaginatedUserData(
        current_page = 1,
        data = testUsers,
        first_page_url = "http://example.com/api/users?page=1",
        from = 1,
        last_page = 1,
        last_page_url = "http://example.com/api/users?page=1",
        next_page_url = null,
        path = "http://example.com/api/users",
        per_page = 15,
        prev_page_url = null,
        to = 2,
        total = 2
    )

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        repository = mockk()
        
        // 默认mock行为
        coEvery { repository.getRoles() } returns Result.success(testRoles)
        coEvery { repository.getUsers(any(), any(), any(), any()) } returns Result.success(testPaginatedData)
        
        viewModel = UserManagementViewModel(repository)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
        clearAllMocks()
    }

    @Test
    fun `初始化时应该加载角色和用户数据`() = runTest {
        // 推进协程执行
        testScheduler.advanceUntilIdle()

        // 验证repository调用
        coVerify { repository.getRoles() }
        coVerify { repository.getUsers(null, null, 1, 15) }

        // 验证UI状态
        val uiState = viewModel.uiState.first()
        assertEquals(testRoles, uiState.roles)
        assertEquals(testUsers, uiState.users)
        assertEquals(1, uiState.currentPage)
        assertEquals(1, uiState.totalPages)
        assertEquals(2, uiState.totalUsers)
        assertFalse(uiState.isLoading)
        assertNull(uiState.error)
    }

    @Test
    fun `搜索用户应该更新筛选条件并重新加载数据`() = runTest {
        val searchQuery = "张三"
        
        // 执行搜索
        viewModel.searchUsers(searchQuery)
        testScheduler.advanceUntilIdle()

        // 验证repository调用
        coVerify { repository.getUsers(searchQuery, null, 1, 15) }

        // 验证UI状态
        val uiState = viewModel.uiState.first()
        assertEquals(searchQuery, uiState.searchQuery)
    }

    @Test
    fun `角色筛选应该更新筛选条件并重新加载数据`() = runTest {
        val roleFilter = "admin"
        
        // 执行角色筛选
        viewModel.filterByRole(roleFilter)
        testScheduler.advanceUntilIdle()

        // 验证repository调用
        coVerify { repository.getUsers(null, roleFilter, 1, 15) }

        // 验证UI状态
        val uiState = viewModel.uiState.first()
        assertEquals(roleFilter, uiState.selectedRole)
    }

    @Test
    fun `更新用户角色成功应该更新UI状态`() = runTest {
        val userId = 1
        val roleIds = listOf(2, 3)
        val updatedUser = testUsers[0].copy(
            roles = listOf(
                Role(id = 2, name = "店长", slug = "store_owner"),
                Role(id = 3, name = "店员", slug = "store_staff")
            )
        )

        coEvery { repository.updateUserRoles(userId, roleIds) } returns Result.success(updatedUser)

        // 先显示角色对话框
        viewModel.showRoleDialog(testUsers[0])
        testScheduler.advanceUntilIdle()

        // 执行角色更新
        viewModel.updateUserRoles(userId, roleIds)
        testScheduler.advanceUntilIdle()

        // 验证repository调用
        coVerify { repository.updateUserRoles(userId, roleIds) }

        // 验证UI状态
        val uiState = viewModel.uiState.first()
        assertFalse(uiState.showRoleDialog)
        assertNull(uiState.selectedUser)
    }

    @Test
    fun `更新用户角色失败应该显示错误`() = runTest {
        val userId = 1
        val roleIds = listOf(2)
        val errorMessage = "更新角色失败"

        coEvery { repository.updateUserRoles(userId, roleIds) } returns Result.failure(Exception(errorMessage))

        // 执行角色更新
        viewModel.updateUserRoles(userId, roleIds)
        testScheduler.advanceUntilIdle()

        // 验证操作结果
        val operationResult = viewModel.operationResult.first()
        assertTrue(operationResult is UserOperationResult.Error)
        assertEquals(errorMessage, (operationResult as UserOperationResult.Error).message)
    }

    @Test
    fun `显示用户详情应该更新UI状态`() = runTest {
        val user = testUsers[0]

        viewModel.showUserDetail(user)
        testScheduler.advanceUntilIdle()

        val uiState = viewModel.uiState.first()
        assertTrue(uiState.showUserDetail)
        assertEquals(user, uiState.selectedUser)
    }

    @Test
    fun `隐藏用户详情应该清除选中状态`() = runTest {
        // 先显示详情
        viewModel.showUserDetail(testUsers[0])
        testScheduler.advanceUntilIdle()

        // 隐藏详情
        viewModel.hideUserDetail()
        testScheduler.advanceUntilIdle()

        val uiState = viewModel.uiState.first()
        assertFalse(uiState.showUserDetail)
        assertNull(uiState.selectedUser)
    }

    @Test
    fun `加载下一页应该更新页码并重新加载数据`() = runTest {
        // 设置多页数据
        val multiPageData = testPaginatedData.copy(
            current_page = 1,
            last_page = 3
        )
        coEvery { repository.getUsers(any(), any(), any(), any()) } returns Result.success(multiPageData)

        // 重新初始化以获取多页数据
        viewModel.loadUsers()
        testScheduler.advanceUntilIdle()

        // 加载下一页
        viewModel.loadNextPage()
        testScheduler.advanceUntilIdle()

        // 验证repository调用
        coVerify { repository.getUsers(null, null, 2, 15) }
    }

    @Test
    fun `权限检查应该正确验证管理员权限`() {
        val adminRoles = listOf("admin")
        val staffRoles = listOf("store_staff")

        assertTrue(viewModel.hasManagementPermission(adminRoles))
        assertFalse(viewModel.hasManagementPermission(staffRoles))
    }

    @Test
    fun `刷新数据应该重新加载用户列表`() = runTest {
        viewModel.refresh()
        testScheduler.advanceUntilIdle()

        // 验证repository调用（初始化 + 刷新）
        coVerify(exactly = 2) { repository.getUsers(null, null, 1, 15) }
    }

    @Test
    fun `清除错误应该重置错误状态`() = runTest {
        // 模拟加载错误
        coEvery { repository.getUsers(any(), any(), any(), any()) } returns Result.failure(Exception("网络错误"))
        
        viewModel.loadUsers()
        testScheduler.advanceUntilIdle()

        // 验证有错误
        var uiState = viewModel.uiState.first()
        assertNotNull(uiState.error)

        // 清除错误
        viewModel.clearError()
        testScheduler.advanceUntilIdle()

        // 验证错误已清除
        uiState = viewModel.uiState.first()
        assertNull(uiState.error)
    }
}
