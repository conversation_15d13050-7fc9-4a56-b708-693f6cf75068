package com.example.gxzhaiwu.ui.dashboard.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.QuickActionData
import com.example.gxzhaiwu.data.model.QuickActionType
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import com.example.gxzhaiwu.ui.theme.cardElevationColor

/**
 * 快速操作卡片组件
 * 提供常用功能的快速访问入口
 */
@Composable
fun QuickActionCard(
    actions: List<QuickActionData>,
    onActionClick: (QuickActionData) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.cardElevationColor(2)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "快速操作",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Icon(
                    imageVector = Icons.Default.Speed,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }

            // 快速操作按钮列表
            if (actions.isNotEmpty()) {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(horizontal = 4.dp)
                ) {
                    items(actions) { action ->
                        QuickActionItem(
                            action = action,
                            onClick = { onActionClick(action) }
                        )
                    }
                }
            } else {
                // 空状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无可用操作",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 单个快速操作项组件
 */
@Composable
private fun QuickActionItem(
    action: QuickActionData,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .width(120.dp)
            .then(
                if (action.enabled) {
                    Modifier.clickable { onClick() }
                } else {
                    Modifier
                }
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (action.enabled) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            }
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (action.enabled) 1.dp else 0.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 图标
            val icon = getActionIcon(action.icon)
            if (icon != null) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = if (action.enabled) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                    },
                    modifier = Modifier.size(32.dp)
                )
            }

            // 标题
            Text(
                text = action.title,
                style = MaterialTheme.typography.labelLarge,
                fontWeight = FontWeight.Medium,
                color = if (action.enabled) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                }
            )

            // 描述
            Text(
                text = action.description,
                style = MaterialTheme.typography.bodySmall,
                color = if (action.enabled) {
                    MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f)
                }
            )
        }
    }
}

/**
 * 根据图标名称获取对应的ImageVector
 */
private fun getActionIcon(iconName: String): ImageVector? {
    return when (iconName) {
        "receipt" -> Icons.Default.Receipt
        "payment" -> Icons.Default.Payment
        "people" -> Icons.Default.PersonAdd
        "analytics" -> Icons.Default.Analytics
        "admin_panel_settings" -> Icons.Default.AdminPanelSettings
        "add" -> Icons.Default.Add
        "edit" -> Icons.Default.Edit
        "search" -> Icons.Default.Search
        "settings" -> Icons.Default.Settings
        else -> Icons.Default.TouchApp
    }
}

@Preview(name = "快速操作卡片 - 管理员")
@Composable
private fun QuickActionCardAdminPreview() {
    GxZhaiWuTheme {
        Surface {
            QuickActionCard(
                actions = listOf(
                    QuickActionData(
                        title = "创建账单",
                        description = "为客户创建新的账单",
                        icon = "receipt",
                        action = QuickActionType.CREATE_INVOICE
                    ),
                    QuickActionData(
                        title = "记录还款",
                        description = "记录客户的还款信息",
                        icon = "payment",
                        action = QuickActionType.RECORD_PAYMENT
                    ),
                    QuickActionData(
                        title = "添加客户",
                        description = "添加新的客户信息",
                        icon = "people",
                        action = QuickActionType.ADD_CUSTOMER
                    ),
                    QuickActionData(
                        title = "查看报表",
                        description = "查看详细的业务报表",
                        icon = "analytics",
                        action = QuickActionType.VIEW_REPORTS
                    )
                ),
                onActionClick = { },
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Preview(name = "快速操作卡片 - 店员")
@Composable
private fun QuickActionCardStaffPreview() {
    GxZhaiWuTheme {
        Surface {
            QuickActionCard(
                actions = listOf(
                    QuickActionData(
                        title = "创建账单",
                        description = "为客户创建新的账单",
                        icon = "receipt",
                        action = QuickActionType.CREATE_INVOICE
                    ),
                    QuickActionData(
                        title = "记录还款",
                        description = "记录客户的还款信息",
                        icon = "payment",
                        action = QuickActionType.RECORD_PAYMENT
                    )
                ),
                onActionClick = { },
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Preview(name = "快速操作卡片 - 空状态")
@Composable
private fun QuickActionCardEmptyPreview() {
    GxZhaiWuTheme {
        Surface {
            QuickActionCard(
                actions = emptyList(),
                onActionClick = { },
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}
