package com.example.gxzhaiwu.utils

import org.junit.Assert.*
import org.junit.Test

class ValidationUtilsTest {

    @Test
    fun validateLogin_withValidEmail_returnsNull() {
        val result = ValidationUtils.validateLogin("<EMAIL>")
        assertNull(result)
    }

    @Test
    fun validateLogin_withValidUsername_returnsNull() {
        val result = ValidationUtils.validateLogin("testuser")
        assertNull(result)
    }

    @Test
    fun validateLogin_withEmptyString_returnsError() {
        val result = ValidationUtils.validateLogin("")
        assertEquals("请输入邮箱或用户名", result)
    }

    @Test
    fun validateLogin_withBlankString_returnsError() {
        val result = ValidationUtils.validateLogin("   ")
        assertEquals("请输入邮箱或用户名", result)
    }

    @Test
    fun validateLogin_withTooLongString_returnsError() {
        val longString = "a".repeat(256)
        val result = ValidationUtils.validateLogin(longString)
        assertEquals("输入内容过长", result)
    }

    @Test
    fun validatePassword_withValidPassword_returnsNull() {
        val result = ValidationUtils.validatePassword("password123")
        assertNull(result)
    }

    @Test
    fun validatePassword_withEmptyPassword_returnsError() {
        val result = ValidationUtils.validatePassword("")
        assertEquals("请输入密码", result)
    }

    @Test
    fun validatePassword_withShortPassword_returnsError() {
        val result = ValidationUtils.validatePassword("123")
        assertEquals("密码长度不能少于${Constants.MIN_PASSWORD_LENGTH}位", result)
    }

    @Test
    fun validateEmail_withValidEmail_returnsNull() {
        val result = ValidationUtils.validateEmail("<EMAIL>")
        assertNull(result)
    }

    @Test
    fun validateEmail_withInvalidEmail_returnsError() {
        val result = ValidationUtils.validateEmail("invalid-email")
        assertEquals("邮箱格式不正确", result)
    }

    @Test
    fun validateEmail_withEmptyEmail_returnsError() {
        val result = ValidationUtils.validateEmail("")
        assertEquals("请输入邮箱", result)
    }

    @Test
    fun validateUsername_withValidUsername_returnsNull() {
        val result = ValidationUtils.validateUsername("testuser")
        assertNull(result)
    }

    @Test
    fun validateUsername_withEmptyUsername_returnsError() {
        val result = ValidationUtils.validateUsername("")
        assertEquals("请输入用户名", result)
    }

    @Test
    fun validatePasswordConfirmation_withMatchingPasswords_returnsNull() {
        val result = ValidationUtils.validatePasswordConfirmation("password123", "password123")
        assertNull(result)
    }

    @Test
    fun validatePasswordConfirmation_withNonMatchingPasswords_returnsError() {
        val result = ValidationUtils.validatePasswordConfirmation("password123", "different")
        assertEquals("两次输入的密码不一致", result)
    }

    @Test
    fun validatePasswordConfirmation_withEmptyConfirmation_returnsError() {
        val result = ValidationUtils.validatePasswordConfirmation("password123", "")
        assertEquals("请确认密码", result)
    }
}
