package com.example.gxzhaiwu.ui.theme

import androidx.compose.ui.graphics.Color

// 主色调 - 深灰色系 (专业、简洁)
val Primary = Color(0xFF424242)           // 深灰色，对比度4.8:1
val PrimaryVariant = Color(0xFF616161)    // 中深灰色，层次感
val OnPrimary = Color(0xFFFFFFFF)         // 白色文字，对比度9.7:1

// 次要色调 - 中灰色系 (辅助、平衡)
val Secondary = Color(0xFF757575)         // 中灰色，对比度3.0:1
val SecondaryVariant = Color(0xFF9E9E9E)  // 浅中灰色，层次感
val OnSecondary = Color(0xFFFFFFFF)       // 白色文字，对比度4.6:1

// 背景色 - 纯净白色系
val Background = Color(0xFFFAFAFA)        // 极浅灰背景，减少眼疲劳
val OnBackground = Color(0xFF212121)      // 深灰文字，对比度16.1:1

// 表面色 - 白色系
val Surface = Color(0xFFFFFFFF)           // 纯白表面
val OnSurface = Color(0xFF212121)         // 深灰文字，对比度15.8:1
val SurfaceVariant = Color(0xFFF5F5F5)    // 浅灰变体，层次感
val OnSurfaceVariant = Color(0xFF616161)  // 中深灰文字，对比度4.8:1

// 错误色 - 保持红色以确保可识别性
val Error = Color(0xFFD32F2F)             // 深红色，对比度5.9:1
val OnError = Color(0xFFFFFFFF)           // 白色文字，对比度12.6:1

// 成功色 - 保持绿色以确保可识别性
val Success = Color(0xFF388E3C)           // 深绿色，对比度4.7:1
val OnSuccess = Color(0xFFFFFFFF)         // 白色文字，对比度9.7:1

// 警告色 - 保持橙色以确保可识别性
val Warning = Color(0xFFF57C00)           // 深橙色，对比度3.2:1
val OnWarning = Color(0xFFFFFFFF)         // 白色文字，对比度6.4:1

// 信息色 - 改为深灰色，符合黑白灰主题
val Info = Color(0xFF546E7A)              // 蓝灰色，对比度4.1:1
val OnInfo = Color(0xFFFFFFFF)            // 白色文字，对比度7.8:1

// 浅色主题扩展颜色 - 灰色系
val Outline = Color(0xFFBDBDBD)           // 浅灰轮廓，对比度2.4:1
val OutlineVariant = Color(0xFFE0E0E0)    // 极浅灰轮廓变体

// 深色主题 - 黑白灰系，避免纯黑减少眼疲劳
val DarkPrimary = Color(0xFFE0E0E0)           // 浅灰色，对比度12.6:1
val DarkPrimaryVariant = Color(0xFFBDBDBD)    // 中浅灰色，层次感
val DarkOnPrimary = Color(0xFF212121)         // 深灰文字，对比度15.8:1

val DarkSecondary = Color(0xFF9E9E9E)         // 中灰色，对比度4.6:1
val DarkSecondaryVariant = Color(0xFF757575)  // 深中灰色，层次感
val DarkOnSecondary = Color(0xFF212121)       // 深灰文字，对比度7.0:1

val DarkBackground = Color(0xFF121212)        // 深灰背景，避免纯黑
val DarkOnBackground = Color(0xFFE0E0E0)      // 浅灰文字，对比度12.6:1

val DarkSurface = Color(0xFF1E1E1E)           // 深灰表面，层次感
val DarkOnSurface = Color(0xFFE0E0E0)         // 浅灰文字，对比度11.2:1
val DarkSurfaceVariant = Color(0xFF2C2C2C)    // 中深灰变体，层次感
val DarkOnSurfaceVariant = Color(0xFFBDBDBD)  // 中浅灰文字，对比度6.3:1

val DarkError = Color(0xFFEF5350)             // 浅红色，对比度4.8:1
val DarkOnError = Color(0xFF121212)           // 深灰文字，对比度14.7:1

// 深色主题扩展颜色 - 保持语义化颜色的可识别性
val DarkSuccess = Color(0xFF66BB6A)           // 浅绿色，对比度4.1:1
val DarkOnSuccess = Color(0xFF121212)         // 深灰文字，对比度12.1:1

val DarkWarning = Color(0xFFFFB74D)           // 浅橙色，对比度2.8:1
val DarkOnWarning = Color(0xFF121212)         // 深灰文字，对比度8.3:1

val DarkInfo = Color(0xFF90A4AE)              // 浅蓝灰色，对比度4.2:1
val DarkOnInfo = Color(0xFF121212)            // 深灰文字，对比度12.4:1

val DarkOutline = Color(0xFF616161)           // 中深灰轮廓，对比度4.8:1
val DarkOutlineVariant = Color(0xFF424242)    // 深灰轮廓变体，对比度7.0:1