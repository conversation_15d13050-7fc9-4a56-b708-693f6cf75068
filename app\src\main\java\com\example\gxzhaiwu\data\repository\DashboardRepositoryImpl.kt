package com.example.gxzhaiwu.data.repository

import com.example.gxzhaiwu.data.api.DashboardApi
import com.example.gxzhaiwu.data.local.UserPreferences
import com.example.gxzhaiwu.data.model.DashboardOverview
import com.example.gxzhaiwu.data.model.DashboardStatistics
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 仪表盘数据仓库实现类
 * 负责从API获取仪表盘数据并处理相关业务逻辑
 */
@Singleton
class DashboardRepositoryImpl @Inject constructor(
    private val dashboardApi: DashboardApi,
    private val userPreferences: UserPreferences
) : DashboardRepository {

    override suspend fun getOverview(): Result<DashboardOverview> {
        return try {
            val token = userPreferences.authToken.first()
            if (token.isNullOrEmpty()) {
                return Result.failure(Exception("用户未登录"))
            }

            val response = dashboardApi.getOverview("Bearer $token")
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    Result.success(apiResponse.data)
                } else {
                    Result.failure(Exception(apiResponse?.message ?: "获取仪表盘概览数据失败"))
                }
            } else {
                when (response.code()) {
                    401 -> Result.failure(Exception("认证失败，请重新登录"))
                    403 -> Result.failure(Exception("权限不足"))
                    404 -> Result.failure(Exception("接口不存在"))
                    500 -> Result.failure(Exception("服务器内部错误"))
                    else -> Result.failure(Exception("网络请求失败: ${response.code()}"))
                }
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getStatistics(
        startDate: String?,
        endDate: String?
    ): Result<DashboardStatistics> {
        return try {
            val token = userPreferences.authToken.first()
            if (token.isNullOrEmpty()) {
                return Result.failure(Exception("用户未登录"))
            }

            val response = dashboardApi.getStatistics(
                authorization = "Bearer $token",
                startDate = startDate,
                endDate = endDate
            )
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    Result.success(apiResponse.data)
                } else {
                    Result.failure(Exception(apiResponse?.message ?: "获取详细统计数据失败"))
                }
            } else {
                when (response.code()) {
                    401 -> Result.failure(Exception("认证失败，请重新登录"))
                    403 -> Result.failure(Exception("权限不足"))
                    404 -> Result.failure(Exception("接口不存在"))
                    500 -> Result.failure(Exception("服务器内部错误"))
                    else -> Result.failure(Exception("网络请求失败: ${response.code()}"))
                }
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
