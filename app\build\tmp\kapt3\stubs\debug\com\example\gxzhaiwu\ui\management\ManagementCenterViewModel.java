package com.example.gxzhaiwu.ui.management;

/**
 * 管理中心ViewModel
 * 处理管理模块的加载和权限控制
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\n\u001a\u00020\u000bJ\u0006\u0010\f\u001a\u00020\rJ\u000e\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u0002J\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0012\u001a\u00020\u0013J\u0006\u0010\u0014\u001a\u00020\u0013J\u0006\u0010\u0015\u001a\u00020\u0016J\u000e\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0012\u001a\u00020\u0013J\u0014\u0010\u0018\u001a\u00020\u000b2\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00130\u000fJ\u0006\u0010\u001a\u001a\u00020\u0016J\u0006\u0010\u001b\u001a\u00020\u0016J\u0006\u0010\u001c\u001a\u00020\u000bJ\u0006\u0010\u001d\u001a\u00020\u000bR\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\t\u00a8\u0006\u001e"}, d2 = {"Lcom/example/gxzhaiwu/ui/management/ManagementCenterViewModel;", "Landroidx/lifecycle/ViewModel;", "()V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/gxzhaiwu/data/model/ManagementCenterUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "clearError", "", "getAccessibleModuleCount", "", "getAllManagementModules", "", "Lcom/example/gxzhaiwu/data/model/ManagementModule;", "getModuleById", "moduleId", "", "getUserRoleDisplayName", "hasManagementAccess", "", "hasModulePermission", "initializeUserRoles", "userRoles", "isAdmin", "isStoreOwner", "loadModules", "refresh", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ManagementCenterViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.gxzhaiwu.data.model.ManagementCenterUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.gxzhaiwu.data.model.ManagementCenterUiState> uiState = null;
    
    @javax.inject.Inject()
    public ManagementCenterViewModel() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.gxzhaiwu.data.model.ManagementCenterUiState> getUiState() {
        return null;
    }
    
    /**
     * 初始化用户角色并加载模块
     */
    public final void initializeUserRoles(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> userRoles) {
    }
    
    /**
     * 加载可用的管理模块
     */
    public final void loadModules() {
    }
    
    /**
     * 获取所有管理模块的定义
     */
    private final java.util.List<com.example.gxzhaiwu.data.model.ManagementModule> getAllManagementModules() {
        return null;
    }
    
    /**
     * 清除错误状态
     */
    public final void clearError() {
    }
    
    /**
     * 重新加载模块
     */
    public final void refresh() {
    }
    
    /**
     * 检查用户是否有访问管理中心的权限
     */
    public final boolean hasManagementAccess() {
        return false;
    }
    
    /**
     * 获取用户可访问的模块数量
     */
    public final int getAccessibleModuleCount() {
        return 0;
    }
    
    /**
     * 根据模块ID获取模块信息
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.gxzhaiwu.data.model.ManagementModule getModuleById(@org.jetbrains.annotations.NotNull()
    java.lang.String moduleId) {
        return null;
    }
    
    /**
     * 验证用户是否有权限访问特定模块
     */
    public final boolean hasModulePermission(@org.jetbrains.annotations.NotNull()
    java.lang.String moduleId) {
        return false;
    }
    
    /**
     * 获取用户角色的显示名称
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUserRoleDisplayName() {
        return null;
    }
    
    /**
     * 检查是否为管理员
     */
    public final boolean isAdmin() {
        return false;
    }
    
    /**
     * 检查是否为店长
     */
    public final boolean isStoreOwner() {
        return false;
    }
}