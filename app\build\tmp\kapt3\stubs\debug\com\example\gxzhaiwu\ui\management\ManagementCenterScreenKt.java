package com.example.gxzhaiwu.ui.management;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000<\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0012\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u001a(\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u001a\u0012\u0010\t\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u001aL\u0010\n\u001a\u00020\u00012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00060\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u000f2\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0010\u001a\u00020\u0011H\u0007\u001a \u0010\u0012\u001a\u00020\u00012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u001a4\u0010\u0013\u001a\u00020\u00012\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\f2\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u000f2\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u00a8\u0006\u0017"}, d2 = {"EmptyContent", "", "modifier", "Landroidx/compose/ui/Modifier;", "ErrorContent", "message", "", "onRetry", "Lkotlin/Function0;", "LoadingContent", "ManagementCenterScreen", "currentUserRoles", "", "onNavigateBack", "onNavigateToModule", "Lkotlin/Function1;", "viewModel", "Lcom/example/gxzhaiwu/ui/management/ManagementCenterViewModel;", "ManagementCenterTopBar", "ManagementModuleGrid", "modules", "Lcom/example/gxzhaiwu/data/model/ManagementModule;", "onModuleClick", "app_debug"})
public final class ManagementCenterScreenKt {
    
    /**
     * 管理中心主屏幕
     * 显示所有可用的管理模块
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ManagementCenterScreen(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> currentUserRoles, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNavigateToModule, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.ui.management.ManagementCenterViewModel viewModel) {
    }
    
    /**
     * 管理中心顶部栏
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void ManagementCenterTopBar(kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 管理模块网格
     */
    @androidx.compose.runtime.Composable()
    private static final void ManagementModuleGrid(java.util.List<com.example.gxzhaiwu.data.model.ManagementModule> modules, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onModuleClick, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 加载状态内容
     */
    @androidx.compose.runtime.Composable()
    private static final void LoadingContent(androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 错误状态内容
     */
    @androidx.compose.runtime.Composable()
    private static final void ErrorContent(java.lang.String message, kotlin.jvm.functions.Function0<kotlin.Unit> onRetry, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 空状态内容（无权限访问任何模块）
     */
    @androidx.compose.runtime.Composable()
    private static final void EmptyContent(androidx.compose.ui.Modifier modifier) {
    }
}