package com.example.gxzhaiwu.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001c\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0016\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\u001a\u0019\u0010\u0017\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0018\u001a\u00020\u0019H\u0007\u00a2\u0006\u0002\u0010\u001a\u001a\u0011\u0010\u001b\u001a\u00020\u0001*\u00020\u0002H\u0007\u00a2\u0006\u0002\u0010\u0004\u001a\u0011\u0010\u001c\u001a\u00020\u0001*\u00020\u0002H\u0007\u00a2\u0006\u0002\u0010\u0004\u001a\u0011\u0010\u001d\u001a\u00020\u0001*\u00020\u0002H\u0007\u00a2\u0006\u0002\u0010\u0004\u001a\f\u0010\u001e\u001a\u00020\u001f*\u00020\u0002H\u0002\"\u0015\u0010\u0000\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0003\u0010\u0004\"\u0015\u0010\u0005\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0006\u0010\u0004\"\u0015\u0010\u0007\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\u0004\"\u0015\u0010\t\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\n\u0010\u0004\"\u0015\u0010\u000b\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\f\u0010\u0004\"\u0015\u0010\r\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u000e\u0010\u0004\"\u0015\u0010\u000f\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0010\u0010\u0004\"\u0015\u0010\u0011\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0012\u0010\u0004\"\u0015\u0010\u0013\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0014\u0010\u0004\"\u0015\u0010\u0015\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0016\u0010\u0004\u00a8\u0006 "}, d2 = {"info", "Landroidx/compose/ui/graphics/Color;", "Landroidx/compose/material3/ColorScheme;", "getInfo", "(Landroidx/compose/material3/ColorScheme;)J", "onInfo", "getOnInfo", "onSuccess", "getOnSuccess", "onSurfaceVariant", "getOnSurfaceVariant", "onWarning", "getOnWarning", "outline", "getOutline", "outlineVariant", "getOutlineVariant", "success", "getSuccess", "surfaceVariant", "getSurfaceVariant", "warning", "getWarning", "cardElevationColor", "elevation", "", "(Landroidx/compose/material3/ColorScheme;I)J", "disabledColor", "disabledContainerColor", "dividerColor", "isLight", "", "app_debug"})
public final class ColorSchemeExtensionsKt {
    
    /**
     * 判断当前是否为浅色主题
     */
    private static final boolean isLight(androidx.compose.material3.ColorScheme $this$isLight) {
        return false;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getSuccess(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$success) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getOnSuccess(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$onSuccess) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getWarning(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$warning) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getOnWarning(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$onWarning) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getInfo(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$info) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getOnInfo(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$onInfo) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getSurfaceVariant(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$surfaceVariant) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getOnSurfaceVariant(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$onSurfaceVariant) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getOutline(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$outline) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getOutlineVariant(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$outlineVariant) {
        return 0L;
    }
    
    /**
     * 获取适合当前主题的卡片elevation颜色
     * 黑白灰主题优化版本
     */
    @androidx.compose.runtime.Composable()
    public static final long cardElevationColor(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$cardElevationColor, int elevation) {
        return 0L;
    }
    
    /**
     * 获取适合当前主题的分割线颜色
     */
    @androidx.compose.runtime.Composable()
    public static final long dividerColor(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$dividerColor) {
        return 0L;
    }
    
    /**
     * 获取适合当前主题的禁用状态颜色
     */
    @androidx.compose.runtime.Composable()
    public static final long disabledColor(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$disabledColor) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long disabledContainerColor(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$disabledContainerColor) {
        return 0L;
    }
}