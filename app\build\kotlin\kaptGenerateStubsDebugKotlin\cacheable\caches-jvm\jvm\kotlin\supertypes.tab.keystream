(com.example.gxzhaiwu.GxZhaiWuApplication!com.example.gxzhaiwu.MainActivity5com.example.gxzhaiwu.data.api.ApiResponse.$serializer7com.example.gxzhaiwu.data.api.ErrorResponse.$serializer0com.example.gxzhaiwu.data.model.User.$serializer1com.example.gxzhaiwu.data.model.Store.$serializer8com.example.gxzhaiwu.data.model.LoginRequest.$serializer9com.example.gxzhaiwu.data.model.LoginResponse.$serializer;com.example.gxzhaiwu.data.model.RegisterRequest.$serializer<com.example.gxzhaiwu.data.model.RegisterResponse.$serializer7com.example.gxzhaiwu.data.repository.AuthRepositoryImpl1com.example.gxzhaiwu.ui.auth.login.LoginViewModel*com.example.gxzhaiwu.ui.home.HomeViewModel:com.example.gxzhaiwu.ui.navigation.AuthNavigationViewModel/com.example.gxzhaiwu.ui.navigation.Screen.Login2com.example.gxzhaiwu.ui.navigation.Screen.Register.com.example.gxzhaiwu.ui.navigation.Screen.Home3com.example.gxzhaiwu.ui.navigation.Screen.Dashboard5com.example.gxzhaiwu.ui.navigation.Screen.InvoiceList7com.example.gxzhaiwu.ui.navigation.Screen.InvoiceDetail7com.example.gxzhaiwu.ui.navigation.Screen.CreateInvoice6com.example.gxzhaiwu.ui.navigation.Screen.CustomerList8com.example.gxzhaiwu.ui.navigation.Screen.CustomerDetail8com.example.gxzhaiwu.ui.navigation.Screen.CreateCustomer5com.example.gxzhaiwu.ui.navigation.Screen.PaymentList7com.example.gxzhaiwu.ui.navigation.Screen.CreatePayment3com.example.gxzhaiwu.ui.navigation.Screen.StoreList5com.example.gxzhaiwu.ui.navigation.Screen.StoreDetail2com.example.gxzhaiwu.ui.navigation.Screen.Settings1com.example.gxzhaiwu.ui.navigation.Screen.Profile1com.example.gxzhaiwu.utils.TestUtils.TestScenario7com.example.gxzhaiwu.ui.auth.register.RegisterViewModel=com.example.gxzhaiwu.data.model.DashboardOverview.$serializer9com.example.gxzhaiwu.data.model.SystemSummary.$serializer<com.example.gxzhaiwu.data.model.FinancialSummary.$serializerEcom.example.gxzhaiwu.data.model.InvoiceStatusDistribution.$serializer?com.example.gxzhaiwu.data.model.DashboardStatistics.$serializer<com.example.gxzhaiwu.data.model.StatisticsPeriod.$serializer>com.example.gxzhaiwu.data.model.CustomerStatistics.$serializer=com.example.gxzhaiwu.data.model.InvoiceStatistics.$serializer=com.example.gxzhaiwu.data.model.PaymentStatistics.$serializer;com.example.gxzhaiwu.data.model.StoreStatistics.$serializerEcom.example.gxzhaiwu.data.model.DashboardOverviewResponse.$serializerGcom.example.gxzhaiwu.data.model.DashboardStatisticsResponse.$serializer(com.example.gxzhaiwu.data.model.UserRole)com.example.gxzhaiwu.data.model.TrendType/com.example.gxzhaiwu.data.model.QuickActionType<com.example.gxzhaiwu.data.repository.DashboardRepositoryImpl9com.example.gxzhaiwu.ui.dashboard.DashboardEvent.LoadData<com.example.gxzhaiwu.ui.dashboard.DashboardEvent.RefreshData;<EMAIL>?com.example.gxzhaiwu.ui.dashboard.DashboardSideEffect.ShowErrorFcom.example.gxzhaiwu.ui.dashboard.DashboardSideEffect.NavigateToScreenBcom.example.gxzhaiwu.ui.dashboard.DashboardSideEffect.ShowSnackbarEcom.example.gxzhaiwu.ui.dashboard.DashboardSideEffect.NavigateToLogin4com.example.gxzhaiwu.ui.dashboard.DashboardViewModel%com.example.gxzhaiwu.utils.Permission7com.example.gxzhaiwu.ui.dashboard.DashboardTestActivity;com.example.gxzhaiwu.data.api.StoreListResponse.$serializer/com.example.gxzhaiwu.data.api.Store.$serializer0com.example.gxzhaiwu.data.model.Role.$serializer;com.example.gxzhaiwu.data.model.UserPermissions.$serializer5com.example.gxzhaiwu.data.model.UserStore.$serializer<com.example.gxzhaiwu.data.model.UserListResponse.$serializer=com.example.gxzhaiwu.data.model.PaginatedUserData.$serializer>com.example.gxzhaiwu.data.model.UserDetailResponse.$serializer<com.example.gxzhaiwu.data.model.RoleListResponse.$serializerBcom.example.gxzhaiwu.data.model.UpdateUserRolesRequest.$serializerCcom.example.gxzhaiwu.data.model.UpdateUserStoresRequest.$serializer4com.example.gxzhaiwu.data.model.UserManagementAction*com.example.gxzhaiwu.data.model.UserStatus;com.example.gxzhaiwu.data.model.UserOperationResult.Success9com.example.gxzhaiwu.data.model.UserOperationResult.Error;com.example.gxzhaiwu.data.model.UserOperationResult.LoadingAcom.example.gxzhaiwu.data.repository.UserManagementRepositoryImpl8com.example.gxzhaiwu.ui.navigation.Screen.UserManagement>com.example.gxzhaiwu.ui.usermanagement.UserManagementViewModel6com.example.gxzhaiwu.data.model.UserDetail.$serializer4com.example.gxzhaiwu.data.model.ManagementModuleType<com.example.gxzhaiwu.ui.management.ManagementCenterViewModel9com.example.gxzhaiwu.ui.management.ManagementTestActivity:com.example.gxzhaiwu.ui.navigation.Screen.ManagementCenter                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            