package com.example.gxzhaiwu.utils

object Constants {
    const val BASE_URL = "https://gxzhaiwu.hrlni.cn/api/"
    
    // DataStore keys
    const val USER_PREFERENCES = "user_preferences"
    const val KEY_AUTH_TOKEN = "auth_token"
    const val KEY_USER_ID = "user_id"
    const val KEY_USER_NAME = "user_name"
    const val KEY_USER_EMAIL = "user_email"
    const val KEY_USER_ROLES = "user_roles"
    
    // Network timeouts
    const val CONNECT_TIMEOUT = 30L
    const val READ_TIMEOUT = 30L
    const val WRITE_TIMEOUT = 30L
    
    // Validation
    const val MIN_PASSWORD_LENGTH = 6
    const val MAX_USERNAME_LENGTH = 255
    const val MAX_EMAIL_LENGTH = 255
}
