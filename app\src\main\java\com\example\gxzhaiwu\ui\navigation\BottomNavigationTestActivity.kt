package com.example.gxzhaiwu.ui.navigation

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.compose.rememberNavController
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * 底部导航测试活动
 * 用于测试新的底部导航架构
 */
@AndroidEntryPoint
class BottomNavigationTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            GxZhaiWuTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    BottomNavigationTestScreen()
                }
            }
        }
    }
}

/**
 * 底部导航测试屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BottomNavigationTestScreen() {
    val navController = rememberNavController()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "底部导航架构测试",
            style = MaterialTheme.typography.headlineMedium
        )
        
        // 测试说明
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "测试项目",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Text("✅ 底部导航基础架构创建完成")
                Text("✅ 账单管理模块开发完成")
                Text("✅ 还款管理模块开发完成")
                Text("✅ 管理中心重构完成")
                Text("✅ 仪表盘快速操作优化完成")
                Text("✅ 权限控制系统更新完成")
                Text("✅ 导航系统集成完成")
            }
        }
        
        // 主容器测试
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "主容器测试",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Button(
                    onClick = { 
                        // 测试主容器屏幕
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("测试主容器屏幕")
                }
                
                Text(
                    text = "主容器包含4个底部导航标签：",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Column(
                    modifier = Modifier.padding(start = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text("• 仪表盘 (Dashboard)")
                    Text("• 账单管理 (Bills)")
                    Text("• 还款管理 (Payments)")
                    Text("• 管理中心 (Management)")
                }
            }
        }
        
        // 权限测试
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "权限控制测试",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Text(
                    text = "不同角色看到的导航项：",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Column(
                    modifier = Modifier.padding(start = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text("• 管理员: 4个标签全部可见")
                    Text("• 店长: 4个标签全部可见")
                    Text("• 店员: 仅3个标签（无管理中心）")
                }
            }
        }
        
        // 功能测试
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "功能模块测试",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Text(
                    text = "管理中心包含的模块：",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Column(
                    modifier = Modifier.padding(start = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text("• 门店管理")
                    Text("• 系统管理")
                    Text("• 用户管理")
                    Text("• 客户管理")
                }
                
                Text(
                    text = "仪表盘快速操作：",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Column(
                    modifier = Modifier.padding(start = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text("• 创建账单")
                    Text("• 记录还款")
                    Text("• 添加客户")
                }
            }
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 状态指示
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            ),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "✅ 底部导航架构重构完成",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                Text(
                    text = "所有模块已成功集成到新的导航架构中",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
    }
}

@Preview(name = "底部导航测试屏幕")
@Composable
private fun BottomNavigationTestScreenPreview() {
    GxZhaiWuTheme {
        Surface {
            BottomNavigationTestScreen()
        }
    }
}
