package com.example.gxzhaiwu.utils

import java.text.DecimalFormat
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

/**
 * 数据格式化工具类
 * 提供各种数据格式化功能
 */
object FormatUtils {

    private val currencyFormat = NumberFormat.getCurrencyInstance(Locale.CHINA)
    private val numberFormat = DecimalFormat("#,###")
    private val percentFormat = DecimalFormat("#0.0%")
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA)
    private val dateTimeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA)
    private val timeFormat = SimpleDateFormat("HH:mm", Locale.CHINA)

    /**
     * 格式化货币金额
     * @param amount 金额字符串或数字
     * @return 格式化后的货币字符串，如：¥1,234.56
     */
    fun formatCurrency(amount: String): String {
        return try {
            val value = amount.toDoubleOrNull() ?: 0.0
            formatCurrency(value)
        } catch (e: Exception) {
            "¥0.00"
        }
    }

    /**
     * 格式化货币金额
     * @param amount 金额数值
     * @return 格式化后的货币字符串
     */
    fun formatCurrency(amount: Double): String {
        return currencyFormat.format(amount)
    }

    /**
     * 格式化货币金额（简化版，不显示货币符号）
     * @param amount 金额字符串或数字
     * @return 格式化后的数字字符串，如：1,234.56
     */
    fun formatAmount(amount: String): String {
        return try {
            val value = amount.toDoubleOrNull() ?: 0.0
            formatAmount(value)
        } catch (e: Exception) {
            "0.00"
        }
    }

    /**
     * 格式化金额数值（简化版）
     */
    fun formatAmount(amount: Double): String {
        return DecimalFormat("#,##0.00").format(amount)
    }

    /**
     * 格式化整数（添加千位分隔符）
     * @param number 数字
     * @return 格式化后的字符串，如：1,234
     */
    fun formatNumber(number: Int): String {
        return numberFormat.format(number)
    }

    /**
     * 格式化整数
     */
    fun formatNumber(number: Long): String {
        return numberFormat.format(number)
    }

    /**
     * 格式化百分比
     * @param value 百分比值（0.0-1.0）
     * @return 格式化后的百分比字符串，如：85.5%
     */
    fun formatPercentage(value: Double): String {
        return percentFormat.format(value)
    }

    /**
     * 格式化百分比（从字符串）
     */
    fun formatPercentage(value: String): String {
        return try {
            val doubleValue = value.toDoubleOrNull() ?: 0.0
            formatPercentage(doubleValue)
        } catch (e: Exception) {
            "0.0%"
        }
    }

    /**
     * 格式化日期
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期字符串，如：2024-01-15
     */
    fun formatDate(timestamp: Long): String {
        return dateFormat.format(Date(timestamp))
    }

    /**
     * 格式化日期时间
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期时间字符串，如：2024-01-15 14:30
     */
    fun formatDateTime(timestamp: Long): String {
        return dateTimeFormat.format(Date(timestamp))
    }

    /**
     * 格式化时间
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的时间字符串，如：14:30
     */
    fun formatTime(timestamp: Long): String {
        return timeFormat.format(Date(timestamp))
    }

    /**
     * 格式化相对时间
     * @param timestamp 时间戳（毫秒）
     * @return 相对时间字符串，如：2小时前、昨天、3天前
     */
    fun formatRelativeTime(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60 * 1000 -> "刚刚"
            diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
            diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
            diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)}天前"
            else -> formatDate(timestamp)
        }
    }

    /**
     * 格式化文件大小
     * @param bytes 字节数
     * @return 格式化后的文件大小字符串，如：1.5 MB
     */
    fun formatFileSize(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return if (unitIndex == 0) {
            "${size.toInt()} ${units[unitIndex]}"
        } else {
            String.format("%.1f %s", size, units[unitIndex])
        }
    }

    /**
     * 格式化趋势值
     * @param current 当前值
     * @param previous 之前值
     * @return 趋势百分比字符串，如：+15.5%、-8.2%
     */
    fun formatTrend(current: Double, previous: Double): String {
        if (previous == 0.0) return "N/A"
        
        val change = (current - previous) / previous
        val sign = if (change >= 0) "+" else ""
        return "$sign${formatPercentage(change)}"
    }

    /**
     * 格式化趋势值（从字符串）
     */
    fun formatTrend(current: String, previous: String): String {
        return try {
            val currentValue = current.toDoubleOrNull() ?: 0.0
            val previousValue = previous.toDoubleOrNull() ?: 0.0
            formatTrend(currentValue, previousValue)
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * 缩短大数字显示
     * @param number 数字
     * @return 缩短后的字符串，如：1.2K、3.5M、1.8B
     */
    fun formatShortNumber(number: Long): String {
        return when {
            number >= 1_000_000_000 -> String.format("%.1fB", number / 1_000_000_000.0)
            number >= 1_000_000 -> String.format("%.1fM", number / 1_000_000.0)
            number >= 1_000 -> String.format("%.1fK", number / 1_000.0)
            else -> number.toString()
        }
    }

    /**
     * 格式化手机号码
     * @param phone 手机号码
     * @return 格式化后的手机号码，如：138****5678
     */
    fun formatPhoneNumber(phone: String): String {
        return if (phone.length == 11) {
            "${phone.substring(0, 3)}****${phone.substring(7)}"
        } else {
            phone
        }
    }

    /**
     * 格式化身份证号码
     * @param idCard 身份证号码
     * @return 格式化后的身份证号码，如：110101********1234
     */
    fun formatIdCard(idCard: String): String {
        return if (idCard.length == 18) {
            "${idCard.substring(0, 6)}********${idCard.substring(14)}"
        } else {
            idCard
        }
    }

    /**
     * 格式化银行卡号
     * @param cardNumber 银行卡号
     * @return 格式化后的银行卡号，如：**** **** **** 1234
     */
    fun formatBankCard(cardNumber: String): String {
        return if (cardNumber.length >= 4) {
            "**** **** **** ${cardNumber.takeLast(4)}"
        } else {
            cardNumber
        }
    }
}
