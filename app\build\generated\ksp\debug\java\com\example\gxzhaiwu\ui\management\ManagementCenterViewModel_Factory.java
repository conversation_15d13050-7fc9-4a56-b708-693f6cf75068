package com.example.gxzhaiwu.ui.management;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class ManagementCenterViewModel_Factory implements Factory<ManagementCenterViewModel> {
  @Override
  public ManagementCenterViewModel get() {
    return newInstance();
  }

  public static ManagementCenterViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ManagementCenterViewModel newInstance() {
    return new ManagementCenterViewModel();
  }

  private static final class InstanceHolder {
    private static final ManagementCenterViewModel_Factory INSTANCE = new ManagementCenterViewModel_Factory();
  }
}
