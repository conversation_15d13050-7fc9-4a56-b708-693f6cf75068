{"logs": [{"outputFile": "com.example.gxzhaiwu.app-mergeDebugResources-61:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6e9efd0aa8467ac29c851889e152da8a\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "6423", "endColumns": "53", "endOffsets": "6472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c19a4f0623c1c480ca6e3f53e35bdcb\\transformed\\navigation-common-2.8.5\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "354,367,373,379,388", "startColumns": "4,4,4,4,4", "startOffsets": "18789,19428,19672,19919,20282", "endLines": "366,372,378,381,392", "endColumns": "24,24,24,24,24", "endOffsets": "19423,19667,19914,20047,20459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98f289bf6c45d262a7223b262b65f304\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "201,202", "startColumns": "4,4", "startOffsets": "12815,12871", "endColumns": "55,54", "endOffsets": "12866,12921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\463c36752911edcd502514953090fec6\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "109", "startColumns": "4", "startOffsets": "6477", "endColumns": "49", "endOffsets": "6522"}}, {"source": "E:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "5,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,675,722,769,816,861,906", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,717,764,811,856,901,943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\34672be5908381f92b2f1efab7609872\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "129,130,131,132,133,134,135,136,137,138,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7810,7898,7984,8065,8149,8218,8283,8366,8472,8558,8678,8732,8801,8862,8931,9020,9115,9189,9286,9379,9477,9626,9717,9805,9901,9999,10063,10131,10218,10312,10379,10451,10523,10624,10733,10809,10878,10926,10992,11056,11113,11170,11242,11292,11346,11417,11488,11558,11627,11685,11761,11832,11906,11992,12042,12112", "endLines": "129,130,131,132,133,134,135,136,137,140,141,142,143,144,145,146,147,148,149,150,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "7893,7979,8060,8144,8213,8278,8361,8467,8553,8673,8727,8796,8857,8926,9015,9110,9184,9281,9374,9472,9621,9712,9800,9896,9994,10058,10126,10213,10307,10374,10446,10518,10619,10728,10804,10873,10921,10987,11051,11108,11165,11237,11287,11341,11412,11483,11553,11622,11680,11756,11827,11901,11987,12037,12107,12172"}}, {"source": "E:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "85", "endOffsets": "137"}, "to": {"startLines": "224", "startColumns": "4", "startOffsets": "14102", "endColumns": "84", "endOffsets": "14182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f269811d8cc0a512ad9a3c6d9f38dcec\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "6320", "endColumns": "42", "endOffsets": "6358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca95cc56ae6c869d643262611d469661\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,81,82,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,112,115,116,117,118,119,120,121,197,213,214,218,219,223,225,226,234,240,250,285,315,348", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,543,612,948,1018,1086,1158,1228,1289,1363,1436,1497,1558,1620,1684,1746,1807,1875,1975,2035,2101,2174,2243,2300,2352,2414,2486,2562,2627,2686,2745,2805,2865,2925,2985,3045,3105,3165,3225,3285,3345,3404,3464,3524,3584,3644,3704,3764,3824,3884,3944,4004,4063,4123,4183,4242,4301,4360,4419,4478,5007,5042,5304,5359,5422,5477,5535,5591,5649,5710,5773,5830,5881,5939,5989,6050,6107,6173,6207,6242,6644,6843,6910,6982,7051,7120,7194,7266,12571,13344,13461,13662,13772,13973,14187,14259,14630,14833,15134,16940,17940,18622", "endLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,81,82,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,112,115,116,117,118,119,120,121,197,213,217,218,222,223,225,226,239,249,284,305,347,353", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,607,670,1013,1081,1153,1223,1284,1358,1431,1492,1553,1615,1679,1741,1802,1870,1970,2030,2096,2169,2238,2295,2347,2409,2481,2557,2622,2681,2740,2800,2860,2920,2980,3040,3100,3160,3220,3280,3340,3399,3459,3519,3579,3639,3699,3759,3819,3879,3939,3999,4058,4118,4178,4237,4296,4355,4414,4473,4532,5037,5072,5354,5417,5472,5530,5586,5644,5705,5768,5825,5876,5934,5984,6045,6102,6168,6202,6237,6272,6709,6905,6977,7046,7115,7189,7261,7349,12637,13456,13657,13767,13968,14097,14254,14321,14828,15129,16935,17616,18617,18784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a797c2aed94d9afc60c1d67666324b36\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "80,84", "startColumns": "4,4", "startOffsets": "4953,5130", "endColumns": "53,66", "endOffsets": "5002,5192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e7a34afc2b6fd3a244eccb13c4cf553\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "73,74,75,78,79,111,122,123,124,125,126,127,128,189,190,191,192,193,194,195,196,198,199,200,203,206,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4537,4611,4669,4847,4898,6591,7354,7419,7473,7539,7640,7698,7750,12177,12239,12293,12343,12397,12443,12489,12531,12642,12689,12725,12926,13038,13149", "endLines": "73,74,75,78,79,111,122,123,124,125,126,127,128,189,190,191,192,193,194,195,196,198,199,200,205,208,212", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "4606,4664,4719,4893,4948,6639,7414,7468,7534,7635,7693,7745,7805,12234,12288,12338,12392,12438,12484,12526,12566,12684,12720,12810,13033,13144,13339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c99abe88a69f82b4efe622efde03120\\transformed\\navigation-runtime-2.8.5\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "83,227,382,385", "startColumns": "4,4,4,4", "startOffsets": "5077,14326,20052,20167", "endLines": "83,233,384,387", "endColumns": "52,24,24,24", "endOffsets": "5125,14625,20162,20277"}}, {"source": "E:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "45", "endOffsets": "57"}, "to": {"startLines": "114", "startColumns": "4", "startOffsets": "6797", "endColumns": "45", "endOffsets": "6838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5addc3b44c294d89924313c3ea377a3b\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "76,86,110,306,311", "startColumns": "4,4,4,4,4", "startOffsets": "4724,5239,6527,17621,17791", "endLines": "76,86,110,310,314", "endColumns": "56,64,63,24,24", "endOffsets": "4776,5299,6586,17786,17935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b2e92d4b87b684e16d60ca5e4e064096\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "6714", "endColumns": "82", "endOffsets": "6792"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763a4083062442933b972ee410371f1a\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "85,107", "startColumns": "4,4", "startOffsets": "5197,6363", "endColumns": "41,59", "endOffsets": "5234,6418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ada28ed5e61b93f14534594e8f74eaa\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6277", "endColumns": "42", "endOffsets": "6315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\412943cd7e22f7dc8c187c67b2cef78f\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "4781", "endColumns": "65", "endOffsets": "4842"}}]}]}