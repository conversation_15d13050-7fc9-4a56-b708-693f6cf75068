package com.example.gxzhaiwu.ui.usermanagement.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.UserDetail
import com.example.gxzhaiwu.data.model.UserManagementAction
import com.example.gxzhaiwu.data.model.getRoleColor
import com.example.gxzhaiwu.data.model.getRoleDisplayName
import com.example.gxzhaiwu.data.model.getStoreNames
import com.example.gxzhaiwu.ui.theme.cardElevationColor
import java.text.SimpleDateFormat
import java.util.*

/**
 * 用户卡片组件
 * 显示用户基本信息和操作按钮
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserCard(
    user: UserDetail,
    onAction: (UserManagementAction, UserDetail) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onAction(UserManagementAction.VIEW_DETAIL, user) },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.cardElevationColor(1)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 用户基本信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                // 左侧：用户信息
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    // 用户名和姓名
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = user.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        
                        // 角色标签
                        RoleChip(
                            role = user.getRoleDisplayName(),
                            color = user.getRoleColor()
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 用户名
                    Text(
                        text = "@${user.username}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    // 邮箱
                    Text(
                        text = user.email,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                // 右侧：操作菜单
                UserActionMenu(
                    user = user,
                    onAction = onAction
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 门店信息
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Store,
                    contentDescription = "门店",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = user.getStoreNames(),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 创建时间
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Schedule,
                    contentDescription = "创建时间",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = "创建于 ${formatDate(user.created_at)}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 角色标签组件
 */
@Composable
private fun RoleChip(
    role: String,
    color: String,
    modifier: Modifier = Modifier
) {
    val chipColor = when (color) {
        "primary" -> MaterialTheme.colorScheme.primary
        "secondary" -> MaterialTheme.colorScheme.secondary
        "tertiary" -> MaterialTheme.colorScheme.tertiary
        else -> MaterialTheme.colorScheme.surfaceVariant
    }
    
    val contentColor = when (color) {
        "primary" -> MaterialTheme.colorScheme.onPrimary
        "secondary" -> MaterialTheme.colorScheme.onSecondary
        "tertiary" -> MaterialTheme.colorScheme.onTertiary
        else -> MaterialTheme.colorScheme.onSurfaceVariant
    }
    
    Box(
        modifier = modifier
            .background(
                color = chipColor,
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = role,
            style = MaterialTheme.typography.labelSmall,
            color = contentColor,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 用户操作菜单组件
 */
@Composable
private fun UserActionMenu(
    user: UserDetail,
    onAction: (UserManagementAction, UserDetail) -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    
    Box(modifier = modifier) {
        IconButton(
            onClick = { expanded = true }
        ) {
            Icon(
                imageVector = Icons.Default.MoreVert,
                contentDescription = "更多操作",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            DropdownMenuItem(
                text = { Text("查看详情") },
                onClick = {
                    expanded = false
                    onAction(UserManagementAction.VIEW_DETAIL, user)
                },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Visibility,
                        contentDescription = null
                    )
                }
            )
            
            DropdownMenuItem(
                text = { Text("编辑角色") },
                onClick = {
                    expanded = false
                    onAction(UserManagementAction.EDIT_ROLES, user)
                },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.AdminPanelSettings,
                        contentDescription = null
                    )
                }
            )
            
            DropdownMenuItem(
                text = { Text("编辑门店") },
                onClick = {
                    expanded = false
                    onAction(UserManagementAction.EDIT_STORES, user)
                },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Store,
                        contentDescription = null
                    )
                }
            )
        }
    }
}

/**
 * 格式化日期
 */
private fun formatDate(dateString: String): String {
    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", Locale.getDefault())
        val outputFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())
        val date = inputFormat.parse(dateString)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        dateString.substringBefore('T')
    }
}
