package com.example.gxzhaiwu.ui.dashboard.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00008\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u001c\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\b\u0010\u0006\u001a\u00020\u0001H\u0003\u001a\"\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0003\u001a \u0010\u000b\u001a\u00020\u00012\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00030\r2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\b\u0010\u000e\u001a\u00020\u0001H\u0003\u001a\b\u0010\u000f\u001a\u00020\u0001H\u0003\u001a;\u0010\u0010\u001a\u00020\u00012\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00030\r2\u0006\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u0011\u0010\u0013\u001a\r\u0012\u0004\u0012\u00020\u00010\u0014\u00a2\u0006\u0002\b\u0015H\u0007\u001a \u0010\u0016\u001a\u00020\u00012\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00030\r2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\b\u0010\u0017\u001a\u00020\u0001H\u0003\u00a8\u0006\u0018"}, d2 = {"PermissionDeniedCard", "", "message", "", "modifier", "Landroidx/compose/ui/Modifier;", "PermissionDeniedCardPreview", "PermissionItem", "title", "enabled", "", "PermissionSummaryCard", "userRoles", "", "PermissionSummaryCardAdminPreview", "PermissionSummaryCardStaffPreview", "RoleBasedContent", "requiredPermission", "Lcom/example/gxzhaiwu/utils/Permission;", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "UserRoleBadge", "UserRoleBadgeAdminPreview", "app_debug"})
public final class RoleBasedContentKt {
    
    /**
     * 基于角色的内容显示组件
     * 根据用户权限动态显示或隐藏内容
     */
    @androidx.compose.runtime.Composable()
    public static final void RoleBasedContent(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> userRoles, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.utils.Permission requiredPermission, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 角色权限不足提示组件
     */
    @androidx.compose.runtime.Composable()
    public static final void PermissionDeniedCard(@org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 用户角色标识组件
     */
    @androidx.compose.runtime.Composable()
    public static final void UserRoleBadge(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> userRoles, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 权限功能卡片组件
     * 显示用户当前拥有的权限功能
     */
    @androidx.compose.runtime.Composable()
    public static final void PermissionSummaryCard(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> userRoles, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 权限项组件
     */
    @androidx.compose.runtime.Composable()
    private static final void PermissionItem(java.lang.String title, boolean enabled, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u7ba1\u7406\u5458\u89d2\u8272\u6807\u8bc6")
    @androidx.compose.runtime.Composable()
    private static final void UserRoleBadgeAdminPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u6743\u9650\u4e0d\u8db3\u63d0\u793a")
    @androidx.compose.runtime.Composable()
    private static final void PermissionDeniedCardPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u5e97\u5458\u6743\u9650\u6982\u89c8")
    @androidx.compose.runtime.Composable()
    private static final void PermissionSummaryCardStaffPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u7ba1\u7406\u5458\u6743\u9650\u6982\u89c8")
    @androidx.compose.runtime.Composable()
    private static final void PermissionSummaryCardAdminPreview() {
    }
}