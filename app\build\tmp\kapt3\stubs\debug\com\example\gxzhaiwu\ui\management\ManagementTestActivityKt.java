package com.example.gxzhaiwu.ui.management;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\u001a\u0016\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u0003\u001a\b\u0010\u0005\u001a\u00020\u0001H\u0003\u001a\u0016\u0010\u0006\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u0003\u001a\u000e\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u0002\u00a8\u0006\t"}, d2 = {"ManagementModulesTestCard", "", "userRoles", "", "", "ManagementTestScreen", "PermissionTestCard", "getAllTestModules", "Lcom/example/gxzhaiwu/data/model/ManagementModule;", "app_debug"})
public final class ManagementTestActivityKt {
    
    @androidx.compose.runtime.Composable()
    private static final void ManagementTestScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PermissionTestCard(java.util.List<java.lang.String> userRoles) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ManagementModulesTestCard(java.util.List<java.lang.String> userRoles) {
    }
    
    private static final java.util.List<com.example.gxzhaiwu.data.model.ManagementModule> getAllTestModules() {
        return null;
    }
}