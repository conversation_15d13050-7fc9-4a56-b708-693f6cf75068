package com.example.gxzhaiwu.data.repository;

import com.example.gxzhaiwu.data.api.AuthApi;
import com.example.gxzhaiwu.data.local.UserPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AuthRepositoryImpl_Factory implements Factory<AuthRepositoryImpl> {
  private final Provider<AuthApi> authApiProvider;

  private final Provider<UserPreferences> userPreferencesProvider;

  public AuthRepositoryImpl_Factory(Provider<AuthApi> authApiProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    this.authApiProvider = authApiProvider;
    this.userPreferencesProvider = userPreferencesProvider;
  }

  @Override
  public AuthRepositoryImpl get() {
    return newInstance(authApiProvider.get(), userPreferencesProvider.get());
  }

  public static AuthRepositoryImpl_Factory create(Provider<AuthApi> authApiProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    return new AuthRepositoryImpl_Factory(authApiProvider, userPreferencesProvider);
  }

  public static AuthRepositoryImpl newInstance(AuthApi authApi, UserPreferences userPreferences) {
    return new AuthRepositoryImpl(authApi, userPreferences);
  }
}
