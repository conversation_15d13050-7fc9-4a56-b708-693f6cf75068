package com.example.gxzhaiwu.data.api

import com.example.gxzhaiwu.data.model.*
import retrofit2.Response
import retrofit2.http.*

/**
 * 用户管理API接口
 * 提供用户管理相关的网络请求接口
 */
interface UserManagementApi {

    /**
     * 获取用户列表
     * 支持搜索、角色筛选和分页
     */
    @GET("users")
    suspend fun getUsers(
        @Header("Authorization") authorization: String,
        @Query("search") search: String? = null,
        @Query("role") role: String? = null,
        @Query("page") page: Int = 1,
        @Query("per_page") perPage: Int = 15
    ): Response<UserListResponse>

    /**
     * 获取用户详情
     */
    @GET("users/{id}")
    suspend fun getUserDetail(
        @Header("Authorization") authorization: String,
        @Path("id") userId: Int
    ): Response<UserDetailResponse>

    /**
     * 更新用户角色
     */
    @PUT("users/{id}/roles")
    suspend fun updateUserRoles(
        @Header("Authorization") authorization: String,
        @Path("id") userId: Int,
        @Body request: UpdateUserRolesRequest
    ): Response<UserDetailResponse>

    /**
     * 更新用户门店权限
     */
    @PUT("users/{id}/stores")
    suspend fun updateUserStores(
        @Header("Authorization") authorization: String,
        @Path("id") userId: Int,
        @Body request: UpdateUserStoresRequest
    ): Response<UserDetailResponse>

    /**
     * 获取角色列表
     */
    @GET("roles")
    suspend fun getRoles(
        @Header("Authorization") authorization: String
    ): Response<RoleListResponse>

    /**
     * 获取门店列表（用于分配门店权限）
     */
    @GET("stores")
    suspend fun getStores(
        @Header("Authorization") authorization: String
    ): Response<StoreListResponse>
}

/**
 * 门店列表响应数据模型（复用现有的门店模型）
 */
@kotlinx.serialization.Serializable
data class StoreListResponse(
    val success: Boolean,
    val data: List<Store>,
    val message: String? = null
)

/**
 * 门店数据模型（简化版，用于用户管理）
 */
@kotlinx.serialization.Serializable
data class Store(
    val id: Int,
    val name: String,
    val code: String,
    val address: String? = null,
    val phone: String? = null,
    val description: String? = null,
    val is_active: Boolean = true,
    val created_at: String? = null,
    val updated_at: String? = null
)
