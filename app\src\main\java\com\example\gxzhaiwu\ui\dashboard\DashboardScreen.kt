package com.example.gxzhaiwu.ui.dashboard

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.ui.components.*
import com.example.gxzhaiwu.ui.dashboard.components.*
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import com.example.gxzhaiwu.utils.Permission
import com.example.gxzhaiwu.utils.RoleUtils

/**
 * 仪表盘主屏幕
 * 显示系统概览、统计数据和快速操作
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DashboardScreen(
    onNavigateToScreen: (String) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: DashboardViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val snackbarHostState = remember { SnackbarHostState() }

    // 处理副作用
    LaunchedEffect(viewModel) {
        viewModel.sideEffect.collect { effect ->
            when (effect) {
                is DashboardSideEffect.ShowError -> {
                    snackbarHostState.showSnackbar(
                        message = effect.message,
                        actionLabel = "确定"
                    )
                }
                is DashboardSideEffect.ShowSnackbar -> {
                    snackbarHostState.showSnackbar(effect.message)
                }
                is DashboardSideEffect.NavigateToScreen -> {
                    onNavigateToScreen(effect.route)
                }
                is DashboardSideEffect.NavigateToLogin -> {
                    onNavigateToScreen("login")
                }
            }
        }
    }

    Scaffold(
        topBar = {
            DashboardTopBar(
                userRoles = uiState.userRoles,
                lastUpdated = uiState.lastUpdated,
                onRefresh = { viewModel.onEvent(DashboardEvent.RefreshData) }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        modifier = modifier
    ) { paddingValues ->
        RefreshableContent(
            isRefreshing = uiState.isRefreshing,
            onRefresh = { viewModel.onEvent(DashboardEvent.RefreshData) },
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    LoadingContent(message = "正在加载仪表盘数据...")
                }
                
                uiState.hasError -> {
                    ErrorContent(
                        message = uiState.primaryErrorMessage ?: "加载失败",
                        onRetry = { viewModel.onEvent(DashboardEvent.LoadData) }
                    )
                }
                
                uiState.showEmptyState -> {
                    EmptyContent(message = "暂无仪表盘数据")
                }
                
                else -> {
                    DashboardContent(
                        uiState = uiState,
                        onEvent = viewModel::onEvent
                    )
                }
            }
        }
    }
}

/**
 * 仪表盘顶部栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DashboardTopBar(
    userRoles: List<String>,
    lastUpdated: Long?,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Column {
                Text(
                    text = "仪表盘",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
                
                lastUpdated?.let { timestamp ->
                    Text(
                        text = "更新于 ${com.example.gxzhaiwu.utils.FormatUtils.formatRelativeTime(timestamp)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        },
        actions = {
            UserRoleBadge(userRoles = userRoles)
            
            Spacer(modifier = Modifier.width(8.dp))
            
            IconButton(onClick = onRefresh) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "刷新",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        modifier = modifier
    )
}

/**
 * 仪表盘内容区域
 */
@Composable
private fun DashboardContent(
    uiState: DashboardUiState,
    onEvent: (DashboardEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 财务汇总卡片（需要权限）
        item {
            RoleBasedContent(
                userRoles = uiState.userRoles,
                requiredPermission = Permission.VIEW_FINANCIAL_DETAILS
            ) {
                uiState.overview?.financial?.let { financial ->
                    FinancialSummaryCard(
                        financialSummary = financial,
                        onClick = { onEvent(DashboardEvent.StatisticsCardClicked(
                            StatisticsCardData(title = "财务详情", value = "", icon = "account_balance")
                        )) }
                    )
                }
            }
        }

        // 统计卡片网格
        item {
            if (uiState.statisticsCards.isNotEmpty()) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Text(
                        text = "数据概览",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    // 使用网格布局显示统计卡片
                    val chunkedCards = uiState.statisticsCards.chunked(2)
                    chunkedCards.forEach { rowCards ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            rowCards.forEach { card ->
                                StatisticsCard(
                                    data = card,
                                    onClick = { onEvent(DashboardEvent.StatisticsCardClicked(card)) },
                                    modifier = Modifier.weight(1f)
                                )
                            }
                            
                            // 如果行中只有一个卡片，添加空白占位
                            if (rowCards.size == 1) {
                                Spacer(modifier = Modifier.weight(1f))
                            }
                        }
                    }
                }
            }
        }

        // 快速操作卡片
        item {
            if (uiState.quickActions.isNotEmpty()) {
                QuickActionCard(
                    actions = uiState.quickActions,
                    onActionClick = { action ->
                        onEvent(DashboardEvent.QuickActionClicked(action))
                    }
                )
            }
        }

        // 高级统计（管理员和店长可见）
        item {
            RoleBasedContent(
                userRoles = uiState.userRoles,
                requiredPermission = Permission.ACCESS_ADVANCED_STATISTICS
            ) {
                uiState.statistics?.let { statistics ->
                    AdvancedStatisticsCard(
                        statistics = statistics,
                        permissions = uiState.permissions
                    )
                }
            }
        }

        // 权限概览卡片（调试用，可选）
        if (uiState.userRoles.isNotEmpty()) {
            item {
                PermissionSummaryCard(userRoles = uiState.userRoles)
            }
        }
    }
}

/**
 * 高级统计卡片
 */
@Composable
private fun AdvancedStatisticsCard(
    statistics: DashboardStatistics,
    permissions: DashboardPermissions,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 1.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "高级统计",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Text(
                text = "统计周期：${statistics.period.getDisplayRange()}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            // 门店对比（仅管理员可见）
            if (permissions.canViewStoreComparison && statistics.stores.isNotEmpty()) {
                Text(
                    text = "门店数据对比",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(statistics.stores) { store ->
                        StoreStatisticsItem(store = store)
                    }
                }
            }
        }
    }
}

/**
 * 门店统计项
 */
@Composable
private fun StoreStatisticsItem(
    store: StoreStatistics,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.width(160.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = store.store_name,
                style = MaterialTheme.typography.labelLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Text(
                text = "账单: ${store.invoice_count}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Text(
                text = "总额: ${com.example.gxzhaiwu.utils.FormatUtils.formatCurrency(store.total_amount)}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
    }
}

@Preview(name = "仪表盘屏幕预览")
@Composable
private fun DashboardScreenPreview() {
    GxZhaiWuTheme {
        Surface {
            // 预览内容将在实际使用时通过ViewModel提供
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text("仪表盘屏幕预览")
            }
        }
    }
}
