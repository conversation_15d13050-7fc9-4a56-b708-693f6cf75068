package com.example.gxzhaiwu.ui.usermanagement

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.data.repository.UserManagementRepository
import com.example.gxzhaiwu.utils.RoleUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 用户管理ViewModel
 * 管理用户管理页面的状态和业务逻辑
 */
@HiltViewModel
class UserManagementViewModel @Inject constructor(
    private val repository: UserManagementRepository
) : ViewModel() {

    // UI状态
    private val _uiState = MutableStateFlow(UserManagementUiState())
    val uiState: StateFlow<UserManagementUiState> = _uiState.asStateFlow()

    // 操作结果
    private val _operationResult = MutableSharedFlow<UserOperationResult>()
    val operationResult: SharedFlow<UserOperationResult> = _operationResult.asSharedFlow()

    // 筛选选项
    private val _filterOptions = MutableStateFlow(UserFilterOptions())
    val filterOptions: StateFlow<UserFilterOptions> = _filterOptions.asStateFlow()

    init {
        loadInitialData()
    }

    /**
     * 加载初始数据
     */
    private fun loadInitialData() {
        viewModelScope.launch {
            loadRoles()
            loadUsers()
        }
    }

    /**
     * 加载用户列表
     */
    fun loadUsers(refresh: Boolean = false) {
        viewModelScope.launch {
            if (refresh) {
                _uiState.value = _uiState.value.copy(isRefreshing = true)
            } else {
                _uiState.value = _uiState.value.copy(isLoading = true)
            }

            val currentFilter = _filterOptions.value
            val result = repository.getUsers(
                search = currentFilter.searchQuery.takeIf { it.isNotBlank() },
                role = currentFilter.selectedRole,
                page = currentFilter.page,
                perPage = currentFilter.perPage
            )

            result.fold(
                onSuccess = { paginatedData ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isRefreshing = false,
                        users = paginatedData.data,
                        currentPage = paginatedData.current_page,
                        totalPages = paginatedData.last_page,
                        totalUsers = paginatedData.total,
                        error = null
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isRefreshing = false,
                        error = exception.message ?: "加载用户列表失败"
                    )
                }
            )
        }
    }

    /**
     * 加载角色列表
     */
    private fun loadRoles() {
        viewModelScope.launch {
            val result = repository.getRoles()
            result.fold(
                onSuccess = { roles ->
                    _uiState.value = _uiState.value.copy(roles = roles)
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message ?: "加载角色列表失败"
                    )
                }
            )
        }
    }

    /**
     * 搜索用户
     */
    fun searchUsers(query: String) {
        _filterOptions.value = _filterOptions.value.copy(
            searchQuery = query,
            page = 1 // 重置到第一页
        )
        _uiState.value = _uiState.value.copy(searchQuery = query)
        loadUsers()
    }

    /**
     * 筛选角色
     */
    fun filterByRole(role: String?) {
        _filterOptions.value = _filterOptions.value.copy(
            selectedRole = role,
            page = 1 // 重置到第一页
        )
        _uiState.value = _uiState.value.copy(selectedRole = role)
        loadUsers()
    }

    /**
     * 加载下一页
     */
    fun loadNextPage() {
        val currentState = _uiState.value
        if (currentState.currentPage < currentState.totalPages && !currentState.isLoading) {
            _filterOptions.value = _filterOptions.value.copy(
                page = currentState.currentPage + 1
            )
            loadUsers()
        }
    }

    /**
     * 加载上一页
     */
    fun loadPreviousPage() {
        val currentState = _uiState.value
        if (currentState.currentPage > 1 && !currentState.isLoading) {
            _filterOptions.value = _filterOptions.value.copy(
                page = currentState.currentPage - 1
            )
            loadUsers()
        }
    }

    /**
     * 显示用户详情
     */
    fun showUserDetail(user: UserDetail) {
        _uiState.value = _uiState.value.copy(
            selectedUser = user,
            showUserDetail = true
        )
    }

    /**
     * 隐藏用户详情
     */
    fun hideUserDetail() {
        _uiState.value = _uiState.value.copy(
            selectedUser = null,
            showUserDetail = false
        )
    }

    /**
     * 显示角色编辑对话框
     */
    fun showRoleDialog(user: UserDetail) {
        _uiState.value = _uiState.value.copy(
            selectedUser = user,
            showRoleDialog = true
        )
    }

    /**
     * 隐藏角色编辑对话框
     */
    fun hideRoleDialog() {
        _uiState.value = _uiState.value.copy(
            selectedUser = null,
            showRoleDialog = false
        )
    }

    /**
     * 显示门店编辑对话框
     */
    fun showStoreDialog(user: UserDetail) {
        _uiState.value = _uiState.value.copy(
            selectedUser = user,
            showStoreDialog = true
        )
    }

    /**
     * 隐藏门店编辑对话框
     */
    fun hideStoreDialog() {
        _uiState.value = _uiState.value.copy(
            selectedUser = null,
            showStoreDialog = false
        )
    }

    /**
     * 更新用户角色
     */
    fun updateUserRoles(userId: Int, roleIds: List<Int>) {
        viewModelScope.launch {
            _operationResult.emit(UserOperationResult.Loading)

            val result = repository.updateUserRoles(userId, roleIds)
            result.fold(
                onSuccess = { updatedUser ->
                    _operationResult.emit(UserOperationResult.Success)
                    // 更新UI中的用户信息
                    updateUserInList(updatedUser)
                    hideRoleDialog()
                },
                onFailure = { exception ->
                    _operationResult.emit(
                        UserOperationResult.Error(
                            exception.message ?: "更新用户角色失败"
                        )
                    )
                }
            )
        }
    }

    /**
     * 更新用户门店权限
     */
    fun updateUserStores(userId: Int, storeIds: List<Int>) {
        viewModelScope.launch {
            _operationResult.emit(UserOperationResult.Loading)

            val result = repository.updateUserStores(userId, storeIds)
            result.fold(
                onSuccess = { updatedUser ->
                    _operationResult.emit(UserOperationResult.Success)
                    // 更新UI中的用户信息
                    updateUserInList(updatedUser)
                    hideStoreDialog()
                },
                onFailure = { exception ->
                    _operationResult.emit(
                        UserOperationResult.Error(
                            exception.message ?: "更新用户门店权限失败"
                        )
                    )
                }
            )
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        loadUsers(refresh = true)
    }

    /**
     * 更新列表中的用户信息
     */
    private fun updateUserInList(updatedUser: UserDetail) {
        val currentUsers = _uiState.value.users.toMutableList()
        val index = currentUsers.indexOfFirst { it.id == updatedUser.id }
        if (index != -1) {
            currentUsers[index] = updatedUser
            _uiState.value = _uiState.value.copy(users = currentUsers)
        }
    }

    /**
     * 检查当前用户是否有管理权限
     */
    fun hasManagementPermission(currentUserRoles: List<String>): Boolean {
        return RoleUtils.hasPermission(currentUserRoles, com.example.gxzhaiwu.utils.Permission.MANAGE_USERS)
    }
}
