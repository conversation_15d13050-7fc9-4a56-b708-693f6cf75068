package com.example.gxzhaiwu.ui.navigation

sealed class Screen(val route: String) {
    object Login : Screen("login")
    object Register : Screen("register")
    object Home : Screen("home")
    object Dashboard : Screen("dashboard")

    // 底部导航主页面
    object MainContainer : Screen("main_container")
    object MainDashboard : Screen("main_dashboard")
    object MainBills : Screen("main_bills")
    object MainPayments : Screen("main_payments")
    object MainManagement : Screen("main_management")

    // 账单相关
    object InvoiceList : Screen("invoice_list")
    object InvoiceDetail : Screen("invoice_detail/{invoiceId}") {
        fun createRoute(invoiceId: Int) = "invoice_detail/$invoiceId"
    }
    object CreateInvoice : Screen("create_invoice")
    object BillManagement : Screen("bill_management")
    object BillDetail : Screen("bill_detail/{billId}") {
        fun createRoute(billId: Int) = "bill_detail/$billId"
    }
    object CreateBill : Screen("create_bill")
    object EditBill : Screen("edit_bill/{billId}") {
        fun createRoute(billId: Int) = "edit_bill/$billId"
    }

    // 客户相关
    object CustomerList : Screen("customer_list")
    object CustomerDetail : Screen("customer_detail/{customerId}") {
        fun createRoute(customerId: Int) = "customer_detail/$customerId"
    }
    object CreateCustomer : Screen("create_customer")
    object CustomerManagement : Screen("customer_management")

    // 还款相关
    object PaymentList : Screen("payment_list")
    object CreatePayment : Screen("create_payment")
    object PaymentManagement : Screen("payment_management")
    object PaymentDetail : Screen("payment_detail/{paymentId}") {
        fun createRoute(paymentId: Int) = "payment_detail/$paymentId"
    }
    object RecordPayment : Screen("record_payment")

    // 门店相关
    object StoreList : Screen("store_list")
    object StoreDetail : Screen("store_detail/{storeId}") {
        fun createRoute(storeId: Int) = "store_detail/$storeId"
    }
    object StoreManagement : Screen("store_management")

    // 管理相关
    object ManagementCenter : Screen("management_center")
    object UserManagement : Screen("user_management")
    object SystemManagement : Screen("system_management")

    // 设置相关
    object Settings : Screen("settings")
    object Profile : Screen("profile")
}
