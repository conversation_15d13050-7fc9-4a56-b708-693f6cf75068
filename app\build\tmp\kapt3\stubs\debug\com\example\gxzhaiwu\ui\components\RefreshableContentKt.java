package com.example.gxzhaiwu.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a\u001c\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\b\u0010\u0006\u001a\u00020\u0001H\u0003\u001a,\u0010\u0007\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\t2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\b\u0010\n\u001a\u00020\u0001H\u0003\u001a\u001c\u0010\u000b\u001a\u00020\u00012\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\b\u0010\f\u001a\u00020\u0001H\u0003\u001a;\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u0011\u0010\u0011\u001a\r\u0012\u0004\u0012\u00020\u00010\t\u00a2\u0006\u0002\b\u0012H\u0007\u00a8\u0006\u0013"}, d2 = {"EmptyContent", "", "message", "", "modifier", "Landroidx/compose/ui/Modifier;", "EmptyContentPreview", "ErrorContent", "onRetry", "Lkotlin/Function0;", "ErrorContentPreview", "LoadingContent", "LoadingContentPreview", "RefreshableContent", "isRefreshing", "", "onRefresh", "content", "Landroidx/compose/runtime/Composable;", "app_debug"})
public final class RefreshableContentKt {
    
    /**
     * 可刷新内容组件
     * 提供下拉刷新功能的通用容器
     */
    @androidx.compose.runtime.Composable()
    public static final void RefreshableContent(boolean isRefreshing, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRefresh, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 加载状态组件
     */
    @androidx.compose.runtime.Composable()
    public static final void LoadingContent(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    /**
     * 错误状态组件
     */
    @androidx.compose.runtime.Composable()
    public static final void ErrorContent(@org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 空状态组件
     */
    @androidx.compose.runtime.Composable()
    public static final void EmptyContent(@org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u52a0\u8f7d\u72b6\u6001")
    @androidx.compose.runtime.Composable()
    private static final void LoadingContentPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u9519\u8bef\u72b6\u6001")
    @androidx.compose.runtime.Composable()
    private static final void ErrorContentPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u7a7a\u72b6\u6001")
    @androidx.compose.runtime.Composable()
    private static final void EmptyContentPreview() {
    }
}