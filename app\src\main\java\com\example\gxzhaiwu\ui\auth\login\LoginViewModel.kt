package com.example.gxzhaiwu.ui.auth.login

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gxzhaiwu.data.model.LoginRequest
import com.example.gxzhaiwu.data.repository.AuthRepository
import com.example.gxzhaiwu.utils.ValidationUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()

    fun updateLogin(login: String) {
        _uiState.value = _uiState.value.copy(
            login = login,
            loginError = ValidationUtils.validateLogin(login),
            errorMessage = null
        )
    }

    fun updatePassword(password: String) {
        _uiState.value = _uiState.value.copy(
            password = password,
            passwordError = ValidationUtils.validatePassword(password),
            errorMessage = null
        )
    }

    fun login() {
        val currentState = _uiState.value
        
        // 验证表单
        val loginError = ValidationUtils.validateLogin(currentState.login)
        val passwordError = ValidationUtils.validatePassword(currentState.password)
        
        if (loginError != null || passwordError != null) {
            _uiState.value = currentState.copy(
                loginError = loginError,
                passwordError = passwordError
            )
            return
        }

        // 开始登录
        _uiState.value = currentState.copy(
            isLoading = true,
            errorMessage = null
        )

        viewModelScope.launch {
            val loginRequest = LoginRequest(
                login = currentState.login.trim(),
                password = currentState.password
            )

            authRepository.login(loginRequest)
                .onSuccess { (token, user) ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isLoginSuccessful = true,
                        errorMessage = null
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isLoginSuccessful = false,
                        errorMessage = exception.message ?: "登录失败，请重试"
                    )
                }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun resetLoginSuccess() {
        _uiState.value = _uiState.value.copy(isLoginSuccessful = false)
    }
}
