package com.example.gxzhaiwu.ui.bills.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.ui.bills.BillAction
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * 账单卡片组件
 * 显示账单基本信息和操作按钮
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BillCard(
    bill: Bill,
    availableActions: List<BillAction>,
    onBillClick: () -> Unit,
    onActionClick: (BillAction) -> Unit,
    modifier: Modifier = Modifier
) {
    var showActionMenu by remember { mutableStateOf(false) }

    Card(
        onClick = onBillClick,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp,
            pressedElevation = 4.dp
        ),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 头部信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = bill.billNumber,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    Text(
                        text = bill.customerName,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }

                // 状态标签
                BillStatusChip(
                    status = bill.status,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 金额信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "总金额",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "¥${bill.totalAmount}",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }

                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = "剩余金额",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "¥${bill.remainingAmount}",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = if (bill.remainingAmount > BigDecimal.ZERO) {
                            MaterialTheme.colorScheme.error
                        } else {
                            MaterialTheme.colorScheme.primary
                        }
                    )
                }
            }

            // 付款进度条
            if (bill.totalAmount > BigDecimal.ZERO) {
                Spacer(modifier = Modifier.height(8.dp))
                
                Column {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "付款进度",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "${(bill.getPaymentProgress() * 100).toInt()}%",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    LinearProgressIndicator(
                        progress = bill.getPaymentProgress(),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 4.dp),
                        color = MaterialTheme.colorScheme.primary,
                        trackColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 底部信息和操作
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = bill.storeName,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    bill.dueDate?.let { dueDate ->
                        Text(
                            text = "到期: ${dueDate.format(DateTimeFormatter.ofPattern("MM/dd"))}",
                            style = MaterialTheme.typography.bodySmall,
                            color = if (bill.isOverdue()) {
                                MaterialTheme.colorScheme.error
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            }
                        )
                    }
                }

                // 操作菜单
                Box {
                    IconButton(
                        onClick = { showActionMenu = true }
                    ) {
                        Icon(
                            imageVector = Icons.Default.MoreVert,
                            contentDescription = "更多操作"
                        )
                    }

                    DropdownMenu(
                        expanded = showActionMenu,
                        onDismissRequest = { showActionMenu = false }
                    ) {
                        availableActions.forEach { action ->
                            DropdownMenuItem(
                                text = { Text(action.displayName) },
                                onClick = {
                                    showActionMenu = false
                                    onActionClick(action)
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = getActionIcon(action),
                                        contentDescription = null
                                    )
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 账单状态芯片
 */
@Composable
fun BillStatusChip(
    status: BillStatus,
    modifier: Modifier = Modifier
) {
    val colors = when (status) {
        BillStatus.PENDING -> AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer,
            labelColor = MaterialTheme.colorScheme.onSecondaryContainer
        )
        BillStatus.PARTIAL_PAID -> AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.tertiaryContainer,
            labelColor = MaterialTheme.colorScheme.onTertiaryContainer
        )
        BillStatus.PAID -> AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer,
            labelColor = MaterialTheme.colorScheme.onPrimaryContainer
        )
        BillStatus.OVERDUE -> AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.errorContainer,
            labelColor = MaterialTheme.colorScheme.onErrorContainer
        )
        BillStatus.CANCELLED -> AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant,
            labelColor = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }

    AssistChip(
        onClick = { },
        label = {
            Text(
                text = status.displayName,
                style = MaterialTheme.typography.labelSmall
            )
        },
        colors = colors,
        modifier = modifier
    )
}

/**
 * 获取操作图标
 */
private fun getActionIcon(action: BillAction) = when (action) {
    BillAction.VIEW -> Icons.Default.Visibility
    BillAction.EDIT -> Icons.Default.Edit
    BillAction.DELETE -> Icons.Default.Delete
    BillAction.RECORD_PAYMENT -> Icons.Default.Payment
    BillAction.DUPLICATE -> Icons.Default.ContentCopy
    BillAction.EXPORT -> Icons.Default.FileDownload
}

// 预览组件
@Preview(name = "账单卡片")
@Composable
private fun BillCardPreview() {
    GxZhaiWuTheme {
        Surface {
            BillCard(
                bill = Bill(
                    id = 1,
                    billNumber = "BILL-2024-001",
                    customerId = 1,
                    customerName = "张三",
                    storeId = 1,
                    storeName = "总店",
                    totalAmount = BigDecimal("1000.00"),
                    paidAmount = BigDecimal("300.00"),
                    remainingAmount = BigDecimal("700.00"),
                    status = BillStatus.PARTIAL_PAID,
                    dueDate = LocalDateTime.now().plusDays(7),
                    createdAt = LocalDateTime.now().minusDays(3),
                    updatedAt = LocalDateTime.now().minusDays(1)
                ),
                availableActions = listOf(BillAction.VIEW, BillAction.EDIT, BillAction.RECORD_PAYMENT),
                onBillClick = {},
                onActionClick = {},
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}
