package com.example.gxzhaiwu.ui.home

import androidx.lifecycle.ViewModel
import com.example.gxzhaiwu.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    val currentUser = authRepository.getCurrentUserFlow()

    suspend fun logout() {
        authRepository.logout()
    }
}
