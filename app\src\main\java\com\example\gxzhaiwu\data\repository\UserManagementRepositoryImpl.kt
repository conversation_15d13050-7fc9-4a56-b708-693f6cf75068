package com.example.gxzhaiwu.data.repository

import com.example.gxzhaiwu.data.api.UserManagementApi
import com.example.gxzhaiwu.data.local.UserPreferences
import com.example.gxzhaiwu.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户管理Repository实现类
 * 处理用户管理相关的数据操作和缓存管理
 */
@Singleton
class UserManagementRepositoryImpl @Inject constructor(
    private val api: UserManagementApi,
    private val userPreferences: UserPreferences
) : UserManagementRepository {

    // 用户列表缓存
    private val _cachedUsers = MutableStateFlow<List<UserDetail>>(emptyList())
    private val cachedUsers = _cachedUsers.asStateFlow()

    // 角色列表缓存
    private val _cachedRoles = MutableStateFlow<List<Role>>(emptyList())
    private val cachedRoles = _cachedRoles.asStateFlow()

    // 门店列表缓存
    private val _cachedStores = MutableStateFlow<List<com.example.gxzhaiwu.data.api.Store>>(emptyList())
    private val cachedStores = _cachedStores.asStateFlow()

    /**
     * 获取认证头
     */
    private suspend fun getAuthHeader(): String {
        val token = userPreferences.authToken.first()
        return "Bearer $token"
    }

    /**
     * 获取用户列表
     */
    override suspend fun getUsers(
        search: String?,
        role: String?,
        page: Int,
        perPage: Int
    ): Result<PaginatedUserData> {
        return try {
            val response = api.getUsers(
                authorization = getAuthHeader(),
                search = search,
                role = role,
                page = page,
                perPage = perPage
            )

            if (response.isSuccessful) {
                val userListResponse = response.body()
                if (userListResponse?.success == true) {
                    // 更新缓存（仅在第一页且无筛选条件时）
                    if (page == 1 && search.isNullOrBlank() && role.isNullOrBlank()) {
                        _cachedUsers.value = userListResponse.data.data
                    }
                    Result.success(userListResponse.data)
                } else {
                    Result.failure(Exception(userListResponse?.message ?: "获取用户列表失败"))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取用户详情
     */
    override suspend fun getUserDetail(userId: Int): Result<UserDetail> {
        return try {
            val response = api.getUserDetail(
                authorization = getAuthHeader(),
                userId = userId
            )

            if (response.isSuccessful) {
                val userDetailResponse = response.body()
                if (userDetailResponse?.success == true) {
                    // 更新缓存中的用户信息
                    updateUserInCache(userDetailResponse.data)
                    Result.success(userDetailResponse.data)
                } else {
                    Result.failure(Exception(userDetailResponse?.message ?: "获取用户详情失败"))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 更新用户角色
     */
    override suspend fun updateUserRoles(userId: Int, roleIds: List<Int>): Result<UserDetail> {
        return try {
            val request = UpdateUserRolesRequest(roleIds)
            val response = api.updateUserRoles(
                authorization = getAuthHeader(),
                userId = userId,
                request = request
            )

            if (response.isSuccessful) {
                val userDetailResponse = response.body()
                if (userDetailResponse?.success == true) {
                    // 更新缓存中的用户信息
                    updateUserInCache(userDetailResponse.data)
                    Result.success(userDetailResponse.data)
                } else {
                    Result.failure(Exception(userDetailResponse?.message ?: "更新用户角色失败"))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 更新用户门店权限
     */
    override suspend fun updateUserStores(userId: Int, storeIds: List<Int>): Result<UserDetail> {
        return try {
            val request = UpdateUserStoresRequest(storeIds)
            val response = api.updateUserStores(
                authorization = getAuthHeader(),
                userId = userId,
                request = request
            )

            if (response.isSuccessful) {
                val userDetailResponse = response.body()
                if (userDetailResponse?.success == true) {
                    // 更新缓存中的用户信息
                    updateUserInCache(userDetailResponse.data)
                    Result.success(userDetailResponse.data)
                } else {
                    Result.failure(Exception(userDetailResponse?.message ?: "更新用户门店权限失败"))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取角色列表
     */
    override suspend fun getRoles(): Result<List<Role>> {
        return try {
            // 先检查缓存
            if (_cachedRoles.value.isNotEmpty()) {
                return Result.success(_cachedRoles.value)
            }

            val response = api.getRoles(authorization = getAuthHeader())

            if (response.isSuccessful) {
                val roleListResponse = response.body()
                if (roleListResponse?.success == true) {
                    // 更新缓存
                    _cachedRoles.value = roleListResponse.data
                    Result.success(roleListResponse.data)
                } else {
                    Result.failure(Exception(roleListResponse?.message ?: "获取角色列表失败"))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取门店列表
     */
    override suspend fun getStores(): Result<List<com.example.gxzhaiwu.data.api.Store>> {
        return try {
            // 先检查缓存
            if (_cachedStores.value.isNotEmpty()) {
                return Result.success(_cachedStores.value)
            }

            val response = api.getStores(authorization = getAuthHeader())

            if (response.isSuccessful) {
                val storeListResponse = response.body()
                if (storeListResponse?.success == true) {
                    // 更新缓存
                    _cachedStores.value = storeListResponse.data
                    Result.success(storeListResponse.data)
                } else {
                    Result.failure(Exception(storeListResponse?.message ?: "获取门店列表失败"))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 搜索用户（本地缓存搜索）
     */
    override fun searchUsers(query: String): Flow<List<UserDetail>> {
        return cachedUsers.map { users ->
            if (query.isBlank()) {
                users
            } else {
                users.filter { user ->
                    user.name.contains(query, ignoreCase = true) ||
                    user.username?.contains(query, ignoreCase = true) == true ||
                    user.email.contains(query, ignoreCase = true)
                }
            }
        }
    }

    /**
     * 获取缓存的用户列表
     */
    override fun getCachedUsers(): Flow<List<UserDetail>> {
        return cachedUsers
    }

    /**
     * 清除缓存
     */
    override suspend fun clearCache() {
        _cachedUsers.value = emptyList()
        _cachedRoles.value = emptyList()
        _cachedStores.value = emptyList()
    }

    /**
     * 更新缓存中的用户信息
     */
    private fun updateUserInCache(updatedUser: UserDetail) {
        val currentUsers = _cachedUsers.value.toMutableList()
        val index = currentUsers.indexOfFirst { it.id == updatedUser.id }
        if (index != -1) {
            currentUsers[index] = updatedUser
            _cachedUsers.value = currentUsers
        }
    }
}
