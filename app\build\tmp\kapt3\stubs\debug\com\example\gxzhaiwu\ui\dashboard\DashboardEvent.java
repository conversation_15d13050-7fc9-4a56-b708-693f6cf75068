package com.example.gxzhaiwu.ui.dashboard;

/**
 * 仪表盘事件类型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0007\u0003\u0004\u0005\u0006\u0007\b\tB\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0007\n\u000b\f\r\u000e\u000f\u0010\u00a8\u0006\u0011"}, d2 = {"Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent;", "", "()V", "ClearError", "LoadData", "QuickActionClicked", "RefreshData", "SelectDateRange", "StatisticsCardClicked", "ToggleAdvancedStatistics", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$ClearError;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$LoadData;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$QuickActionClicked;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$RefreshData;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$SelectDateRange;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$StatisticsCardClicked;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$ToggleAdvancedStatistics;", "app_debug"})
public abstract class DashboardEvent {
    
    private DashboardEvent() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$ClearError;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent;", "()V", "app_debug"})
    public static final class ClearError extends com.example.gxzhaiwu.ui.dashboard.DashboardEvent {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.dashboard.DashboardEvent.ClearError INSTANCE = null;
        
        private ClearError() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$LoadData;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent;", "()V", "app_debug"})
    public static final class LoadData extends com.example.gxzhaiwu.ui.dashboard.DashboardEvent {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.dashboard.DashboardEvent.LoadData INSTANCE = null;
        
        private LoadData() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$QuickActionClicked;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent;", "action", "Lcom/example/gxzhaiwu/data/model/QuickActionData;", "(Lcom/example/gxzhaiwu/data/model/QuickActionData;)V", "getAction", "()Lcom/example/gxzhaiwu/data/model/QuickActionData;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class QuickActionClicked extends com.example.gxzhaiwu.ui.dashboard.DashboardEvent {
        @org.jetbrains.annotations.NotNull()
        private final com.example.gxzhaiwu.data.model.QuickActionData action = null;
        
        public QuickActionClicked(@org.jetbrains.annotations.NotNull()
        com.example.gxzhaiwu.data.model.QuickActionData action) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.gxzhaiwu.data.model.QuickActionData getAction() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.gxzhaiwu.data.model.QuickActionData component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.gxzhaiwu.ui.dashboard.DashboardEvent.QuickActionClicked copy(@org.jetbrains.annotations.NotNull()
        com.example.gxzhaiwu.data.model.QuickActionData action) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$RefreshData;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent;", "()V", "app_debug"})
    public static final class RefreshData extends com.example.gxzhaiwu.ui.dashboard.DashboardEvent {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.gxzhaiwu.ui.dashboard.DashboardEvent.RefreshData INSTANCE = null;
        
        private RefreshData() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u000f\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0004J\u000b\u0010\u0007\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0015\u0010\b\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$SelectDateRange;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent;", "dateRange", "Lcom/example/gxzhaiwu/ui/dashboard/DateRange;", "(Lcom/example/gxzhaiwu/ui/dashboard/DateRange;)V", "getDateRange", "()Lcom/example/gxzhaiwu/ui/dashboard/DateRange;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class SelectDateRange extends com.example.gxzhaiwu.ui.dashboard.DashboardEvent {
        @org.jetbrains.annotations.Nullable()
        private final com.example.gxzhaiwu.ui.dashboard.DateRange dateRange = null;
        
        public SelectDateRange(@org.jetbrains.annotations.Nullable()
        com.example.gxzhaiwu.ui.dashboard.DateRange dateRange) {
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.example.gxzhaiwu.ui.dashboard.DateRange getDateRange() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.example.gxzhaiwu.ui.dashboard.DateRange component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.gxzhaiwu.ui.dashboard.DashboardEvent.SelectDateRange copy(@org.jetbrains.annotations.Nullable()
        com.example.gxzhaiwu.ui.dashboard.DateRange dateRange) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$StatisticsCardClicked;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent;", "card", "Lcom/example/gxzhaiwu/data/model/StatisticsCardData;", "(Lcom/example/gxzhaiwu/data/model/StatisticsCardData;)V", "getCard", "()Lcom/example/gxzhaiwu/data/model/StatisticsCardData;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class StatisticsCardClicked extends com.example.gxzhaiwu.ui.dashboard.DashboardEvent {
        @org.jetbrains.annotations.NotNull()
        private final com.example.gxzhaiwu.data.model.StatisticsCardData card = null;
        
        public StatisticsCardClicked(@org.jetbrains.annotations.NotNull()
        com.example.gxzhaiwu.data.model.StatisticsCardData card) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.gxzhaiwu.data.model.StatisticsCardData getCard() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.gxzhaiwu.data.model.StatisticsCardData component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.gxzhaiwu.ui.dashboard.DashboardEvent.StatisticsCardClicked copy(@org.jetbrains.annotations.NotNull()
        com.example.gxzhaiwu.data.model.StatisticsCardData card) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\u00032\b\u0010\n\u001a\u0004\u0018\u00010\u000bH\u00d6\u0003J\t\u0010\f\u001a\u00020\rH\u00d6\u0001J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent$ToggleAdvancedStatistics;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent;", "show", "", "(Z)V", "getShow", "()Z", "component1", "copy", "equals", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class ToggleAdvancedStatistics extends com.example.gxzhaiwu.ui.dashboard.DashboardEvent {
        private final boolean show = false;
        
        public ToggleAdvancedStatistics(boolean show) {
        }
        
        public final boolean getShow() {
            return false;
        }
        
        public final boolean component1() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.gxzhaiwu.ui.dashboard.DashboardEvent.ToggleAdvancedStatistics copy(boolean show) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}