package com.example.gxzhaiwu.utils

object TestUtils {
    
    // 测试用户凭据
    object TestCredentials {
        const val VALID_EMAIL = "<EMAIL>"
        const val VALID_USERNAME = "testuser"
        const val VALID_PASSWORD = "password123"
        
        const val INVALID_EMAIL = "invalid-email"
        const val INVALID_PASSWORD = "123" // 太短
        const val EMPTY_LOGIN = ""
        const val EMPTY_PASSWORD = ""
        
        val LONG_EMAIL = "a".repeat(256) + "@example.com" // 超过最大长度
        val LONG_USERNAME = "a".repeat(256) // 超过最大长度
    }
    
    // 测试网络响应
    object TestResponses {
        const val SUCCESS_LOGIN_JSON = """
        {
            "success": true,
            "data": {
                "user": {
                    "id": 1,
                    "name": "测试用户",
                    "username": "testuser",
                    "email": "<EMAIL>",
                    "email_verified_at": null,
                    "created_at": "2024-01-01T00:00:00.000000Z",
                    "updated_at": "2024-01-01T00:00:00.000000Z",
                    "roles": ["user"],
                    "stores": []
                },
                "token": "test-jwt-token-12345"
            },
            "message": "登录成功"
        }
        """
        
        const val ERROR_INVALID_CREDENTIALS_JSON = """
        {
            "success": false,
            "message": "用户名或密码错误",
            "errors": {
                "login": ["用户名或密码错误"]
            }
        }
        """
        
        const val ERROR_VALIDATION_JSON = """
        {
            "success": false,
            "message": "验证失败",
            "errors": {
                "login": ["请输入邮箱或用户名"],
                "password": ["密码长度不能少于6位"]
            }
        }
        """
        
        const val ERROR_NETWORK_JSON = """
        {
            "success": false,
            "message": "网络连接失败"
        }
        """
    }
    
    // 测试场景
    enum class TestScenario {
        VALID_LOGIN,
        INVALID_CREDENTIALS,
        VALIDATION_ERROR,
        NETWORK_ERROR,
        EMPTY_FIELDS,
        LONG_INPUT
    }
    
    fun getTestCredentials(scenario: TestScenario): Pair<String, String> {
        return when (scenario) {
            TestScenario.VALID_LOGIN -> TestCredentials.VALID_EMAIL to TestCredentials.VALID_PASSWORD
            TestScenario.INVALID_CREDENTIALS -> TestCredentials.VALID_EMAIL to "wrongpassword"
            TestScenario.VALIDATION_ERROR -> TestCredentials.INVALID_EMAIL to TestCredentials.INVALID_PASSWORD
            TestScenario.NETWORK_ERROR -> TestCredentials.VALID_EMAIL to TestCredentials.VALID_PASSWORD
            TestScenario.EMPTY_FIELDS -> TestCredentials.EMPTY_LOGIN to TestCredentials.EMPTY_PASSWORD
            TestScenario.LONG_INPUT -> TestCredentials.LONG_EMAIL to TestCredentials.VALID_PASSWORD
        }
    }
}
