package com.example.gxzhaiwu.data.repository;

import com.example.gxzhaiwu.data.api.UserManagementApi;
import com.example.gxzhaiwu.data.local.UserPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class UserManagementRepositoryImpl_Factory implements Factory<UserManagementRepositoryImpl> {
  private final Provider<UserManagementApi> apiProvider;

  private final Provider<UserPreferences> userPreferencesProvider;

  public UserManagementRepositoryImpl_Factory(Provider<UserManagementApi> apiProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    this.apiProvider = apiProvider;
    this.userPreferencesProvider = userPreferencesProvider;
  }

  @Override
  public UserManagementRepositoryImpl get() {
    return newInstance(apiProvider.get(), userPreferencesProvider.get());
  }

  public static UserManagementRepositoryImpl_Factory create(Provider<UserManagementApi> apiProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    return new UserManagementRepositoryImpl_Factory(apiProvider, userPreferencesProvider);
  }

  public static UserManagementRepositoryImpl newInstance(UserManagementApi api,
      UserPreferences userPreferences) {
    return new UserManagementRepositoryImpl(api, userPreferences);
  }
}
