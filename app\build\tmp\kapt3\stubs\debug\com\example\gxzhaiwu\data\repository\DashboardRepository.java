package com.example.gxzhaiwu.data.repository;

/**
 * 仪表盘数据仓库接口
 * 提供仪表盘相关数据的访问抽象
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\bf\u0018\u00002\u00020\u0001J\u001c\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0005\u0010\u0006J4\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\f\u0010\r\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u000e"}, d2 = {"Lcom/example/gxzhaiwu/data/repository/DashboardRepository;", "", "getOverview", "Lkotlin/Result;", "Lcom/example/gxzhaiwu/data/model/DashboardOverview;", "getOverview-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getStatistics", "Lcom/example/gxzhaiwu/data/model/DashboardStatistics;", "startDate", "", "endDate", "getStatistics-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface DashboardRepository {
    
    /**
     * 仪表盘数据仓库接口
     * 提供仪表盘相关数据的访问抽象
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}