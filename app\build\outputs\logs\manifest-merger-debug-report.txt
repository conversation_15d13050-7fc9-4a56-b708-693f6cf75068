-- Merging decision tree log ---
manifest
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:2:1-33:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd1004847bd6d5ce61d9c60cb4287ad5\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed654ba23f42fde4b700be0d32f1d2c6\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c19a4f0623c1c480ca6e3f53e35bdcb\transformed\navigation-common-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c99abe88a69f82b4efe622efde03120\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\957dbd44cba30cfeb783bc775f75253e\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\caee232b08ec2fa4e2b3b98af7baf03a\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b98823ec0e24b426741077cc82e78c\transformed\navigation-compose-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96a4bd88eed9cbd0e46dbdb61835a31\transformed\hilt-android-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5addc3b44c294d89924313c3ea377a3b\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34672be5908381f92b2f1efab7609872\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97ebc98232b056e900235e30ef067680\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc1b6a98fe89f0376a3b88e6881572ba\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8e0697a2e839a8818ab2e625f9e61b1\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78092803766c0a9776e86e2f802ce3a7\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\886e1afd64882135b27d7a7faaa08e48\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98f289bf6c45d262a7223b262b65f304\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f034a1b5cd81e4ff40dadeef5ee60c3\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e138f0002d4776600ec3ef8c1d624d8\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412943cd7e22f7dc8c187c67b2cef78f\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\552c2d16ebacaf2ec7ea18f1b706cb2c\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2bd8f8ca0f1f58bbcff76784424ab33\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a93f44d94e792e6ae8273853680006b3\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17cfcbb607414ceb3b2c0fa072cdc3a\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0218312c92cc52cb8b5d2bc6690d075\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28145d651bde6abd9ce37879d0042dca\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7284029f3c4ae475ae053797d890b475\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1822ffcd8d141c6ec27c1b76586dff2f\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29363e912acff5b53509f9ba6e6ff00a\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1b2c7745d237da6250a60c1d844e8d9\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35d1fd530f6cfcbd2837527cc22c5854\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c0a3b3e0bcc786f6d4636c948926c5\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e10a3dfbe4fde0798b958c25009cd6b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e9efd0aa8467ac29c851889e152da8a\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb37c663f6b6355e4c9bc8c35f72ff66\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2f9e8e39c950c10295228f8ba00ca0e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fd7eb731e5534272cd0f015635e45e0\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1208676a06b9d40bef2753af4d8a7685\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\463c36752911edcd502514953090fec6\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f269811d8cc0a512ad9a3c6d9f38dcec\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e9945addaf6f3f62b4611e60857cd2\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66bb93bfe89d21366a698cdecc04f7b1\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1895eea7a1cb96185f451a6fa409a9f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d77b300797316a0cbdac428d63c9888f\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\640bee2b0ea01b07da9ccc437827b1b3\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f8292b642baf578d0d20cb8653d909a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e7a34afc2b6fd3a244eccb13c4cf553\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d44ba69223df912c153c3651ac7988\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\763a4083062442933b972ee410371f1a\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4331be203709c150ef9fa7e985fd6916\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3bc4a2c45acd99c4e4bd215c9303fc\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a797c2aed94d9afc60c1d67666324b36\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23afb6445ff00994be9a1779f78c9c06\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5682efc4b5866147cd3a24be1ec4211\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be2bbaabe3ec7dc3638c52bb9392b69a\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60d7f1079df9f7e8df5dce46eda47bea\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\935d4e8174106a464b55934683b05b92\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70687d79e79127e8d177376e23f87a6c\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6e01715afba3fe3111960e6a182f0bf\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2e92d4b87b684e16d60ca5e4e064096\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fade1697daa4e9efd9bdd30590836f0\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a51aa6a8926fe679ad787d8077d6a90\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d16b967115831f34f161c2b8042162\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14edd447060286e79e634c459c3a00fe\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ada28ed5e61b93f14534594e8f74eaa\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\847af613feb5d2e0d19f5974b567e54c\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:6:22-76
application
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:8:5-31:19
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:8:5-31:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\552c2d16ebacaf2ec7ea18f1b706cb2c\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\552c2d16ebacaf2ec7ea18f1b706cb2c\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb37c663f6b6355e4c9bc8c35f72ff66\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb37c663f6b6355e4c9bc8c35f72ff66\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d44ba69223df912c153c3651ac7988\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d44ba69223df912c153c3651ac7988\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2e92d4b87b684e16d60ca5e4e064096\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2e92d4b87b684e16d60ca5e4e064096\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a51aa6a8926fe679ad787d8077d6a90\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a51aa6a8926fe679ad787d8077d6a90\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:14:9-41
	android:fullBackupContent
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:12:9-54
	android:roundIcon
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:15:9-54
	tools:targetApi
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:13:9-43
	android:allowBackup
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:10:9-35
	android:theme
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:17:9-46
	android:dataExtractionRules
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:11:9-65
	android:usesCleartextTraffic
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:18:9-44
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:9:9-44
activity#com.example.gxzhaiwu.MainActivity
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:20:9-30:20
	android:label
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:23:13-45
	android:exported
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:24:13-50
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:21:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:25:13-29:29
action#android.intent.action.MAIN
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:26:17-69
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:26:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:28:17-77
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:28:27-74
uses-sdk
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd1004847bd6d5ce61d9c60cb4287ad5\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd1004847bd6d5ce61d9c60cb4287ad5\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed654ba23f42fde4b700be0d32f1d2c6\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed654ba23f42fde4b700be0d32f1d2c6\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c19a4f0623c1c480ca6e3f53e35bdcb\transformed\navigation-common-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c19a4f0623c1c480ca6e3f53e35bdcb\transformed\navigation-common-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c99abe88a69f82b4efe622efde03120\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c99abe88a69f82b4efe622efde03120\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\957dbd44cba30cfeb783bc775f75253e\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\957dbd44cba30cfeb783bc775f75253e\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\caee232b08ec2fa4e2b3b98af7baf03a\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\caee232b08ec2fa4e2b3b98af7baf03a\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b98823ec0e24b426741077cc82e78c\transformed\navigation-compose-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b98823ec0e24b426741077cc82e78c\transformed\navigation-compose-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96a4bd88eed9cbd0e46dbdb61835a31\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96a4bd88eed9cbd0e46dbdb61835a31\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5addc3b44c294d89924313c3ea377a3b\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5addc3b44c294d89924313c3ea377a3b\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34672be5908381f92b2f1efab7609872\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34672be5908381f92b2f1efab7609872\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97ebc98232b056e900235e30ef067680\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97ebc98232b056e900235e30ef067680\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc1b6a98fe89f0376a3b88e6881572ba\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc1b6a98fe89f0376a3b88e6881572ba\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8e0697a2e839a8818ab2e625f9e61b1\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8e0697a2e839a8818ab2e625f9e61b1\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78092803766c0a9776e86e2f802ce3a7\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78092803766c0a9776e86e2f802ce3a7\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\886e1afd64882135b27d7a7faaa08e48\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\886e1afd64882135b27d7a7faaa08e48\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98f289bf6c45d262a7223b262b65f304\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98f289bf6c45d262a7223b262b65f304\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f034a1b5cd81e4ff40dadeef5ee60c3\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f034a1b5cd81e4ff40dadeef5ee60c3\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e138f0002d4776600ec3ef8c1d624d8\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e138f0002d4776600ec3ef8c1d624d8\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412943cd7e22f7dc8c187c67b2cef78f\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412943cd7e22f7dc8c187c67b2cef78f\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\552c2d16ebacaf2ec7ea18f1b706cb2c\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\552c2d16ebacaf2ec7ea18f1b706cb2c\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2bd8f8ca0f1f58bbcff76784424ab33\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2bd8f8ca0f1f58bbcff76784424ab33\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a93f44d94e792e6ae8273853680006b3\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a93f44d94e792e6ae8273853680006b3\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17cfcbb607414ceb3b2c0fa072cdc3a\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17cfcbb607414ceb3b2c0fa072cdc3a\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0218312c92cc52cb8b5d2bc6690d075\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0218312c92cc52cb8b5d2bc6690d075\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28145d651bde6abd9ce37879d0042dca\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28145d651bde6abd9ce37879d0042dca\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7284029f3c4ae475ae053797d890b475\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7284029f3c4ae475ae053797d890b475\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1822ffcd8d141c6ec27c1b76586dff2f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1822ffcd8d141c6ec27c1b76586dff2f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29363e912acff5b53509f9ba6e6ff00a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29363e912acff5b53509f9ba6e6ff00a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1b2c7745d237da6250a60c1d844e8d9\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1b2c7745d237da6250a60c1d844e8d9\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35d1fd530f6cfcbd2837527cc22c5854\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35d1fd530f6cfcbd2837527cc22c5854\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c0a3b3e0bcc786f6d4636c948926c5\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25c0a3b3e0bcc786f6d4636c948926c5\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e10a3dfbe4fde0798b958c25009cd6b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e10a3dfbe4fde0798b958c25009cd6b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e9efd0aa8467ac29c851889e152da8a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e9efd0aa8467ac29c851889e152da8a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb37c663f6b6355e4c9bc8c35f72ff66\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb37c663f6b6355e4c9bc8c35f72ff66\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2f9e8e39c950c10295228f8ba00ca0e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2f9e8e39c950c10295228f8ba00ca0e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fd7eb731e5534272cd0f015635e45e0\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fd7eb731e5534272cd0f015635e45e0\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1208676a06b9d40bef2753af4d8a7685\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1208676a06b9d40bef2753af4d8a7685\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\463c36752911edcd502514953090fec6\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\463c36752911edcd502514953090fec6\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f269811d8cc0a512ad9a3c6d9f38dcec\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f269811d8cc0a512ad9a3c6d9f38dcec\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e9945addaf6f3f62b4611e60857cd2\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e9945addaf6f3f62b4611e60857cd2\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66bb93bfe89d21366a698cdecc04f7b1\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66bb93bfe89d21366a698cdecc04f7b1\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1895eea7a1cb96185f451a6fa409a9f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1895eea7a1cb96185f451a6fa409a9f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d77b300797316a0cbdac428d63c9888f\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d77b300797316a0cbdac428d63c9888f\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\640bee2b0ea01b07da9ccc437827b1b3\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\640bee2b0ea01b07da9ccc437827b1b3\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f8292b642baf578d0d20cb8653d909a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f8292b642baf578d0d20cb8653d909a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e7a34afc2b6fd3a244eccb13c4cf553\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e7a34afc2b6fd3a244eccb13c4cf553\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d44ba69223df912c153c3651ac7988\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d44ba69223df912c153c3651ac7988\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\763a4083062442933b972ee410371f1a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\763a4083062442933b972ee410371f1a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4331be203709c150ef9fa7e985fd6916\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4331be203709c150ef9fa7e985fd6916\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3bc4a2c45acd99c4e4bd215c9303fc\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3bc4a2c45acd99c4e4bd215c9303fc\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a797c2aed94d9afc60c1d67666324b36\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a797c2aed94d9afc60c1d67666324b36\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23afb6445ff00994be9a1779f78c9c06\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23afb6445ff00994be9a1779f78c9c06\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5682efc4b5866147cd3a24be1ec4211\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5682efc4b5866147cd3a24be1ec4211\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be2bbaabe3ec7dc3638c52bb9392b69a\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be2bbaabe3ec7dc3638c52bb9392b69a\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60d7f1079df9f7e8df5dce46eda47bea\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60d7f1079df9f7e8df5dce46eda47bea\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\935d4e8174106a464b55934683b05b92\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\935d4e8174106a464b55934683b05b92\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70687d79e79127e8d177376e23f87a6c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70687d79e79127e8d177376e23f87a6c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6e01715afba3fe3111960e6a182f0bf\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6e01715afba3fe3111960e6a182f0bf\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2e92d4b87b684e16d60ca5e4e064096\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2e92d4b87b684e16d60ca5e4e064096\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fade1697daa4e9efd9bdd30590836f0\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fade1697daa4e9efd9bdd30590836f0\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a51aa6a8926fe679ad787d8077d6a90\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a51aa6a8926fe679ad787d8077d6a90\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d16b967115831f34f161c2b8042162\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d16b967115831f34f161c2b8042162\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14edd447060286e79e634c459c3a00fe\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14edd447060286e79e634c459c3a00fe\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ada28ed5e61b93f14534594e8f74eaa\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ada28ed5e61b93f14534594e8f74eaa\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\847af613feb5d2e0d19f5974b567e54c\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\847af613feb5d2e0d19f5974b567e54c\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\552c2d16ebacaf2ec7ea18f1b706cb2c\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\552c2d16ebacaf2ec7ea18f1b706cb2c\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\552c2d16ebacaf2ec7ea18f1b706cb2c\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb37c663f6b6355e4c9bc8c35f72ff66\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb37c663f6b6355e4c9bc8c35f72ff66\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2e92d4b87b684e16d60ca5e4e064096\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2e92d4b87b684e16d60ca5e4e064096\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d72302063c6d8926be77fc80c248256\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.gxzhaiwu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.gxzhaiwu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca95cc56ae6c869d643262611d469661\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb37c663f6b6355e4c9bc8c35f72ff66\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb37c663f6b6355e4c9bc8c35f72ff66\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb37c663f6b6355e4c9bc8c35f72ff66\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d44ba69223df912c153c3651ac7988\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d44ba69223df912c153c3651ac7988\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d44ba69223df912c153c3651ac7988\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cca24f6c7d5dca2bf1b3b7ea20db21a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
