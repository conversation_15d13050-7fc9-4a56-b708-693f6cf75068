package com.example.gxzhaiwu.ui.usermanagement.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a\"\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0003\u001a4\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0018\u0010\n\u001a\u0014\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0003\u001a4\u0010\r\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0018\u0010\n\u001a\u0014\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0007\u001a\u0010\u0010\u000e\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u0003H\u0002\u00a8\u0006\u0010"}, d2 = {"RoleChip", "", "role", "", "color", "modifier", "Landroidx/compose/ui/Modifier;", "UserActionMenu", "user", "Lcom/example/gxzhaiwu/data/model/UserDetail;", "onAction", "Lkotlin/Function2;", "Lcom/example/gxzhaiwu/data/model/UserManagementAction;", "UserCard", "formatDate", "dateString", "app_debug"})
public final class UserCardKt {
    
    /**
     * 用户卡片组件
     * 显示用户基本信息和操作按钮
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void UserCard(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail user, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.example.gxzhaiwu.data.model.UserManagementAction, ? super com.example.gxzhaiwu.data.model.UserDetail, kotlin.Unit> onAction, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 角色标签组件
     */
    @androidx.compose.runtime.Composable()
    private static final void RoleChip(java.lang.String role, java.lang.String color, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 用户操作菜单组件
     */
    @androidx.compose.runtime.Composable()
    private static final void UserActionMenu(com.example.gxzhaiwu.data.model.UserDetail user, kotlin.jvm.functions.Function2<? super com.example.gxzhaiwu.data.model.UserManagementAction, ? super com.example.gxzhaiwu.data.model.UserDetail, kotlin.Unit> onAction, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 格式化日期
     */
    private static final java.lang.String formatDate(java.lang.String dateString) {
        return null;
    }
}