android.app.Application#androidx.activity.ComponentActivity2kotlinx.serialization.internal.GeneratedSerializerkotlin.Enum3com.example.gxzhaiwu.data.model.UserOperationResult3com.example.gxzhaiwu.data.repository.AuthRepository8com.example.gxzhaiwu.data.repository.DashboardRepository=com.example.gxzhaiwu.data.repository.UserManagementRepositoryandroidx.lifecycle.ViewModel6com.example.gxzhaiwu.ui.bills.BillManagementSideEffect0com.example.gxzhaiwu.ui.dashboard.DashboardEvent5com.example.gxzhaiwu.ui.dashboard.DashboardSideEffect0com.example.gxzhaiwu.ui.navigation.BottomNavItem)com.example.gxzhaiwu.ui.navigation.Screen<com.example.gxzhaiwu.ui.payments.PaymentManagementSideEffect                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   