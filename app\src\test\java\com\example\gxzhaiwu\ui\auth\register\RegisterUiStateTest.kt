package com.example.gxzhaiwu.ui.auth.register

import org.junit.Assert.*
import org.junit.Test

class RegisterUiStateTest {

    @Test
    fun `isFormValid should return false when all fields are empty`() {
        // Given
        val uiState = RegisterUiState()

        // Then
        assertFalse(uiState.isFormValid)
    }

    @Test
    fun `isFormValid should return false when any field is empty`() {
        // Given
        val uiState = RegisterUiState(
            name = "张三",
            username = "zhang<PERSON>",
            email = "<EMAIL>",
            password = "password123",
            passwordConfirmation = "" // Empty confirmation
        )

        // Then
        assertFalse(uiState.isFormValid)
    }

    @Test
    fun `isFormValid should return false when any field has error`() {
        // Given
        val uiState = RegisterUiState(
            name = "张三",
            username = "zhang<PERSON>",
            email = "<EMAIL>",
            password = "password123",
            passwordConfirmation = "password123",
            emailError = "邮箱格式不正确" // Has error
        )

        // Then
        assertFalse(uiState.isFormValid)
    }

    @Test
    fun `isFormValid should return true when all fields are valid`() {
        // Given
        val uiState = RegisterUiState(
            name = "张三",
            username = "zhangsan",
            email = "<EMAIL>",
            password = "password123",
            passwordConfirmation = "password123",
            nameError = null,
            usernameError = null,
            emailError = null,
            passwordError = null,
            passwordConfirmationError = null
        )

        // Then
        assertTrue(uiState.isFormValid)
    }

    @Test
    fun `default state should have correct initial values`() {
        // Given
        val uiState = RegisterUiState()

        // Then
        assertEquals("", uiState.name)
        assertEquals("", uiState.username)
        assertEquals("", uiState.email)
        assertEquals("", uiState.password)
        assertEquals("", uiState.passwordConfirmation)
        assertFalse(uiState.isLoading)
        assertFalse(uiState.isRegisterSuccessful)
        assertNull(uiState.errorMessage)
        assertNull(uiState.nameError)
        assertNull(uiState.usernameError)
        assertNull(uiState.emailError)
        assertNull(uiState.passwordError)
        assertNull(uiState.passwordConfirmationError)
        assertFalse(uiState.isFormValid)
    }

    @Test
    fun `copy should work correctly`() {
        // Given
        val originalState = RegisterUiState()

        // When
        val newState = originalState.copy(
            name = "张三",
            isLoading = true,
            nameError = "姓名错误"
        )

        // Then
        assertEquals("张三", newState.name)
        assertTrue(newState.isLoading)
        assertEquals("姓名错误", newState.nameError)
        // Other fields should remain unchanged
        assertEquals("", newState.username)
        assertEquals("", newState.email)
        assertFalse(newState.isRegisterSuccessful)
    }
}
