package com.example.gxzhaiwu.ui.dashboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.data.repository.AuthRepository
import com.example.gxzhaiwu.data.repository.DashboardRepository
import com.example.gxzhaiwu.utils.RoleUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 仪表盘ViewModel
 * 管理仪表盘页面的业务逻辑和状态
 */
@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val dashboardRepository: DashboardRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()

    private val _sideEffect = MutableSharedFlow<DashboardSideEffect>()
    val sideEffect: SharedFlow<DashboardSideEffect> = _sideEffect.asSharedFlow()

    init {
        // 监听用户状态变化
        viewModelScope.launch {
            authRepository.getCurrentUserFlow().collect { user ->
                if (user != null) {
                    updateUserPermissions(user.roles)
                    loadDashboardData()
                } else {
                    _sideEffect.emit(DashboardSideEffect.NavigateToLogin)
                }
            }
        }
    }

    /**
     * 处理仪表盘事件
     */
    fun onEvent(event: DashboardEvent) {
        when (event) {
            is DashboardEvent.LoadData -> loadDashboardData()
            is DashboardEvent.RefreshData -> refreshDashboardData()
            is DashboardEvent.ClearError -> clearError()
            is DashboardEvent.SelectDateRange -> selectDateRange(event.dateRange)
            is DashboardEvent.ToggleAdvancedStatistics -> toggleAdvancedStatistics(event.show)
            is DashboardEvent.QuickActionClicked -> handleQuickAction(event.action)
            is DashboardEvent.StatisticsCardClicked -> handleStatisticsCardClick(event.card)
        }
    }

    /**
     * 加载仪表盘数据
     */
    private fun loadDashboardData() {
        if (_uiState.value.isLoading) return

        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, hasError = false, errorMessage = null) }

            try {
                // 并行加载概览和统计数据
                val overviewResult = dashboardRepository.getOverview()
                val statisticsResult = dashboardRepository.getStatistics(
                    startDate = _uiState.value.selectedDateRange?.startDate,
                    endDate = _uiState.value.selectedDateRange?.endDate
                )

                overviewResult.fold(
                    onSuccess = { overview ->
                        _uiState.update { currentState ->
                            currentState.copy(
                                overview = overview,
                                statisticsCards = createStatisticsCards(overview),
                                lastUpdated = System.currentTimeMillis()
                            )
                        }
                    },
                    onFailure = { error ->
                        handleError("加载概览数据失败: ${error.message}")
                    }
                )

                statisticsResult.fold(
                    onSuccess = { statistics ->
                        _uiState.update { it.copy(statistics = statistics) }
                    },
                    onFailure = { error ->
                        // 统计数据加载失败不影响主要功能
                        handleError("加载统计数据失败: ${error.message}", isMinor = true)
                    }
                )

            } catch (e: Exception) {
                handleError("加载数据时发生错误: ${e.message}")
            } finally {
                _uiState.update { it.copy(isLoading = false) }
            }
        }
    }

    /**
     * 刷新仪表盘数据
     */
    private fun refreshDashboardData() {
        if (_uiState.value.isRefreshing) return

        viewModelScope.launch {
            _uiState.update { it.copy(isRefreshing = true) }
            
            try {
                val overviewResult = dashboardRepository.getOverview()
                overviewResult.fold(
                    onSuccess = { overview ->
                        _uiState.update { currentState ->
                            currentState.copy(
                                overview = overview,
                                statisticsCards = createStatisticsCards(overview),
                                lastUpdated = System.currentTimeMillis(),
                                hasError = false,
                                errorMessage = null
                            )
                        }
                        _sideEffect.emit(DashboardSideEffect.ShowSnackbar("数据已更新"))
                    },
                    onFailure = { error ->
                        _sideEffect.emit(DashboardSideEffect.ShowError("刷新失败: ${error.message}"))
                    }
                )
            } finally {
                _uiState.update { it.copy(isRefreshing = false) }
            }
        }
    }

    /**
     * 更新用户权限
     */
    private fun updateUserPermissions(roles: List<String>) {
        val permissions = RoleUtils.getDashboardPermissions(roles)
        val quickActions = RoleUtils.getAvailableQuickActions(roles)
        
        _uiState.update { 
            it.copy(
                userRoles = roles,
                permissions = permissions,
                quickActions = quickActions
            )
        }
    }

    /**
     * 创建统计卡片数据
     */
    private fun createStatisticsCards(overview: DashboardOverview): List<StatisticsCardData> {
        return listOf(
            StatisticsCardData(
                title = "总客户数",
                value = overview.summary.total_customers.toString(),
                subtitle = "活跃客户",
                icon = "people"
            ),
            StatisticsCardData(
                title = "总账单数",
                value = overview.summary.total_invoices.toString(),
                subtitle = "累计账单",
                icon = "receipt"
            ),
            StatisticsCardData(
                title = "还款记录",
                value = overview.summary.total_payments.toString(),
                subtitle = "还款次数",
                icon = "payment"
            ),
            StatisticsCardData(
                title = "门店数量",
                value = overview.summary.total_stores.toString(),
                subtitle = "营业门店",
                icon = "store"
            )
        )
    }

    /**
     * 处理错误
     */
    private fun handleError(message: String, isMinor: Boolean = false) {
        _uiState.update { 
            it.copy(
                hasError = !isMinor,
                errorMessage = message
            )
        }
        
        viewModelScope.launch {
            _sideEffect.emit(DashboardSideEffect.ShowError(message))
        }
    }

    /**
     * 清除错误状态
     */
    private fun clearError() {
        _uiState.update { 
            it.copy(
                hasError = false,
                errorMessage = null
            )
        }
    }

    /**
     * 选择日期范围
     */
    private fun selectDateRange(dateRange: DateRange?) {
        _uiState.update { it.copy(selectedDateRange = dateRange) }
        loadDashboardData() // 重新加载数据
    }

    /**
     * 切换高级统计显示
     */
    private fun toggleAdvancedStatistics(show: Boolean) {
        _uiState.update { it.copy(showAdvancedStatistics = show) }
    }

    /**
     * 处理快速操作点击
     */
    private fun handleQuickAction(action: QuickActionData) {
        viewModelScope.launch {
            val route = when (action.action) {
                QuickActionType.CREATE_INVOICE -> "create_invoice"
                QuickActionType.RECORD_PAYMENT -> "record_payment"
                QuickActionType.ADD_CUSTOMER -> "add_customer"
                QuickActionType.VIEW_REPORTS -> "reports"
                QuickActionType.MANAGE_USERS -> "user_management"
                QuickActionType.MANAGEMENT_CENTER -> "management_center"
            }
            _sideEffect.emit(DashboardSideEffect.NavigateToScreen(route))
        }
    }

    /**
     * 处理统计卡片点击
     */
    private fun handleStatisticsCardClick(card: StatisticsCardData) {
        viewModelScope.launch {
            val route = when (card.icon) {
                "people" -> "customers"
                "receipt" -> "invoices"
                "payment" -> "payments"
                "store" -> "stores"
                else -> return@launch
            }
            _sideEffect.emit(DashboardSideEffect.NavigateToScreen(route))
        }
    }
}
