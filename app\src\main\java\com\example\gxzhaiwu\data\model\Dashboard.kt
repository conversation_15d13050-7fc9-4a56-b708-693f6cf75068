package com.example.gxzhaiwu.data.model

import kotlinx.serialization.Serializable

/**
 * 仪表盘概览数据模型
 */
@Serializable
data class DashboardOverview(
    val summary: SystemSummary,
    val financial: FinancialSummary,
    val invoice_status_distribution: InvoiceStatusDistribution
)

/**
 * 系统基础统计数据
 */
@Serializable
data class SystemSummary(
    val total_customers: Int,
    val total_invoices: Int,
    val total_payments: Int,
    val total_stores: Int
)

/**
 * 财务汇总数据
 */
@Serializable
data class FinancialSummary(
    val total_invoice_amount: String,
    val total_paid_amount: String,
    val total_outstanding_amount: String,
    val total_payment_amount: String
)

/**
 * 账单状态分布
 */
@Serializable
data class InvoiceStatusDistribution(
    val unpaid: Int,
    val partially_paid: Int,
    val paid: Int,
    val overdue: Int
)

/**
 * 详细统计数据模型
 */
@Serializable
data class DashboardStatistics(
    val period: StatisticsPeriod,
    val customers: CustomerStatistics,
    val invoices: InvoiceStatistics,
    val payments: PaymentStatistics,
    val stores: List<StoreStatistics>
)

/**
 * 统计时间范围
 */
@Serializable
data class StatisticsPeriod(
    val start_date: String? = null,
    val end_date: String? = null
) {
    /**
     * 获取显示用的开始日期，如果为null则返回默认文本
     */
    fun getDisplayStartDate(): String = start_date ?: "未指定"

    /**
     * 获取显示用的结束日期，如果为null则返回默认文本
     */
    fun getDisplayEndDate(): String = end_date ?: "未指定"

    /**
     * 获取日期范围的显示文本
     */
    fun getDisplayRange(): String {
        return when {
            start_date != null && end_date != null -> "$start_date 至 $end_date"
            start_date != null -> "从 $start_date 开始"
            end_date != null -> "截至 $end_date"
            else -> "全部时间"
        }
    }
}

/**
 * 客户统计数据
 */
@Serializable
data class CustomerStatistics(
    val total_customers: Int,
    val customers_with_debt: Int
)

/**
 * 账单统计数据
 */
@Serializable
data class InvoiceStatistics(
    val count: Int,
    val total_amount: String,
    val paid_amount: String,
    val average_amount: String
)

/**
 * 还款统计数据
 */
@Serializable
data class PaymentStatistics(
    val count: Int,
    val total_amount: String,
    val average_amount: String
)

/**
 * 门店统计数据
 */
@Serializable
data class StoreStatistics(
    val store_id: Int,
    val store_name: String,
    val invoice_count: Int,
    val total_amount: String,
    val paid_amount: String
)

/**
 * 仪表盘API响应包装类
 */
@Serializable
data class DashboardOverviewResponse(
    val success: Boolean,
    val data: DashboardOverview,
    val message: String? = null
)

@Serializable
data class DashboardStatisticsResponse(
    val success: Boolean,
    val data: DashboardStatistics,
    val message: String? = null
)

/**
 * 用户角色枚举
 */
enum class UserRole(val value: String, val displayName: String) {
    ADMIN("admin", "系统管理员"),
    STORE_OWNER("store_owner", "店长"),
    STORE_STAFF("store_staff", "店员");

    companion object {
        fun fromString(value: String): UserRole? {
            return values().find { it.value == value }
        }
    }
}

/**
 * 仪表盘权限配置
 */
data class DashboardPermissions(
    val canViewAllStores: Boolean,
    val canViewFinancialDetails: Boolean,
    val canViewStoreComparison: Boolean,
    val canAccessAdvancedStatistics: Boolean
) {
    companion object {
        fun fromUserRoles(roles: List<String>): DashboardPermissions {
            val hasAdminRole = roles.contains(UserRole.ADMIN.value)
            val hasStoreOwnerRole = roles.contains(UserRole.STORE_OWNER.value)
            
            return DashboardPermissions(
                canViewAllStores = hasAdminRole,
                canViewFinancialDetails = hasAdminRole || hasStoreOwnerRole,
                canViewStoreComparison = hasAdminRole,
                canAccessAdvancedStatistics = hasAdminRole || hasStoreOwnerRole
            )
        }
    }
}

/**
 * 统计卡片数据模型
 */
data class StatisticsCardData(
    val title: String,
    val value: String,
    val subtitle: String? = null,
    val trend: TrendType = TrendType.NEUTRAL,
    val trendValue: String? = null,
    val icon: String? = null
)

/**
 * 趋势类型
 */
enum class TrendType {
    POSITIVE,    // 正向趋势（绿色）
    NEGATIVE,    // 负向趋势（红色）
    NEUTRAL      // 中性（默认颜色）
}

/**
 * 快速操作数据模型
 */
data class QuickActionData(
    val title: String,
    val description: String,
    val icon: String,
    val action: QuickActionType,
    val enabled: Boolean = true
)

/**
 * 快速操作类型
 */
enum class QuickActionType {
    CREATE_INVOICE,     // 创建账单
    RECORD_PAYMENT,     // 记录还款
    ADD_CUSTOMER        // 添加客户
}

/**
 * 管理模块数据模型
 */
data class ManagementModule(
    val id: String,
    val title: String,
    val description: String,
    val icon: String,
    val requiredPermission: com.example.gxzhaiwu.utils.Permission,
    val route: String,
    val enabled: Boolean = true
)

/**
 * 管理模块类型枚举
 */
enum class ManagementModuleType {
    STORE_MANAGEMENT,       // 门店管理
    SYSTEM_MANAGEMENT,      // 系统管理
    USER_MANAGEMENT,        // 用户管理
    CUSTOMER_MANAGEMENT     // 客户管理
}

/**
 * 管理中心UI状态
 */
data class ManagementCenterUiState(
    val isLoading: Boolean = false,
    val modules: List<ManagementModule> = emptyList(),
    val userRoles: List<String> = emptyList(),
    val hasError: Boolean = false,
    val errorMessage: String? = null
)
