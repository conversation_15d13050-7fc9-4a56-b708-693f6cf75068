package com.example.gxzhaiwu.data.api

import com.example.gxzhaiwu.data.model.DashboardOverviewResponse
import com.example.gxzhaiwu.data.model.DashboardStatisticsResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Query

/**
 * 仪表盘API接口
 * 提供仪表盘相关的数据获取功能
 */
interface DashboardApi {

    /**
     * 获取仪表盘概览数据
     * 包括系统基础统计信息和财务数据汇总
     * 
     * @param authorization 认证令牌，格式：Bearer {token}
     * @return 仪表盘概览数据响应
     */
    @GET("dashboard/overview")
    suspend fun getOverview(
        @Header("Authorization") authorization: String
    ): Response<DashboardOverviewResponse>

    /**
     * 获取详细统计数据
     * 支持时间范围筛选，管理员可查看门店对比数据
     * 
     * @param authorization 认证令牌，格式：Bearer {token}
     * @param startDate 开始日期，格式：YYYY-MM-DD（可选）
     * @param endDate 结束日期，格式：YYYY-MM-DD（可选）
     * @return 详细统计数据响应
     */
    @GET("dashboard/statistics")
    suspend fun getStatistics(
        @Header("Authorization") authorization: String,
        @Query("start_date") startDate: String? = null,
        @Query("end_date") endDate: String? = null
    ): Response<DashboardStatisticsResponse>
}
