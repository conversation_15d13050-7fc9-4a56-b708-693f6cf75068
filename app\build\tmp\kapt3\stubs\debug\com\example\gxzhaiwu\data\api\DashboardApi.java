package com.example.gxzhaiwu.data.api;

/**
 * 仪表盘API接口
 * 提供仪表盘相关的数据获取功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\bf\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u0007J6\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\n\b\u0003\u0010\n\u001a\u0004\u0018\u00010\u00062\n\b\u0003\u0010\u000b\u001a\u0004\u0018\u00010\u0006H\u00a7@\u00a2\u0006\u0002\u0010\f\u00a8\u0006\r"}, d2 = {"Lcom/example/gxzhaiwu/data/api/DashboardApi;", "", "getOverview", "Lretrofit2/Response;", "Lcom/example/gxzhaiwu/data/model/DashboardOverviewResponse;", "authorization", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getStatistics", "Lcom/example/gxzhaiwu/data/model/DashboardStatisticsResponse;", "startDate", "endDate", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface DashboardApi {
    
    /**
     * 获取仪表盘概览数据
     * 包括系统基础统计信息和财务数据汇总
     *
     * @param authorization 认证令牌，格式：Bearer {token}
     * @return 仪表盘概览数据响应
     */
    @retrofit2.http.GET(value = "dashboard/overview")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOverview(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.gxzhaiwu.data.model.DashboardOverviewResponse>> $completion);
    
    /**
     * 获取详细统计数据
     * 支持时间范围筛选，管理员可查看门店对比数据
     *
     * @param authorization 认证令牌，格式：Bearer {token}
     * @param startDate 开始日期，格式：YYYY-MM-DD（可选）
     * @param endDate 结束日期，格式：YYYY-MM-DD（可选）
     * @return 详细统计数据响应
     */
    @retrofit2.http.GET(value = "dashboard/statistics")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getStatistics(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Query(value = "start_date")
    @org.jetbrains.annotations.Nullable()
    java.lang.String startDate, @retrofit2.http.Query(value = "end_date")
    @org.jetbrains.annotations.Nullable()
    java.lang.String endDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.gxzhaiwu.data.model.DashboardStatisticsResponse>> $completion);
    
    /**
     * 仪表盘API接口
     * 提供仪表盘相关的数据获取功能
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}