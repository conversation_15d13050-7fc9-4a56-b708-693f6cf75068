package com.example.gxzhaiwu.ui.dashboard.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gxzhaiwu.data.model.StatisticsCardData
import com.example.gxzhaiwu.data.model.TrendType
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import com.example.gxzhaiwu.ui.theme.cardElevationColor
import com.example.gxzhaiwu.ui.theme.success
import com.example.gxzhaiwu.ui.theme.warning

/**
 * 统计卡片组件
 * 用于显示各种统计数据，支持趋势指示和图标
 */
@Composable
fun StatisticsCard(
    data: StatisticsCardData,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null
) {
    val cardModifier = if (onClick != null) {
        modifier.then(Modifier.fillMaxWidth())
    } else {
        modifier.fillMaxWidth()
    }

    Card(
        modifier = cardModifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.cardElevationColor(2)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        ),
        onClick = onClick ?: {}
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 标题和图标行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = data.title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.weight(1f)
                )
                
                data.icon?.let { iconName ->
                    getIconByName(iconName)?.let { icon ->
                        Icon(
                            imageVector = icon,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }

            // 主要数值
            Text(
                text = data.value,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )

            // 副标题和趋势
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                data.subtitle?.let { subtitle ->
                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.weight(1f)
                    )
                }

                data.trendValue?.let { trendValue ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        val (trendIcon, trendColor) = when (data.trend) {
                            TrendType.POSITIVE -> Icons.Default.TrendingUp to MaterialTheme.colorScheme.success
                            TrendType.NEGATIVE -> Icons.Default.TrendingDown to MaterialTheme.colorScheme.error
                            TrendType.NEUTRAL -> Icons.Default.TrendingFlat to MaterialTheme.colorScheme.onSurfaceVariant
                        }

                        Icon(
                            imageVector = trendIcon,
                            contentDescription = null,
                            tint = trendColor,
                            modifier = Modifier.size(16.dp)
                        )

                        Text(
                            text = trendValue,
                            style = MaterialTheme.typography.bodySmall,
                            color = trendColor,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

/**
 * 根据图标名称获取对应的ImageVector
 */
private fun getIconByName(iconName: String): ImageVector? {
    return when (iconName) {
        "people" -> Icons.Default.People
        "receipt" -> Icons.Default.Receipt
        "payment" -> Icons.Default.Payment
        "store" -> Icons.Default.Store
        "account_balance" -> Icons.Default.AccountBalance
        "trending_up" -> Icons.Default.TrendingUp
        "trending_down" -> Icons.Default.TrendingDown
        "analytics" -> Icons.Default.Analytics
        "pie_chart" -> Icons.Default.PieChart
        "bar_chart" -> Icons.Default.BarChart
        else -> null
    }
}

@Preview(name = "浅色模式 - 基础统计卡片")
@Composable
private fun StatisticsCardPreview() {
    GxZhaiWuTheme(darkTheme = false) {
        Surface {
            StatisticsCard(
                data = StatisticsCardData(
                    title = "总客户数",
                    value = "150",
                    subtitle = "本月新增 12 位",
                    trend = TrendType.POSITIVE,
                    trendValue = "+8.7%",
                    icon = "people"
                ),
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Preview(name = "深色模式 - 财务统计卡片")
@Composable
private fun StatisticsCardDarkPreview() {
    GxZhaiWuTheme(darkTheme = true) {
        Surface {
            StatisticsCard(
                data = StatisticsCardData(
                    title = "总收入",
                    value = "¥125,000",
                    subtitle = "较上月",
                    trend = TrendType.NEGATIVE,
                    trendValue = "-2.3%",
                    icon = "account_balance"
                ),
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Preview(name = "无趋势数据卡片")
@Composable
private fun StatisticsCardNoTrendPreview() {
    GxZhaiWuTheme {
        Surface {
            StatisticsCard(
                data = StatisticsCardData(
                    title = "门店数量",
                    value = "5",
                    subtitle = "覆盖3个城市",
                    icon = "store"
                ),
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}
