package com.example.gxzhaiwu.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\b"}, d2 = {"Lcom/example/gxzhaiwu/utils/UiUtils;", "", "()V", "hideKeyboard", "", "context", "Landroid/content/Context;", "showKeyboard", "app_debug"})
public final class UiUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.gxzhaiwu.utils.UiUtils INSTANCE = null;
    
    private UiUtils() {
        super();
    }
    
    /**
     * 隐藏软键盘
     */
    public final void hideKeyboard(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 显示软键盘
     * 注意：现代 Android 开发中，通常不需要手动显示键盘，
     * 系统会在用户点击输入框时自动显示。
     * 此方法保留用于特殊情况，但建议依赖系统自动管理。
     */
    public final void showKeyboard(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
}