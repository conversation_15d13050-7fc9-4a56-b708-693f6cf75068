package com.example.gxzhaiwu.utils

import android.util.Patterns

object ValidationUtils {
    
    fun validateLogin(login: String): String? {
        return when {
            login.isBlank() -> "请输入邮箱或用户名"
            login.length > Constants.MAX_EMAIL_LENGTH -> "输入内容过长"
            else -> null
        }
    }
    
    fun validatePassword(password: String): String? {
        return when {
            password.isBlank() -> "请输入密码"
            password.length < Constants.MIN_PASSWORD_LENGTH -> "密码长度不能少于${Constants.MIN_PASSWORD_LENGTH}位"
            else -> null
        }
    }
    
    fun validateEmail(email: String): String? {
        return when {
            email.isBlank() -> "请输入邮箱"
            !Patterns.EMAIL_ADDRESS.matcher(email).matches() -> "邮箱格式不正确"
            email.length > Constants.MAX_EMAIL_LENGTH -> "邮箱长度过长"
            else -> null
        }
    }
    
    fun validateUsername(username: String): String? {
        return when {
            username.isBlank() -> "请输入用户名"
            username.length > Constants.MAX_USERNAME_LENGTH -> "用户名长度过长"
            else -> null
        }
    }
    
    fun validateName(name: String): String? {
        return when {
            name.isBlank() -> "请输入姓名"
            name.length > Constants.MAX_USERNAME_LENGTH -> "姓名长度过长"
            else -> null
        }
    }
    
    fun validatePasswordConfirmation(password: String, confirmation: String): String? {
        return when {
            confirmation.isBlank() -> "请确认密码"
            password != confirmation -> "两次输入的密码不一致"
            else -> null
        }
    }
}
