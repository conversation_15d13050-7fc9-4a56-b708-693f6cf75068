package com.example.gxzhaiwu.ui.dashboard;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000V\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\u001a\"\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0003\u001a.\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\f2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0003\u001a0\u0010\u000e\u001a\u00020\u00012\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\f2\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0007\u001a\b\u0010\u0013\u001a\u00020\u0001H\u0003\u001a=\u0010\u0014\u001a\u00020\u00012\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00100\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u00182\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00010\u001a2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0003\u00a2\u0006\u0002\u0010\u001b\u001a\u001a\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u001e2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0003\u00a8\u0006\u001f"}, d2 = {"AdvancedStatisticsCard", "", "statistics", "Lcom/example/gxzhaiwu/data/model/DashboardStatistics;", "permissions", "Lcom/example/gxzhaiwu/data/model/DashboardPermissions;", "modifier", "Landroidx/compose/ui/Modifier;", "DashboardContent", "uiState", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardUiState;", "onEvent", "Lkotlin/Function1;", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardEvent;", "DashboardScreen", "onNavigateToScreen", "", "viewModel", "Lcom/example/gxzhaiwu/ui/dashboard/DashboardViewModel;", "DashboardScreenPreview", "DashboardTopBar", "userRoles", "", "lastUpdated", "", "onRefresh", "Lkotlin/Function0;", "(Ljava/util/List;Ljava/lang/Long;Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/Modifier;)V", "StoreStatisticsItem", "store", "Lcom/example/gxzhaiwu/data/model/StoreStatistics;", "app_debug"})
public final class DashboardScreenKt {
    
    /**
     * 仪表盘主屏幕
     * 显示系统概览、统计数据和快速操作
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DashboardScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNavigateToScreen, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.ui.dashboard.DashboardViewModel viewModel) {
    }
    
    /**
     * 仪表盘顶部栏
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void DashboardTopBar(java.util.List<java.lang.String> userRoles, java.lang.Long lastUpdated, kotlin.jvm.functions.Function0<kotlin.Unit> onRefresh, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 仪表盘内容区域
     */
    @androidx.compose.runtime.Composable()
    private static final void DashboardContent(com.example.gxzhaiwu.ui.dashboard.DashboardUiState uiState, kotlin.jvm.functions.Function1<? super com.example.gxzhaiwu.ui.dashboard.DashboardEvent, kotlin.Unit> onEvent, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 高级统计卡片
     */
    @androidx.compose.runtime.Composable()
    private static final void AdvancedStatisticsCard(com.example.gxzhaiwu.data.model.DashboardStatistics statistics, com.example.gxzhaiwu.data.model.DashboardPermissions permissions, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 门店统计项
     */
    @androidx.compose.runtime.Composable()
    private static final void StoreStatisticsItem(com.example.gxzhaiwu.data.model.StoreStatistics store, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(name = "\u4eea\u8868\u76d8\u5c4f\u5e55\u9884\u89c8")
    @androidx.compose.runtime.Composable()
    private static final void DashboardScreenPreview() {
    }
}