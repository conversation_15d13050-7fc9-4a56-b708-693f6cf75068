package com.example.gxzhaiwu.ui.usermanagement.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a(\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a*\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0003\u001a6\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\n2\u0006\u0010\f\u001a\u00020\r2\u001c\u0010\u0010\u001a\u0018\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u0011\u00a2\u0006\u0002\b\u0013\u00a2\u0006\u0002\b\u0014H\u0003\u001a\u0010\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0016\u001a\u00020\nH\u0002\u00a8\u0006\u0017"}, d2 = {"UserDetailDialog", "", "user", "Lcom/example/gxzhaiwu/data/model/UserDetail;", "onDismiss", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "UserInfoItem", "label", "", "value", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "UserInfoSection", "title", "content", "Lkotlin/Function1;", "Landroidx/compose/foundation/layout/ColumnScope;", "Landroidx/compose/runtime/Composable;", "Lkotlin/ExtensionFunctionType;", "formatDateTime", "dateString", "app_debug"})
public final class UserDetailDialogKt {
    
    /**
     * 用户详情对话框组件
     * 显示用户的完整信息
     */
    @androidx.compose.runtime.Composable()
    public static final void UserDetailDialog(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail user, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 用户信息部分组件
     */
    @androidx.compose.runtime.Composable()
    private static final void UserInfoSection(java.lang.String title, androidx.compose.ui.graphics.vector.ImageVector icon, kotlin.jvm.functions.Function1<? super androidx.compose.foundation.layout.ColumnScope, kotlin.Unit> content) {
    }
    
    /**
     * 用户信息项组件
     */
    @androidx.compose.runtime.Composable()
    private static final void UserInfoItem(java.lang.String label, java.lang.String value, androidx.compose.ui.graphics.vector.ImageVector icon, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 格式化日期时间
     */
    private static final java.lang.String formatDateTime(java.lang.String dateString) {
        return null;
    }
}