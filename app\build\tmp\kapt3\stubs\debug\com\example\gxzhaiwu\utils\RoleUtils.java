package com.example.gxzhaiwu.utils;

/**
 * 角色权限工具类
 * 提供角色相关的权限判断和功能配置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0003\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u0006\u0010\n\u001a\u00020\tJ(\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006J\u001a\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u00062\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0014\u0010\u000f\u001a\u00020\u00102\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0014\u0010\u0011\u001a\u00020\u00122\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0014\u0010\u0013\u001a\u00020\u00072\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0014\u0010\u0014\u001a\u00020\u00072\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u001c\u0010\u0015\u001a\u00020\u00042\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\u0016\u001a\u00020\u0017J\u0014\u0010\u0018\u001a\u00020\u00042\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0014\u0010\u0019\u001a\u00020\u00042\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0014\u0010\u001a\u001a\u00020\u00042\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a8\u0006\u001b"}, d2 = {"Lcom/example/gxzhaiwu/utils/RoleUtils;", "", "()V", "canAccessStore", "", "userRoles", "", "", "userStoreIds", "", "targetStoreId", "getAccessibleStoreIds", "getAvailableQuickActions", "Lcom/example/gxzhaiwu/data/model/QuickActionData;", "roles", "getDashboardPermissions", "Lcom/example/gxzhaiwu/data/model/DashboardPermissions;", "getPrimaryRole", "Lcom/example/gxzhaiwu/data/model/UserRole;", "getRoleColor", "getRoleDisplayName", "hasPermission", "permission", "Lcom/example/gxzhaiwu/utils/Permission;", "isAdmin", "isStoreOwner", "isStoreStaff", "app_debug"})
public final class RoleUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.gxzhaiwu.utils.RoleUtils INSTANCE = null;
    
    private RoleUtils() {
        super();
    }
    
    /**
     * 检查用户是否为管理员
     */
    public final boolean isAdmin(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> roles) {
        return false;
    }
    
    /**
     * 检查用户是否为店长
     */
    public final boolean isStoreOwner(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> roles) {
        return false;
    }
    
    /**
     * 检查用户是否为店员
     */
    public final boolean isStoreStaff(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> roles) {
        return false;
    }
    
    /**
     * 获取用户的主要角色（优先级：管理员 > 店长 > 店员）
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.gxzhaiwu.data.model.UserRole getPrimaryRole(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> roles) {
        return null;
    }
    
    /**
     * 获取角色显示名称
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRoleDisplayName(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> roles) {
        return null;
    }
    
    /**
     * 根据用户角色获取仪表盘权限配置
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.gxzhaiwu.data.model.DashboardPermissions getDashboardPermissions(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> roles) {
        return null;
    }
    
    /**
     * 根据用户角色获取可用的快速操作列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.gxzhaiwu.data.model.QuickActionData> getAvailableQuickActions(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> roles) {
        return null;
    }
    
    /**
     * 检查用户是否有权限访问特定功能
     */
    public final boolean hasPermission(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> roles, @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.utils.Permission permission) {
        return false;
    }
    
    /**
     * 获取角色对应的颜色（用于UI显示）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRoleColor(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> roles) {
        return null;
    }
    
    /**
     * 检查用户是否可以查看特定门店的数据
     */
    public final boolean canAccessStore(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> userRoles, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> userStoreIds, int targetStoreId) {
        return false;
    }
    
    /**
     * 获取用户可以访问的门店ID列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Integer> getAccessibleStoreIds(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> userRoles, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> userStoreIds) {
        return null;
    }
}