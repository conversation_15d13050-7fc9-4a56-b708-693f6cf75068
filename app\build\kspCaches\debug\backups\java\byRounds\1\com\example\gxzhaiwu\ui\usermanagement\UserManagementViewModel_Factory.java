package com.example.gxzhaiwu.ui.usermanagement;

import com.example.gxzhaiwu.data.repository.UserManagementRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class UserManagementViewModel_Factory implements Factory<UserManagementViewModel> {
  private final Provider<UserManagementRepository> repositoryProvider;

  public UserManagementViewModel_Factory(Provider<UserManagementRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public UserManagementViewModel get() {
    return newInstance(repositoryProvider.get());
  }

  public static UserManagementViewModel_Factory create(
      Provider<UserManagementRepository> repositoryProvider) {
    return new UserManagementViewModel_Factory(repositoryProvider);
  }

  public static UserManagementViewModel newInstance(UserManagementRepository repository) {
    return new UserManagementViewModel(repository);
  }
}
