package com.example.gxzhaiwu.utils

import com.example.gxzhaiwu.data.model.QuickActionType
import com.example.gxzhaiwu.data.model.UserRole
import org.junit.Test
import org.junit.Assert.*

/**
 * RoleUtils单元测试
 */
class RoleUtilsTest {

    @Test
    fun `isAdmin应该正确识别管理员角色`() {
        // Given
        val adminRoles = listOf("admin", "store_owner")
        val nonAdminRoles = listOf("store_owner", "store_staff")

        // When & Then
        assertTrue(RoleUtils.isAdmin(adminRoles))
        assertFalse(RoleUtils.isAdmin(nonAdminRoles))
    }

    @Test
    fun `isStoreOwner应该正确识别店长角色`() {
        // Given
        val ownerRoles = listOf("store_owner", "store_staff")
        val nonOwnerRoles = listOf("store_staff")

        // When & Then
        assertTrue(RoleUtils.isStoreOwner(ownerRoles))
        assertFalse(RoleUtils.isStoreOwner(nonOwnerRoles))
    }

    @Test
    fun `isStoreStaff应该正确识别店员角色`() {
        // Given
        val staffRoles = listOf("store_staff")
        val nonStaffRoles = listOf("admin")

        // When & Then
        assertTrue(RoleUtils.isStoreStaff(staffRoles))
        assertFalse(RoleUtils.isStoreStaff(nonStaffRoles))
    }

    @Test
    fun `getPrimaryRole应该按优先级返回主要角色`() {
        // Given & When & Then
        assertEquals(UserRole.ADMIN, RoleUtils.getPrimaryRole(listOf("admin", "store_owner", "store_staff")))
        assertEquals(UserRole.STORE_OWNER, RoleUtils.getPrimaryRole(listOf("store_owner", "store_staff")))
        assertEquals(UserRole.STORE_STAFF, RoleUtils.getPrimaryRole(listOf("store_staff")))
        assertEquals(UserRole.STORE_STAFF, RoleUtils.getPrimaryRole(emptyList())) // 默认为店员
    }

    @Test
    fun `getRoleDisplayName应该返回正确的显示名称`() {
        // Given & When & Then
        assertEquals("系统管理员", RoleUtils.getRoleDisplayName(listOf("admin")))
        assertEquals("店长", RoleUtils.getRoleDisplayName(listOf("store_owner")))
        assertEquals("店员", RoleUtils.getRoleDisplayName(listOf("store_staff")))
    }

    @Test
    fun `getDashboardPermissions应该为管理员返回完整权限`() {
        // Given
        val adminRoles = listOf("admin")

        // When
        val permissions = RoleUtils.getDashboardPermissions(adminRoles)

        // Then
        assertTrue(permissions.canViewAllStores)
        assertTrue(permissions.canViewFinancialDetails)
        assertTrue(permissions.canViewStoreComparison)
        assertTrue(permissions.canAccessAdvancedStatistics)
    }

    @Test
    fun `getDashboardPermissions应该为店长返回部分权限`() {
        // Given
        val ownerRoles = listOf("store_owner")

        // When
        val permissions = RoleUtils.getDashboardPermissions(ownerRoles)

        // Then
        assertFalse(permissions.canViewAllStores)
        assertTrue(permissions.canViewFinancialDetails)
        assertFalse(permissions.canViewStoreComparison)
        assertTrue(permissions.canAccessAdvancedStatistics)
    }

    @Test
    fun `getDashboardPermissions应该为店员返回基础权限`() {
        // Given
        val staffRoles = listOf("store_staff")

        // When
        val permissions = RoleUtils.getDashboardPermissions(staffRoles)

        // Then
        assertFalse(permissions.canViewAllStores)
        assertFalse(permissions.canViewFinancialDetails)
        assertFalse(permissions.canViewStoreComparison)
        assertFalse(permissions.canAccessAdvancedStatistics)
    }

    @Test
    fun `getAvailableQuickActions应该为管理员返回所有操作`() {
        // Given
        val adminRoles = listOf("admin")

        // When
        val actions = RoleUtils.getAvailableQuickActions(adminRoles)

        // Then
        assertEquals(4, actions.size)
        assertTrue(actions.any { it.action == QuickActionType.CREATE_INVOICE })
        assertTrue(actions.any { it.action == QuickActionType.RECORD_PAYMENT })
        assertTrue(actions.any { it.action == QuickActionType.ADD_CUSTOMER })
        assertTrue(actions.any { it.action == QuickActionType.VIEW_REPORTS })
    }

    @Test
    fun `getAvailableQuickActions应该为店长返回部分操作`() {
        // Given
        val ownerRoles = listOf("store_owner")

        // When
        val actions = RoleUtils.getAvailableQuickActions(ownerRoles)

        // Then
        assertEquals(3, actions.size)
        assertTrue(actions.any { it.action == QuickActionType.CREATE_INVOICE })
        assertTrue(actions.any { it.action == QuickActionType.RECORD_PAYMENT })
        assertTrue(actions.any { it.action == QuickActionType.ADD_CUSTOMER })
        assertFalse(actions.any { it.action == QuickActionType.VIEW_REPORTS })
    }

    @Test
    fun `getAvailableQuickActions应该为店员返回基础操作`() {
        // Given
        val staffRoles = listOf("store_staff")

        // When
        val actions = RoleUtils.getAvailableQuickActions(staffRoles)

        // Then
        assertEquals(2, actions.size)
        assertTrue(actions.any { it.action == QuickActionType.CREATE_INVOICE })
        assertTrue(actions.any { it.action == QuickActionType.RECORD_PAYMENT })
        assertFalse(actions.any { it.action == QuickActionType.ADD_CUSTOMER })
        assertFalse(actions.any { it.action == QuickActionType.VIEW_REPORTS })
    }

    @Test
    fun `hasPermission应该正确验证各种权限`() {
        // Given
        val adminRoles = listOf("admin")
        val ownerRoles = listOf("store_owner")
        val staffRoles = listOf("store_staff")

        // When & Then - 管理员权限
        assertTrue(RoleUtils.hasPermission(adminRoles, Permission.VIEW_ALL_STORES))
        assertTrue(RoleUtils.hasPermission(adminRoles, Permission.VIEW_FINANCIAL_DETAILS))
        assertTrue(RoleUtils.hasPermission(adminRoles, Permission.VIEW_STORE_COMPARISON))
        assertTrue(RoleUtils.hasPermission(adminRoles, Permission.ACCESS_ADVANCED_STATISTICS))
        assertTrue(RoleUtils.hasPermission(adminRoles, Permission.MANAGE_USERS))
        assertTrue(RoleUtils.hasPermission(adminRoles, Permission.MANAGE_STORES))

        // When & Then - 店长权限
        assertFalse(RoleUtils.hasPermission(ownerRoles, Permission.VIEW_ALL_STORES))
        assertTrue(RoleUtils.hasPermission(ownerRoles, Permission.VIEW_FINANCIAL_DETAILS))
        assertFalse(RoleUtils.hasPermission(ownerRoles, Permission.VIEW_STORE_COMPARISON))
        assertTrue(RoleUtils.hasPermission(ownerRoles, Permission.ACCESS_ADVANCED_STATISTICS))
        assertFalse(RoleUtils.hasPermission(ownerRoles, Permission.MANAGE_USERS))
        assertFalse(RoleUtils.hasPermission(ownerRoles, Permission.MANAGE_STORES))

        // When & Then - 店员权限
        assertFalse(RoleUtils.hasPermission(staffRoles, Permission.VIEW_ALL_STORES))
        assertFalse(RoleUtils.hasPermission(staffRoles, Permission.VIEW_FINANCIAL_DETAILS))
        assertFalse(RoleUtils.hasPermission(staffRoles, Permission.VIEW_STORE_COMPARISON))
        assertFalse(RoleUtils.hasPermission(staffRoles, Permission.ACCESS_ADVANCED_STATISTICS))
        assertFalse(RoleUtils.hasPermission(staffRoles, Permission.MANAGE_USERS))
        assertFalse(RoleUtils.hasPermission(staffRoles, Permission.MANAGE_STORES))

        // When & Then - 所有角色都有的权限
        assertTrue(RoleUtils.hasPermission(adminRoles, Permission.CREATE_INVOICE))
        assertTrue(RoleUtils.hasPermission(ownerRoles, Permission.CREATE_INVOICE))
        assertTrue(RoleUtils.hasPermission(staffRoles, Permission.CREATE_INVOICE))
        
        assertTrue(RoleUtils.hasPermission(adminRoles, Permission.RECORD_PAYMENT))
        assertTrue(RoleUtils.hasPermission(ownerRoles, Permission.RECORD_PAYMENT))
        assertTrue(RoleUtils.hasPermission(staffRoles, Permission.RECORD_PAYMENT))
    }

    @Test
    fun `canAccessStore应该正确验证门店访问权限`() {
        // Given
        val adminRoles = listOf("admin")
        val staffRoles = listOf("store_staff")
        val userStoreIds = listOf(1, 2)
        val targetStoreId1 = 1
        val targetStoreId3 = 3

        // When & Then
        assertTrue(RoleUtils.canAccessStore(adminRoles, userStoreIds, targetStoreId1))
        assertTrue(RoleUtils.canAccessStore(adminRoles, userStoreIds, targetStoreId3)) // 管理员可以访问所有门店
        
        assertTrue(RoleUtils.canAccessStore(staffRoles, userStoreIds, targetStoreId1)) // 店员可以访问自己的门店
        assertFalse(RoleUtils.canAccessStore(staffRoles, userStoreIds, targetStoreId3)) // 店员不能访问其他门店
    }

    @Test
    fun `getAccessibleStoreIds应该返回正确的可访问门店列表`() {
        // Given
        val adminRoles = listOf("admin")
        val staffRoles = listOf("store_staff")
        val userStoreIds = listOf(1, 2, 3)

        // When & Then
        assertEquals(emptyList<Int>(), RoleUtils.getAccessibleStoreIds(adminRoles, userStoreIds)) // 管理员返回空列表表示无限制
        assertEquals(userStoreIds, RoleUtils.getAccessibleStoreIds(staffRoles, userStoreIds)) // 其他角色返回用户所属门店
    }

    @Test
    fun `getRoleColor应该返回正确的角色颜色`() {
        // Given & When & Then
        assertEquals("primary", RoleUtils.getRoleColor(listOf("admin")))
        assertEquals("secondary", RoleUtils.getRoleColor(listOf("store_owner")))
        assertEquals("tertiary", RoleUtils.getRoleColor(listOf("store_staff")))
    }
}
