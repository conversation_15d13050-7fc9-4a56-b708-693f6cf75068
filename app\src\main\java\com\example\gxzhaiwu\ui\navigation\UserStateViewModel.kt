package com.example.gxzhaiwu.ui.navigation

import androidx.lifecycle.ViewModel
import com.example.gxzhaiwu.utils.Permission
import com.example.gxzhaiwu.utils.UserStateManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * 用户状态ViewModel
 * 包装UserStateManager以便在Compose中使用
 */
@HiltViewModel
class UserStateViewModel @Inject constructor(
    private val userStateManager: UserStateManager
) : ViewModel() {

    val userState: StateFlow<com.example.gxzhaiwu.utils.UserState> = userStateManager.userState

    /**
     * 设置用户信息
     */
    fun setUser(
        userId: Int,
        userName: String,
        userEmail: String,
        userRoles: List<String>,
        userStoreIds: List<Int> = emptyList()
    ) {
        userStateManager.setUser(userId, userName, userEmail, userRoles, userStoreIds)
    }

    /**
     * 清除用户状态（登出）
     */
    fun clearUser() {
        userStateManager.clearUser()
    }

    /**
     * 获取当前用户角色
     */
    fun getCurrentUserRoles(): List<String> {
        return userStateManager.getCurrentUserRoles()
    }

    /**
     * 获取当前用户ID
     */
    fun getCurrentUserId(): Int? {
        return userStateManager.getCurrentUserId()
    }

    /**
     * 获取当前用户名
     */
    fun getCurrentUserName(): String? {
        return userStateManager.getCurrentUserName()
    }

    /**
     * 检查用户是否已登录
     */
    fun isLoggedIn(): Boolean {
        return userStateManager.isLoggedIn()
    }

    /**
     * 检查用户是否有特定权限
     */
    fun hasPermission(permission: Permission): Boolean {
        return userStateManager.hasPermission(permission)
    }

    /**
     * 检查用户是否为管理员
     */
    fun isAdmin(): Boolean {
        return userStateManager.isAdmin()
    }

    /**
     * 检查用户是否为店长
     */
    fun isStoreOwner(): Boolean {
        return userStateManager.isStoreOwner()
    }

    /**
     * 检查用户是否为店员
     */
    fun isStoreStaff(): Boolean {
        return userStateManager.isStoreStaff()
    }

    /**
     * 获取用户角色显示名称
     */
    fun getUserRoleDisplayName(): String {
        return userStateManager.getUserRoleDisplayName()
    }

    /**
     * 模拟登录（用于测试）
     */
    fun simulateLogin(roleType: String = "admin") {
        userStateManager.simulateLogin(roleType)
    }
}
