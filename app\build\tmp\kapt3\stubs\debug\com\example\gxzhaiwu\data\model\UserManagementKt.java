package com.example.gxzhaiwu.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001c\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\u001a\f\u0010\u0000\u001a\u0004\u0018\u00010\u0001*\u00020\u0002\u001a\n\u0010\u0003\u001a\u00020\u0004*\u00020\u0002\u001a\n\u0010\u0005\u001a\u00020\u0004*\u00020\u0002\u001a\n\u0010\u0006\u001a\u00020\u0004*\u00020\u0002\u001a\n\u0010\u0007\u001a\u00020\b*\u00020\u0002\u001a\n\u0010\t\u001a\u00020\b*\u00020\u0002\u001a\n\u0010\n\u001a\u00020\b*\u00020\u0002\u00a8\u0006\u000b"}, d2 = {"getPrimaryRole", "Lcom/example/gxzhaiwu/data/model/Role;", "Lcom/example/gxzhaiwu/data/model/UserDetail;", "getRoleColor", "", "getRoleDisplayName", "getStoreNames", "isAdmin", "", "isStoreOwner", "isStoreStaff", "app_debug"})
public final class UserManagementKt {
    
    /**
     * 扩展函数：获取用户的主要角色
     */
    @org.jetbrains.annotations.Nullable()
    public static final com.example.gxzhaiwu.data.model.Role getPrimaryRole(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail $this$getPrimaryRole) {
        return null;
    }
    
    /**
     * 扩展函数：获取用户角色显示名称
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getRoleDisplayName(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail $this$getRoleDisplayName) {
        return null;
    }
    
    /**
     * 扩展函数：获取用户角色颜色（用于UI）
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getRoleColor(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail $this$getRoleColor) {
        return null;
    }
    
    /**
     * 扩展函数：检查用户是否为管理员
     */
    public static final boolean isAdmin(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail $this$isAdmin) {
        return false;
    }
    
    /**
     * 扩展函数：检查用户是否为店长
     */
    public static final boolean isStoreOwner(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail $this$isStoreOwner) {
        return false;
    }
    
    /**
     * 扩展函数：检查用户是否为店员
     */
    public static final boolean isStoreStaff(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail $this$isStoreStaff) {
        return false;
    }
    
    /**
     * 扩展函数：获取用户门店名称列表
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getStoreNames(@org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UserDetail $this$getStoreNames) {
        return null;
    }
}