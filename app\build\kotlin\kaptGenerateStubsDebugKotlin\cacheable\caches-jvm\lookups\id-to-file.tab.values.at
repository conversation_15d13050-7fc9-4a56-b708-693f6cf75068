/ Header Record For PersistentHashMapValueStorage> =app/src/main/java/com/example/gxzhaiwu/GxZhaiWuApplication.kt7 6app/src/main/java/com/example/gxzhaiwu/MainActivity.kt? >app/src/main/java/com/example/gxzhaiwu/data/api/ApiResponse.kt; :app/src/main/java/com/example/gxzhaiwu/data/api/AuthApi.ktA @app/src/main/java/com/example/gxzhaiwu/data/api/NetworkModule.ktG Fapp/src/main/java/com/example/gxzhaiwu/data/local/PreferencesModule.ktE Dapp/src/main/java/com/example/gxzhaiwu/data/local/UserPreferences.kt: 9app/src/main/java/com/example/gxzhaiwu/data/model/User.ktI Happ/src/main/java/com/example/gxzhaiwu/data/repository/AuthRepository.ktM Lapp/src/main/java/com/example/gxzhaiwu/data/repository/AuthRepositoryImpl.ktK Japp/src/main/java/com/example/gxzhaiwu/data/repository/RepositoryModule.ktH Gapp/src/main/java/com/example/gxzhaiwu/ui/auth/components/AuthButton.ktK Japp/src/main/java/com/example/gxzhaiwu/ui/auth/components/AuthTextField.ktD Capp/src/main/java/com/example/gxzhaiwu/ui/auth/login/LoginScreen.ktE Dapp/src/main/java/com/example/gxzhaiwu/ui/auth/login/LoginUiState.ktG Fapp/src/main/java/com/example/gxzhaiwu/ui/auth/login/LoginViewModel.ktD Capp/src/main/java/com/example/gxzhaiwu/ui/components/ErrorDialog.ktF Eapp/src/main/java/com/example/gxzhaiwu/ui/components/LoadingDialog.kt= <app/src/main/java/com/example/gxzhaiwu/ui/home/<USER>/src/main/java/com/example/gxzhaiwu/ui/home/<USER>/src/main/java/com/example/gxzhaiwu/ui/navigation/AppNavigation.ktP Oapp/src/main/java/com/example/gxzhaiwu/ui/navigation/AuthNavigationViewModel.kt? >app/src/main/java/com/example/gxzhaiwu/ui/navigation/Screen.kt9 8app/src/main/java/com/example/gxzhaiwu/ui/theme/Color.kt9 8app/src/main/java/com/example/gxzhaiwu/ui/theme/Theme.kt8 7app/src/main/java/com/example/gxzhaiwu/ui/theme/Type.kt: 9app/src/main/java/com/example/gxzhaiwu/utils/Constants.kt= <app/src/main/java/com/example/gxzhaiwu/utils/NetworkUtils.kt> =app/src/main/java/com/example/gxzhaiwu/utils/SecurityUtils.kt: 9app/src/main/java/com/example/gxzhaiwu/utils/TestUtils.kt8 7app/src/main/java/com/example/gxzhaiwu/utils/UiUtils.kt@ ?app/src/main/java/com/example/gxzhaiwu/utils/ValidationUtils.kt8 7app/src/main/java/com/example/gxzhaiwu/utils/UiUtils.kt: 9app/src/main/java/com/example/gxzhaiwu/data/model/User.kt8 7app/src/main/java/com/example/gxzhaiwu/utils/UiUtils.kt; :app/src/main/java/com/example/gxzhaiwu/data/api/AuthApi.ktE Dapp/src/main/java/com/example/gxzhaiwu/data/local/UserPreferences.ktI Happ/src/main/java/com/example/gxzhaiwu/data/repository/AuthRepository.ktM Lapp/src/main/java/com/example/gxzhaiwu/data/repository/AuthRepositoryImpl.kt@ ?app/src/main/java/com/example/gxzhaiwu/ui/home/<USER>/src/main/java/com/example/gxzhaiwu/ui/auth/register/RegisterScreen.ktK Japp/src/main/java/com/example/gxzhaiwu/ui/auth/register/RegisterUiState.ktM Lapp/src/main/java/com/example/gxzhaiwu/ui/auth/register/RegisterViewModel.ktF Eapp/src/main/java/com/example/gxzhaiwu/ui/navigation/AppNavigation.ktH Gapp/src/main/java/com/example/gxzhaiwu/ui/auth/components/AuthButton.ktK Japp/src/main/java/com/example/gxzhaiwu/ui/auth/components/AuthTextField.ktD Capp/src/main/java/com/example/gxzhaiwu/ui/auth/login/LoginScreen.ktJ Iapp/src/main/java/com/example/gxzhaiwu/ui/auth/register/RegisterScreen.kt9 8app/src/main/java/com/example/gxzhaiwu/ui/theme/Color.ktI Happ/src/main/java/com/example/gxzhaiwu/ui/theme/ColorSchemeExtensions.kt9 8app/src/main/java/com/example/gxzhaiwu/ui/theme/Theme.kt@ ?app/src/main/java/com/example/gxzhaiwu/ui/theme/ThemePreview.ktI Happ/src/main/java/com/example/gxzhaiwu/ui/theme/ColorSchemeExtensions.kt@ ?app/src/main/java/com/example/gxzhaiwu/data/api/DashboardApi.ktA @app/src/main/java/com/example/gxzhaiwu/data/api/NetworkModule.kt? >app/src/main/java/com/example/gxzhaiwu/data/model/Dashboard.ktN Mapp/src/main/java/com/example/gxzhaiwu/data/repository/DashboardRepository.ktR Qapp/src/main/java/com/example/gxzhaiwu/data/repository/DashboardRepositoryImpl.ktK Japp/src/main/java/com/example/gxzhaiwu/data/repository/RepositoryModule.ktK Japp/src/main/java/com/example/gxzhaiwu/ui/components/RefreshableContent.ktG Fapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/DashboardScreen.ktH Gapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/DashboardUiState.ktJ Iapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/DashboardViewModel.ktW Vapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/components/FinancialSummaryCard.ktR Qapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/components/QuickActionCard.ktS Rapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/components/RoleBasedContent.ktQ Papp/src/main/java/com/example/gxzhaiwu/ui/dashboard/components/StatisticsCard.ktF Eapp/src/main/java/com/example/gxzhaiwu/ui/navigation/AppNavigation.kt< ;app/src/main/java/com/example/gxzhaiwu/utils/FormatUtils.kt: 9app/src/main/java/com/example/gxzhaiwu/utils/RoleUtils.ktK Japp/src/main/java/com/example/gxzhaiwu/ui/components/RefreshableContent.ktR Qapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/components/QuickActionCard.ktR Qapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/components/QuickActionCard.ktM Lapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/DashboardTestActivity.ktR Qapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/components/QuickActionCard.ktM Lapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/DashboardTestActivity.kt? >app/src/main/java/com/example/gxzhaiwu/data/model/Dashboard.ktG Fapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/DashboardScreen.kt9 8app/src/main/java/com/example/gxzhaiwu/ui/theme/Color.ktI Happ/src/main/java/com/example/gxzhaiwu/ui/theme/ColorSchemeExtensions.kt@ ?app/src/main/java/com/example/gxzhaiwu/ui/theme/ThemePreview.ktE Dapp/src/main/java/com/example/gxzhaiwu/data/api/UserManagementApi.kt? >app/src/main/java/com/example/gxzhaiwu/data/model/Dashboard.kt: 9app/src/main/java/com/example/gxzhaiwu/data/model/User.ktD Capp/src/main/java/com/example/gxzhaiwu/data/model/UserManagement.ktI Happ/src/main/java/com/example/gxzhaiwu/data/repository/AuthRepository.ktM Lapp/src/main/java/com/example/gxzhaiwu/data/repository/AuthRepositoryImpl.ktS Rapp/src/main/java/com/example/gxzhaiwu/data/repository/UserManagementRepository.ktW Vapp/src/main/java/com/example/gxzhaiwu/data/repository/UserManagementRepositoryImpl.ktJ Iapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/DashboardViewModel.ktF Eapp/src/main/java/com/example/gxzhaiwu/ui/navigation/AppNavigation.kt? >app/src/main/java/com/example/gxzhaiwu/ui/navigation/Screen.ktQ Papp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/UserManagementScreen.ktT Sapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/UserManagementViewModel.ktV Uapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/RoleEditDialog.ktP Oapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserCard.ktX Wapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserDetailDialog.ktW Vapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserListContent.kt: 9app/src/main/java/com/example/gxzhaiwu/utils/RoleUtils.kt; :app/src/main/java/com/example/gxzhaiwu/data/api/AuthApi.ktE Dapp/src/main/java/com/example/gxzhaiwu/data/local/UserPreferences.ktR Qapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/components/QuickActionCard.kt@ ?app/src/main/java/com/example/gxzhaiwu/ui/home/<USER>/src/main/java/com/example/gxzhaiwu/data/api/AuthApi.ktE Dapp/src/main/java/com/example/gxzhaiwu/data/local/UserPreferences.kt: 9app/src/main/java/com/example/gxzhaiwu/data/model/User.ktD Capp/src/main/java/com/example/gxzhaiwu/data/model/UserManagement.ktI Happ/src/main/java/com/example/gxzhaiwu/data/repository/AuthRepository.ktM Lapp/src/main/java/com/example/gxzhaiwu/data/repository/AuthRepositoryImpl.ktS Rapp/src/main/java/com/example/gxzhaiwu/data/repository/UserManagementRepository.ktW Vapp/src/main/java/com/example/gxzhaiwu/data/repository/UserManagementRepositoryImpl.kt@ ?app/src/main/java/com/example/gxzhaiwu/ui/home/<USER>/src/main/java/com/example/gxzhaiwu/ui/usermanagement/UserManagementScreen.ktT Sapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/UserManagementViewModel.ktV Uapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/RoleEditDialog.ktP Oapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserCard.ktX Wapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserDetailDialog.ktW Vapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserListContent.ktE Dapp/src/main/java/com/example/gxzhaiwu/data/api/UserManagementApi.ktD Capp/src/main/java/com/example/gxzhaiwu/data/model/UserManagement.ktW Vapp/src/main/java/com/example/gxzhaiwu/data/repository/UserManagementRepositoryImpl.ktQ Papp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/UserManagementScreen.ktT Sapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/UserManagementViewModel.ktP Oapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserCard.ktX Wapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserDetailDialog.ktW Vapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserListContent.ktM Lapp/src/main/java/com/example/gxzhaiwu/data/repository/AuthRepositoryImpl.ktQ Papp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/UserManagementScreen.ktV Uapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/RoleEditDialog.ktP Oapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserCard.ktX Wapp/src/main/java/com/example/gxzhaiwu/ui/usermanagement/components/UserDetailDialog.ktA @app/src/main/java/com/example/gxzhaiwu/data/api/NetworkModule.ktK Japp/src/main/java/com/example/gxzhaiwu/data/repository/RepositoryModule.kt? >app/src/main/java/com/example/gxzhaiwu/data/model/Dashboard.ktJ Iapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/DashboardViewModel.ktR Qapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/components/QuickActionCard.ktO Napp/src/main/java/com/example/gxzhaiwu/ui/management/ManagementCenterScreen.ktR Qapp/src/main/java/com/example/gxzhaiwu/ui/management/ManagementCenterViewModel.ktO Napp/src/main/java/com/example/gxzhaiwu/ui/management/ManagementTestActivity.ktN Mapp/src/main/java/com/example/gxzhaiwu/ui/management/components/ModuleCard.ktF Eapp/src/main/java/com/example/gxzhaiwu/ui/navigation/AppNavigation.kt? >app/src/main/java/com/example/gxzhaiwu/ui/navigation/Screen.kt: 9app/src/main/java/com/example/gxzhaiwu/utils/RoleUtils.ktG Fapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/DashboardScreen.ktS Rapp/src/main/java/com/example/gxzhaiwu/ui/dashboard/components/RoleBasedContent.ktO Napp/src/main/java/com/example/gxzhaiwu/ui/management/ManagementTestActivity.kt