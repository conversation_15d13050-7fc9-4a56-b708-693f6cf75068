package com.example.gxzhaiwu.ui.payments

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.ui.payments.components.*
import com.example.gxzhaiwu.ui.components.*
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme

/**
 * 还款管理主屏幕
 * 显示还款记录列表、搜索、过滤和操作功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PaymentManagementScreen(
    onNavigateToScreen: (String) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: PaymentManagementViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val snackbarHostState = remember { SnackbarHostState() }

    // 处理副作用
    LaunchedEffect(viewModel) {
        viewModel.sideEffect.collect { effect ->
            when (effect) {
                is PaymentManagementSideEffect.ShowError -> {
                    snackbarHostState.showSnackbar(
                        message = effect.message,
                        actionLabel = "确定"
                    )
                }
                is PaymentManagementSideEffect.ShowSuccess -> {
                    snackbarHostState.showSnackbar(effect.message)
                }
                is PaymentManagementSideEffect.NavigateToPaymentDetail -> {
                    onNavigateToScreen("payment_detail/${effect.paymentId}")
                }
                is PaymentManagementSideEffect.NavigateToRecordPayment -> {
                    val route = if (effect.billId != null) {
                        "record_payment?billId=${effect.billId}"
                    } else {
                        "record_payment"
                    }
                    onNavigateToScreen(route)
                }
                is PaymentManagementSideEffect.NavigateToEditPayment -> {
                    onNavigateToScreen("edit_payment/${effect.paymentId}")
                }
                is PaymentManagementSideEffect.NavigateToBillDetail -> {
                    onNavigateToScreen("bill_detail/${effect.billId}")
                }
                is PaymentManagementSideEffect.NavigateBack -> {
                    // 处理返回导航
                }
                is PaymentManagementSideEffect.ShowConfirmDialog -> {
                    // TODO: 显示确认对话框
                }
            }
        }
    }

    Scaffold(
        topBar = {
            PaymentManagementTopBar(
                searchQuery = uiState.searchQuery,
                onSearchQueryChange = viewModel::searchPayments,
                currentFilter = uiState.currentFilter,
                onFilterChange = viewModel::applyFilter,
                onClearFilters = viewModel::clearFilters,
                viewMode = uiState.viewMode,
                onViewModeChange = viewModel::setViewMode
            )
        },
        floatingActionButton = {
            if (uiState.canRecordPayment()) {
                FloatingActionButton(
                    onClick = { viewModel.recordPayment() },
                    containerColor = MaterialTheme.colorScheme.primary
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "记录还款"
                    )
                }
            }
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        modifier = modifier.fillMaxSize()
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 统计信息卡片
            uiState.statistics?.let { statistics ->
                PaymentStatisticsCard(
                    statistics = statistics,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }

            // 还款记录列表
            when {
                uiState.isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                
                uiState.isEmpty() -> {
                    PaymentEmptyState(
                        onRecordPayment = { viewModel.recordPayment() },
                        canRecordPayment = uiState.canRecordPayment(),
                        modifier = Modifier.fillMaxSize()
                    )
                }
                
                uiState.isEmptySearch() -> {
                    PaymentEmptySearchState(
                        searchQuery = uiState.searchQuery,
                        onClearSearch = { viewModel.searchPayments("") },
                        modifier = Modifier.fillMaxSize()
                    )
                }
                
                else -> {
                    RefreshableContent(
                        isRefreshing = uiState.isRefreshing,
                        onRefresh = { viewModel.loadPayments(refresh = true) }
                    ) {
                        when (uiState.viewMode) {
                            PaymentViewMode.LIST -> {
                                PaymentListView(
                                    payments = uiState.getDisplayPayments(),
                                    userRoles = uiState.userRoles,
                                    onPaymentClick = { viewModel.selectPayment(it) },
                                    onActionClick = { action, payment -> 
                                        viewModel.handlePaymentAction(action, payment) 
                                    },
                                    hasMoreData = uiState.hasMoreData,
                                    isLoading = uiState.isLoading,
                                    onLoadMore = { viewModel.loadPayments() },
                                    modifier = Modifier.fillMaxSize()
                                )
                            }
                            PaymentViewMode.SUMMARY -> {
                                PaymentSummaryView(
                                    payments = uiState.getDisplayPayments(),
                                    statistics = uiState.statistics,
                                    modifier = Modifier.fillMaxSize()
                                )
                            }
                            PaymentViewMode.CALENDAR -> {
                                PaymentCalendarView(
                                    payments = uiState.getDisplayPayments(),
                                    onDateSelected = { /* TODO: 实现日期选择 */ },
                                    modifier = Modifier.fillMaxSize()
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    // 错误处理
    if (uiState.hasError) {
        LaunchedEffect(uiState.errorMessage) {
            uiState.errorMessage?.let { message ->
                snackbarHostState.showSnackbar(
                    message = message,
                    actionLabel = "重试"
                )
            }
            viewModel.clearError()
        }
    }
}

/**
 * 还款记录列表视图
 */
@Composable
private fun PaymentListView(
    payments: List<Payment>,
    userRoles: List<String>,
    onPaymentClick: (Payment) -> Unit,
    onActionClick: (PaymentAction, Payment) -> Unit,
    hasMoreData: Boolean,
    isLoading: Boolean,
    onLoadMore: () -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = modifier
    ) {
        items(
            items = payments,
            key = { it.id }
        ) { payment ->
            PaymentCard(
                payment = payment,
                availableActions = PaymentAction.getAvailableActions(userRoles, payment),
                onPaymentClick = { onPaymentClick(payment) },
                onActionClick = { action -> onActionClick(action, payment) },
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 加载更多指示器
        if (hasMoreData && !isLoading) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Button(onClick = onLoadMore) {
                        Text("加载更多")
                    }
                }
            }
        }
    }
}

/**
 * 还款汇总视图
 */
@Composable
private fun PaymentSummaryView(
    payments: List<Payment>,
    statistics: PaymentStatistics?,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = modifier
    ) {
        // 统计卡片
        statistics?.let { stats ->
            item {
                PaymentStatisticsCard(
                    statistics = stats,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        // 按支付方式分组的汇总
        item {
            PaymentMethodSummaryCard(
                payments = payments,
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 按状态分组的汇总
        item {
            PaymentStatusSummaryCard(
                payments = payments,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 还款日历视图
 */
@Composable
private fun PaymentCalendarView(
    payments: List<Payment>,
    onDateSelected: (java.time.LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(16.dp)
    ) {
        Text(
            text = "日历视图",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // TODO: 实现日历组件
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "日历组件待实现",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

// 预览组件
@Preview(name = "还款管理屏幕")
@Composable
private fun PaymentManagementScreenPreview() {
    GxZhaiWuTheme {
        Surface {
            PaymentManagementScreen(
                onNavigateToScreen = {}
            )
        }
    }
}
