package com.example.gxzhaiwu.data.repository

import com.example.gxzhaiwu.data.api.AuthApi
import com.example.gxzhaiwu.data.local.UserPreferences
import com.example.gxzhaiwu.data.model.LoginRequest
import com.example.gxzhaiwu.data.model.RegisterRequest
import com.example.gxzhaiwu.data.model.User
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepositoryImpl @Inject constructor(
    private val authApi: AuthApi,
    private val userPreferences: UserPreferences
) : AuthRepository {

    override suspend fun login(loginRequest: LoginRequest): Result<Pair<String, User>> {
        return try {
            val response = authApi.login(loginRequest)
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    val token = apiResponse.data.token
                    val user = apiResponse.data.user
                    
                    // 保存登录信息到本地
                    userPreferences.saveLoginData(token, user)
                    
                    Result.success(Pair(token, user))
                } else {
                    Result.failure(Exception(apiResponse?.message ?: "登录失败"))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun register(registerRequest: RegisterRequest): Result<Pair<String, User>> {
        return try {
            val response = authApi.register(registerRequest)
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    val token = apiResponse.data.token
                    val user = apiResponse.data.user
                    
                    // 保存注册后的登录信息到本地
                    userPreferences.saveLoginData(token, user)
                    
                    Result.success(Pair(token, user))
                } else {
                    Result.failure(Exception(apiResponse?.message ?: "注册失败"))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun logout(): Result<Unit> {
        return try {
            val token = userPreferences.authToken.first()
            if (!token.isNullOrEmpty()) {
                val response = authApi.logout("Bearer $token")
                // 无论服务器响应如何，都清除本地数据
                userPreferences.clearAll()
                
                if (response.isSuccessful) {
                    Result.success(Unit)
                } else {
                    // 即使服务器返回错误，本地登出仍然成功
                    Result.success(Unit)
                }
            } else {
                // 没有token，直接清除本地数据
                userPreferences.clearAll()
                Result.success(Unit)
            }
        } catch (e: Exception) {
            // 发生异常时也要清除本地数据
            userPreferences.clearAll()
            Result.success(Unit)
        }
    }

    override suspend fun getCurrentUser(): Result<User> {
        return try {
            val token = userPreferences.authToken.first()
            if (!token.isNullOrEmpty()) {
                val response = authApi.getCurrentUser("Bearer $token")
                if (response.isSuccessful) {
                    val apiResponse = response.body()
                    if (apiResponse?.success == true && apiResponse.data != null) {
                        // 更新本地用户信息
                        userPreferences.saveUser(apiResponse.data)
                        Result.success(apiResponse.data)
                    } else {
                        Result.failure(Exception(apiResponse?.message ?: "获取用户信息失败"))
                    }
                } else {
                    Result.failure(Exception("网络请求失败: ${response.code()}"))
                }
            } else {
                Result.failure(Exception("未登录"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getAuthToken(): Flow<String?> = userPreferences.authToken

    override fun isLoggedIn(): Flow<Boolean> = userPreferences.isLoggedIn

    override fun getCurrentUserFlow(): Flow<User?> = userPreferences.currentUser

    override fun getCurrentUserRoles(): List<String> {
        return try {
            // 使用runBlocking获取当前用户信息
            kotlinx.coroutines.runBlocking {
                val user = userPreferences.currentUser.first()
                user?.roles ?: emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    override suspend fun clearUserData() {
        userPreferences.clearAll()
    }
}
