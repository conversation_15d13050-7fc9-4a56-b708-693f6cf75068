package com.example.gxzhaiwu.ui.management

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gxzhaiwu.data.model.ManagementCenterUiState
import com.example.gxzhaiwu.data.model.ManagementModule
import com.example.gxzhaiwu.utils.Permission
import com.example.gxzhaiwu.utils.RoleUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import android.util.Log
import javax.inject.Inject

/**
 * 管理中心ViewModel
 * 处理管理模块的加载和权限控制
 */
@HiltViewModel
class ManagementCenterViewModel @Inject constructor() : ViewModel() {

    private val _uiState = MutableStateFlow(ManagementCenterUiState())
    val uiState: StateFlow<ManagementCenterUiState> = _uiState.asStateFlow()

    /**
     * 初始化用户角色并加载模块
     */
    fun initializeUserRoles(userRoles: List<String>) {
        Log.d("ManagementCenter", "初始化用户角色: $userRoles")
        _uiState.update { it.copy(userRoles = userRoles) }
        loadModules()
    }

    /**
     * 加载可用的管理模块
     */
    fun loadModules() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, hasError = false, errorMessage = null) }

            try {
                val userRoles = _uiState.value.userRoles
                val allModules = getAllManagementModules()

                Log.d("ManagementCenter", "用户角色: $userRoles")
                Log.d("ManagementCenter", "所有模块数量: ${allModules.size}")

                // 根据用户权限过滤模块
                val availableModules = allModules.filter { module ->
                    val hasPermission = RoleUtils.hasPermission(userRoles, module.requiredPermission)
                    Log.d("ManagementCenter", "模块 ${module.title} (${module.requiredPermission}) - 权限检查: $hasPermission")
                    hasPermission
                }

                Log.d("ManagementCenter", "可用模块数量: ${availableModules.size}")
                Log.d("ManagementCenter", "可用模块: ${availableModules.map { it.title }}")

                _uiState.update {
                    it.copy(
                        isLoading = false,
                        modules = availableModules,
                        hasError = false,
                        errorMessage = null
                    )
                }
            } catch (e: Exception) {
                Log.e("ManagementCenter", "加载管理模块失败", e)
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        hasError = true,
                        errorMessage = e.message ?: "加载管理模块失败"
                    )
                }
            }
        }
    }

    /**
     * 获取所有管理模块的定义
     */
    private fun getAllManagementModules(): List<ManagementModule> {
        return listOf(
            ManagementModule(
                id = "store_management",
                title = "门店管理",
                description = "管理门店信息和设置",
                icon = "store",
                requiredPermission = Permission.MANAGE_STORES,
                route = "store_management"
            ),
            ManagementModule(
                id = "user_management",
                title = "用户管理",
                description = "管理系统用户和权限",
                icon = "people",
                requiredPermission = Permission.MANAGE_USERS,
                route = "user_management"
            ),
            ManagementModule(
                id = "customer_management",
                title = "客户管理",
                description = "管理客户信息和关系",
                icon = "person",
                requiredPermission = Permission.MANAGE_CUSTOMERS,
                route = "customer_management"
            ),
            ManagementModule(
                id = "bill_management",
                title = "账单管理",
                description = "管理账单信息和状态",
                icon = "receipt",
                requiredPermission = Permission.MANAGE_BILLS,
                route = "bill_management"
            ),
            ManagementModule(
                id = "payment_management",
                title = "还款管理",
                description = "管理还款记录和状态",
                icon = "payment",
                requiredPermission = Permission.MANAGE_PAYMENTS,
                route = "payment_management"
            ),
            ManagementModule(
                id = "system_management",
                title = "系统管理",
                description = "系统设置和配置管理",
                icon = "settings",
                requiredPermission = Permission.MANAGE_SYSTEM,
                route = "system_management"
            )
        )
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.update { it.copy(hasError = false, errorMessage = null) }
    }

    /**
     * 重新加载模块
     */
    fun refresh() {
        loadModules()
    }

    /**
     * 检查用户是否有访问管理中心的权限
     */
    fun hasManagementAccess(): Boolean {
        val userRoles = _uiState.value.userRoles
        return RoleUtils.hasPermission(userRoles, Permission.ACCESS_MANAGEMENT_CENTER)
    }

    /**
     * 获取用户可访问的模块数量
     */
    fun getAccessibleModuleCount(): Int {
        return _uiState.value.modules.size
    }

    /**
     * 根据模块ID获取模块信息
     */
    fun getModuleById(moduleId: String): ManagementModule? {
        return _uiState.value.modules.find { it.id == moduleId }
    }

    /**
     * 验证用户是否有权限访问特定模块
     */
    fun hasModulePermission(moduleId: String): Boolean {
        val module = getModuleById(moduleId)
        return if (module != null) {
            RoleUtils.hasPermission(_uiState.value.userRoles, module.requiredPermission)
        } else {
            false
        }
    }

    /**
     * 获取用户角色的显示名称
     */
    fun getUserRoleDisplayName(): String {
        return RoleUtils.getRoleDisplayName(_uiState.value.userRoles)
    }

    /**
     * 检查是否为管理员
     */
    fun isAdmin(): Boolean {
        return RoleUtils.isAdmin(_uiState.value.userRoles)
    }

    /**
     * 检查是否为店长
     */
    fun isStoreOwner(): Boolean {
        return RoleUtils.isStoreOwner(_uiState.value.userRoles)
    }
}
