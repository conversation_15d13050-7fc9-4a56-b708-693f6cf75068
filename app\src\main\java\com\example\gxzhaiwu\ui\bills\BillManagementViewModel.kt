package com.example.gxzhaiwu.ui.bills

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import com.example.gxzhaiwu.data.model.*
import com.example.gxzhaiwu.utils.RoleUtils
import java.math.BigDecimal
import java.time.LocalDateTime
import javax.inject.Inject

/**
 * 账单管理ViewModel
 * 管理账单列表、搜索、过滤和操作
 */
@HiltViewModel
class BillManagementViewModel @Inject constructor(
    // TODO: 注入账单仓库和相关服务
) : ViewModel() {

    private val _uiState = MutableStateFlow(BillManagementUiState())
    val uiState: StateFlow<BillManagementUiState> = _uiState.asStateFlow()

    private val _sideEffect = MutableSharedFlow<BillManagementSideEffect>()
    val sideEffect: SharedFlow<BillManagementSideEffect> = _sideEffect.asSharedFlow()

    init {
        initializeUserRoles()
        loadBills()
    }

    /**
     * 初始化用户角色
     */
    private fun initializeUserRoles() {
        viewModelScope.launch {
            try {
                // TODO: 从认证服务获取用户角色
                val userRoles = getMockUserRoles()
                _uiState.update { it.copy(userRoles = userRoles) }
            } catch (e: Exception) {
                handleError("获取用户信息失败", e)
            }
        }
    }

    /**
     * 加载账单列表
     */
    fun loadBills(refresh: Boolean = false) {
        viewModelScope.launch {
            try {
                _uiState.update { 
                    it.copy(
                        isLoading = !refresh,
                        isRefreshing = refresh,
                        hasError = false,
                        errorMessage = null
                    )
                }

                // TODO: 调用实际的API
                val bills = getMockBills()
                val statistics = calculateStatistics(bills)

                _uiState.update { currentState ->
                    val newBills = if (refresh) bills else currentState.bills + bills
                    currentState.copy(
                        bills = newBills,
                        filteredBills = applyFilters(newBills, currentState.currentFilter, currentState.searchQuery),
                        statistics = statistics,
                        isLoading = false,
                        isRefreshing = false,
                        hasMoreData = bills.size >= 20 // 假设每页20条
                    )
                }
            } catch (e: Exception) {
                handleError("加载账单失败", e)
            }
        }
    }

    /**
     * 搜索账单
     */
    fun searchBills(query: String) {
        _uiState.update { currentState ->
            val filteredBills = applyFilters(currentState.bills, currentState.currentFilter, query)
            currentState.copy(
                searchQuery = query,
                filteredBills = filteredBills
            )
        }
    }

    /**
     * 应用过滤器
     */
    fun applyFilter(filter: BillFilter) {
        _uiState.update { currentState ->
            val filteredBills = applyFilters(currentState.bills, filter, currentState.searchQuery)
            currentState.copy(
                currentFilter = filter,
                filteredBills = filteredBills
            )
        }
    }

    /**
     * 清除过滤器
     */
    fun clearFilters() {
        _uiState.update { currentState ->
            val clearedFilter = BillFilter()
            val filteredBills = applyFilters(currentState.bills, clearedFilter, currentState.searchQuery)
            currentState.copy(
                currentFilter = clearedFilter,
                filteredBills = filteredBills
            )
        }
    }

    /**
     * 选择账单
     */
    fun selectBill(bill: Bill) {
        _uiState.update { it.copy(selectedBill = bill) }
    }

    /**
     * 处理账单操作
     */
    fun handleBillAction(action: BillAction, bill: Bill) {
        viewModelScope.launch {
            when (action) {
                BillAction.VIEW -> {
                    _sideEffect.emit(BillManagementSideEffect.NavigateToBillDetail(bill.id))
                }
                BillAction.EDIT -> {
                    _sideEffect.emit(BillManagementSideEffect.NavigateToEditBill(bill.id))
                }
                BillAction.DELETE -> {
                    deleteBill(bill)
                }
                BillAction.RECORD_PAYMENT -> {
                    // TODO: 导航到记录还款页面
                    _sideEffect.emit(BillManagementSideEffect.ShowSuccess("导航到记录还款页面"))
                }
                BillAction.DUPLICATE -> {
                    duplicateBill(bill)
                }
                BillAction.EXPORT -> {
                    exportBill(bill)
                }
            }
        }
    }

    /**
     * 创建新账单
     */
    fun createBill(customerId: Int? = null) {
        viewModelScope.launch {
            _sideEffect.emit(BillManagementSideEffect.NavigateToCreateBill(customerId))
        }
    }

    /**
     * 删除账单
     */
    private suspend fun deleteBill(bill: Bill) {
        try {
            // TODO: 调用删除API
            _uiState.update { currentState ->
                val updatedBills = currentState.bills.filter { it.id != bill.id }
                val filteredBills = applyFilters(updatedBills, currentState.currentFilter, currentState.searchQuery)
                currentState.copy(
                    bills = updatedBills,
                    filteredBills = filteredBills,
                    statistics = calculateStatistics(updatedBills)
                )
            }
            _sideEffect.emit(BillManagementSideEffect.ShowSuccess("账单删除成功"))
        } catch (e: Exception) {
            handleError("删除账单失败", e)
        }
    }

    /**
     * 复制账单
     */
    private suspend fun duplicateBill(bill: Bill) {
        try {
            // TODO: 调用复制API
            _sideEffect.emit(BillManagementSideEffect.ShowSuccess("账单复制成功"))
            loadBills(refresh = true)
        } catch (e: Exception) {
            handleError("复制账单失败", e)
        }
    }

    /**
     * 导出账单
     */
    private suspend fun exportBill(bill: Bill) {
        try {
            // TODO: 实现导出功能
            _sideEffect.emit(BillManagementSideEffect.ShowSuccess("账单导出成功"))
        } catch (e: Exception) {
            handleError("导出账单失败", e)
        }
    }

    /**
     * 应用过滤和搜索逻辑
     */
    private fun applyFilters(bills: List<Bill>, filter: BillFilter, searchQuery: String): List<Bill> {
        var filteredBills = bills

        // 应用搜索
        if (searchQuery.isNotEmpty()) {
            filteredBills = filteredBills.filter { bill ->
                bill.billNumber.contains(searchQuery, ignoreCase = true) ||
                bill.customerName.contains(searchQuery, ignoreCase = true) ||
                bill.storeName.contains(searchQuery, ignoreCase = true)
            }
        }

        // 应用过滤器
        filter.status?.let { status ->
            filteredBills = filteredBills.filter { it.status == status }
        }

        filter.storeId?.let { storeId ->
            filteredBills = filteredBills.filter { it.storeId == storeId }
        }

        filter.customerId?.let { customerId ->
            filteredBills = filteredBills.filter { it.customerId == customerId }
        }

        filter.dateRange?.let { dateRange ->
            filteredBills = filteredBills.filter { dateRange.contains(it.createdAt) }
        }

        filter.amountRange?.let { amountRange ->
            filteredBills = filteredBills.filter { amountRange.contains(it.totalAmount) }
        }

        return filteredBills
    }

    /**
     * 计算统计信息
     */
    private fun calculateStatistics(bills: List<Bill>): BillStatistics {
        val totalAmount = bills.sumOf { it.totalAmount }
        val paidAmount = bills.sumOf { it.paidAmount }
        val pendingAmount = bills.filter { it.status == BillStatus.PENDING }.sumOf { it.remainingAmount }
        val overdueAmount = bills.filter { it.status == BillStatus.OVERDUE }.sumOf { it.remainingAmount }
        val overdueCount = bills.count { it.status == BillStatus.OVERDUE }

        return BillStatistics(
            totalBills = bills.size,
            totalAmount = totalAmount,
            paidAmount = paidAmount,
            pendingAmount = pendingAmount,
            overdueAmount = overdueAmount,
            overdueCount = overdueCount
        )
    }

    /**
     * 处理错误
     */
    private fun handleError(message: String, exception: Exception) {
        _uiState.update { 
            it.copy(
                isLoading = false,
                isRefreshing = false,
                hasError = true,
                errorMessage = "$message: ${exception.message}"
            )
        }
        viewModelScope.launch {
            _sideEffect.emit(BillManagementSideEffect.ShowError("$message: ${exception.message}"))
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.update { it.copy(hasError = false, errorMessage = null) }
    }

    // 模拟数据方法
    private suspend fun getMockUserRoles(): List<String> {
        kotlinx.coroutines.delay(100)
        return listOf("admin") // 模拟管理员角色
    }

    private suspend fun getMockBills(): List<Bill> {
        kotlinx.coroutines.delay(500)
        return listOf(
            Bill(
                id = 1,
                billNumber = "BILL-2024-001",
                customerId = 1,
                customerName = "张三",
                storeId = 1,
                storeName = "总店",
                totalAmount = BigDecimal("1000.00"),
                paidAmount = BigDecimal("500.00"),
                remainingAmount = BigDecimal("500.00"),
                status = BillStatus.PARTIAL_PAID,
                dueDate = LocalDateTime.now().plusDays(7),
                createdAt = LocalDateTime.now().minusDays(3),
                updatedAt = LocalDateTime.now().minusDays(1)
            ),
            Bill(
                id = 2,
                billNumber = "BILL-2024-002",
                customerId = 2,
                customerName = "李四",
                storeId = 1,
                storeName = "总店",
                totalAmount = BigDecimal("2000.00"),
                paidAmount = BigDecimal.ZERO,
                remainingAmount = BigDecimal("2000.00"),
                status = BillStatus.PENDING,
                dueDate = LocalDateTime.now().plusDays(14),
                createdAt = LocalDateTime.now().minusDays(1),
                updatedAt = LocalDateTime.now().minusDays(1)
            )
        )
    }
}
