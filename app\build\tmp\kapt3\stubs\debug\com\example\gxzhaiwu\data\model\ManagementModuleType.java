package com.example.gxzhaiwu.data.model;

/**
 * 管理模块类型枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/example/gxzhaiwu/data/model/ManagementModuleType;", "", "(<PERSON>ja<PERSON>/lang/String;I)V", "USER_MANAGEMENT", "STORE_MANAGEMENT", "BILL_MANAGEMENT", "PAYMENT_MANAGEMENT", "ADVANCED_REPORTS", "app_debug"})
public enum ManagementModuleType {
    /*public static final*/ USER_MANAGEMENT /* = new USER_MANAGEMENT() */,
    /*public static final*/ STORE_MANAGEMENT /* = new STORE_MANAGEMENT() */,
    /*public static final*/ BILL_MANAGEMENT /* = new BILL_MANAGEMENT() */,
    /*public static final*/ PAYMENT_MANAGEMENT /* = new PAYMENT_MANAGEMENT() */,
    /*public static final*/ ADVANCED_REPORTS /* = new ADVANCED_REPORTS() */;
    
    ManagementModuleType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.gxzhaiwu.data.model.ManagementModuleType> getEntries() {
        return null;
    }
}