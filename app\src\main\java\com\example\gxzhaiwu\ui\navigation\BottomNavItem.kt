package com.example.gxzhaiwu.ui.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector
import com.example.gxzhaiwu.utils.Permission

/**
 * 底部导航项定义
 * 支持权限驱动的动态显示
 */
sealed class BottomNavItem(
    val route: String,
    val title: String,
    val icon: ImageVector,
    val selectedIcon: ImageVector = icon,
    val requiredPermission: Permission? = null
) {
    /**
     * 仪表盘 - 所有用户可访问
     */
    object Dashboard : BottomNavItem(
        route = "main_dashboard",
        title = "仪表盘",
        icon = Icons.Default.Dashboard,
        selectedIcon = Icons.Default.Dashboard
    )

    /**
     * 账单管理 - 所有用户可访问（数据按门店过滤）
     */
    object Bills : BottomNavItem(
        route = "main_bills",
        title = "账单管理",
        icon = Icons.Default.Receipt,
        selectedIcon = Icons.Default.Receipt
    )

    /**
     * 还款管理 - 所有用户可访问（数据按门店过滤）
     */
    object Payments : BottomNavItem(
        route = "main_payments",
        title = "还款管理",
        icon = Icons.Default.Payment,
        selectedIcon = Icons.Default.Payment
    )

    /**
     * 管理中心 - 仅管理员和店长可访问
     */
    object Management : BottomNavItem(
        route = "main_management",
        title = "管理中心",
        icon = Icons.Default.AdminPanelSettings,
        selectedIcon = Icons.Default.AdminPanelSettings,
        requiredPermission = Permission.ACCESS_MANAGEMENT_CENTER
    )

    companion object {
        /**
         * 获取所有底部导航项
         */
        fun getAllItems(): List<BottomNavItem> = listOf(
            Dashboard,
            Bills,
            Payments,
            Management
        )

        /**
         * 根据用户权限过滤可用的导航项
         */
        fun getAvailableItems(userRoles: List<String>): List<BottomNavItem> {
            return getAllItems().filter { item ->
                item.requiredPermission?.let { permission ->
                    com.example.gxzhaiwu.utils.RoleUtils.hasPermission(userRoles, permission)
                } ?: true
            }
        }

        /**
         * 根据路由获取导航项
         */
        fun fromRoute(route: String): BottomNavItem? {
            return getAllItems().find { it.route == route }
        }

        /**
         * 获取默认导航项（仪表盘）
         */
        fun getDefaultItem(): BottomNavItem = Dashboard
    }
}

/**
 * 底部导航状态管理
 */
data class BottomNavState(
    val currentRoute: String = BottomNavItem.Dashboard.route,
    val availableItems: List<BottomNavItem> = emptyList(),
    val isVisible: Boolean = true
) {
    /**
     * 获取当前选中的导航项
     */
    fun getCurrentItem(): BottomNavItem? {
        return availableItems.find { it.route == currentRoute }
    }

    /**
     * 检查指定路由是否为当前选中项
     */
    fun isSelected(route: String): Boolean {
        return currentRoute == route
    }

    /**
     * 检查是否有权限访问管理中心
     */
    fun hasManagementAccess(): Boolean {
        return availableItems.any { it is BottomNavItem.Management }
    }
}
