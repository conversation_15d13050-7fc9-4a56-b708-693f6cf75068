package com.example.gxzhaiwu.ui.bills

import com.example.gxzhaiwu.data.model.*

/**
 * 账单管理UI状态
 */
data class BillManagementUiState(
    val bills: List<Bill> = emptyList(),
    val filteredBills: List<Bill> = emptyList(),
    val statistics: BillStatistics? = null,
    val selectedBill: Bill? = null,
    val currentFilter: BillFilter = BillFilter(),
    val searchQuery: String = "",
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val hasError: Boolean = false,
    val errorMessage: String? = null,
    val hasMoreData: Boolean = true,
    val currentPage: Int = 1,
    val userRoles: List<String> = emptyList()
) {
    /**
     * 检查是否有数据
     */
    fun hasData(): Boolean = bills.isNotEmpty()

    /**
     * 检查是否为空状态
     */
    fun isEmpty(): Boolean = !isLoading && bills.isEmpty()

    /**
     * 检查是否显示空搜索结果
     */
    fun isEmptySearch(): Boolean = !isLoading && searchQuery.isNotEmpty() && filteredBills.isEmpty()

    /**
     * 获取显示的账单列表
     */
    fun getDisplayBills(): List<Bill> {
        return if (searchQuery.isNotEmpty() || currentFilter.hasActiveFilter()) {
            filteredBills
        } else {
            bills
        }
    }

    /**
     * 检查用户是否有创建权限
     */
    fun canCreateBill(): Boolean {
        return com.example.gxzhaiwu.utils.RoleUtils.hasPermission(userRoles, com.example.gxzhaiwu.utils.Permission.CREATE_INVOICE)
    }

    /**
     * 检查用户是否有编辑权限
     */
    fun canEditBill(): Boolean {
        return com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles) || 
               com.example.gxzhaiwu.utils.RoleUtils.isStoreOwner(userRoles)
    }

    /**
     * 检查用户是否有删除权限
     */
    fun canDeleteBill(): Boolean {
        return com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles)
    }
}

/**
 * 账单过滤器
 */
data class BillFilter(
    val status: BillStatus? = null,
    val storeId: Int? = null,
    val customerId: Int? = null,
    val dateRange: DateRange? = null,
    val amountRange: AmountRange? = null
) {
    /**
     * 检查是否有激活的过滤条件
     */
    fun hasActiveFilter(): Boolean {
        return status != null || storeId != null || customerId != null || 
               dateRange != null || amountRange != null
    }

    /**
     * 获取激活的过滤条件数量
     */
    fun getActiveFilterCount(): Int {
        var count = 0
        if (status != null) count++
        if (storeId != null) count++
        if (customerId != null) count++
        if (dateRange != null) count++
        if (amountRange != null) count++
        return count
    }

    /**
     * 清除所有过滤条件
     */
    fun clear(): BillFilter {
        return BillFilter()
    }
}

/**
 * 日期范围
 */
data class DateRange(
    val startDate: java.time.LocalDateTime,
    val endDate: java.time.LocalDateTime
) {
    /**
     * 检查日期是否在范围内
     */
    fun contains(date: java.time.LocalDateTime): Boolean {
        return !date.isBefore(startDate) && !date.isAfter(endDate)
    }

    /**
     * 获取范围描述
     */
    fun getDescription(): String {
        val formatter = java.time.format.DateTimeFormatter.ofPattern("MM/dd")
        return "${startDate.format(formatter)} - ${endDate.format(formatter)}"
    }
}

/**
 * 金额范围
 */
data class AmountRange(
    val minAmount: java.math.BigDecimal,
    val maxAmount: java.math.BigDecimal
) {
    /**
     * 检查金额是否在范围内
     */
    fun contains(amount: java.math.BigDecimal): Boolean {
        return amount >= minAmount && amount <= maxAmount
    }

    /**
     * 获取范围描述
     */
    fun getDescription(): String {
        return "¥${minAmount} - ¥${maxAmount}"
    }
}

/**
 * 账单管理副作用
 */
sealed class BillManagementSideEffect {
    data class ShowError(val message: String) : BillManagementSideEffect()
    data class ShowSuccess(val message: String) : BillManagementSideEffect()
    data class NavigateToBillDetail(val billId: Int) : BillManagementSideEffect()
    data class NavigateToCreateBill(val customerId: Int? = null) : BillManagementSideEffect()
    data class NavigateToEditBill(val billId: Int) : BillManagementSideEffect()
    object NavigateBack : BillManagementSideEffect()
}

/**
 * 账单排序选项
 */
enum class BillSortOption(val displayName: String, val sortBy: String, val sortOrder: String) {
    CREATED_DESC("最新创建", "created_at", "desc"),
    CREATED_ASC("最早创建", "created_at", "asc"),
    AMOUNT_DESC("金额从高到低", "total_amount", "desc"),
    AMOUNT_ASC("金额从低到高", "total_amount", "asc"),
    DUE_DATE_ASC("到期日期", "due_date", "asc"),
    STATUS("状态", "status", "asc");

    companion object {
        fun getDefault(): BillSortOption = CREATED_DESC
    }
}

/**
 * 账单操作类型
 */
enum class BillAction(val displayName: String) {
    VIEW("查看详情"),
    EDIT("编辑"),
    DELETE("删除"),
    RECORD_PAYMENT("记录还款"),
    DUPLICATE("复制账单"),
    EXPORT("导出");

    companion object {
        fun getAvailableActions(userRoles: List<String>, bill: Bill): List<BillAction> {
            val actions = mutableListOf<BillAction>()
            
            // 所有用户都可以查看
            actions.add(VIEW)
            
            // 管理员和店长可以编辑
            if (com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles) || 
                com.example.gxzhaiwu.utils.RoleUtils.isStoreOwner(userRoles)) {
                actions.add(EDIT)
                actions.add(DUPLICATE)
            }
            
            // 只有管理员可以删除
            if (com.example.gxzhaiwu.utils.RoleUtils.isAdmin(userRoles)) {
                actions.add(DELETE)
            }
            
            // 未完全付清的账单可以记录还款
            if (!bill.isFullyPaid()) {
                actions.add(RECORD_PAYMENT)
            }
            
            // 所有用户都可以导出
            actions.add(EXPORT)
            
            return actions
        }
    }
}
