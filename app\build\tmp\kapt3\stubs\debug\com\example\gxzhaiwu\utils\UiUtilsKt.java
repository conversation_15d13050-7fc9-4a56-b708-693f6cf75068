package com.example.gxzhaiwu.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\u001a\u000e\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001H\u0007\u001a\u000e\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00040\u0001H\u0007\u00a8\u0006\u0005"}, d2 = {"rememberIsKeyboardOpen", "Landroidx/compose/runtime/State;", "", "rememberKeyboardHeight", "", "app_debug"})
public final class UiUtilsKt {
    
    /**
     * 检测键盘是否显示的Composable函数
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.runtime.State<java.lang.Boolean> rememberIsKeyboardOpen() {
        return null;
    }
    
    /**
     * 获取键盘高度的Composable函数
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.runtime.State<java.lang.Integer> rememberKeyboardHeight() {
        return null;
    }
}