package com.example.gxzhaiwu.data.api;

/**
 * 用户管理API接口
 * 提供用户管理相关的网络请求接口
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u0007J\u001e\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u0007J(\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\b\b\u0001\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJJ\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\n\b\u0003\u0010\u0011\u001a\u0004\u0018\u00010\u00062\n\b\u0003\u0010\u0012\u001a\u0004\u0018\u00010\u00062\b\b\u0003\u0010\u0013\u001a\u00020\r2\b\b\u0003\u0010\u0014\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u0015J2\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\b\b\u0001\u0010\f\u001a\u00020\r2\b\b\u0001\u0010\u0017\u001a\u00020\u0018H\u00a7@\u00a2\u0006\u0002\u0010\u0019J2\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\b\b\u0001\u0010\f\u001a\u00020\r2\b\b\u0001\u0010\u0017\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010\u001c\u00a8\u0006\u001d"}, d2 = {"Lcom/example/gxzhaiwu/data/api/UserManagementApi;", "", "getRoles", "Lretrofit2/Response;", "Lcom/example/gxzhaiwu/data/model/RoleListResponse;", "authorization", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getStores", "Lcom/example/gxzhaiwu/data/api/StoreListResponse;", "getUserDetail", "Lcom/example/gxzhaiwu/data/model/UserDetailResponse;", "userId", "", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUsers", "Lcom/example/gxzhaiwu/data/model/UserListResponse;", "search", "role", "page", "perPage", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserRoles", "request", "Lcom/example/gxzhaiwu/data/model/UpdateUserRolesRequest;", "(Ljava/lang/String;ILcom/example/gxzhaiwu/data/model/UpdateUserRolesRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserStores", "Lcom/example/gxzhaiwu/data/model/UpdateUserStoresRequest;", "(Ljava/lang/String;ILcom/example/gxzhaiwu/data/model/UpdateUserStoresRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface UserManagementApi {
    
    /**
     * 获取用户列表
     * 支持搜索、角色筛选和分页
     */
    @retrofit2.http.GET(value = "users")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUsers(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Query(value = "search")
    @org.jetbrains.annotations.Nullable()
    java.lang.String search, @retrofit2.http.Query(value = "role")
    @org.jetbrains.annotations.Nullable()
    java.lang.String role, @retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "per_page")
    int perPage, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.gxzhaiwu.data.model.UserListResponse>> $completion);
    
    /**
     * 获取用户详情
     */
    @retrofit2.http.GET(value = "users/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserDetail(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Path(value = "id")
    int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.gxzhaiwu.data.model.UserDetailResponse>> $completion);
    
    /**
     * 更新用户角色
     */
    @retrofit2.http.PUT(value = "users/{id}/roles")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateUserRoles(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Path(value = "id")
    int userId, @retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UpdateUserRolesRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.gxzhaiwu.data.model.UserDetailResponse>> $completion);
    
    /**
     * 更新用户门店权限
     */
    @retrofit2.http.PUT(value = "users/{id}/stores")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateUserStores(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Path(value = "id")
    int userId, @retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.gxzhaiwu.data.model.UpdateUserStoresRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.gxzhaiwu.data.model.UserDetailResponse>> $completion);
    
    /**
     * 获取角色列表
     */
    @retrofit2.http.GET(value = "roles")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRoles(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.gxzhaiwu.data.model.RoleListResponse>> $completion);
    
    /**
     * 获取门店列表（用于分配门店权限）
     */
    @retrofit2.http.GET(value = "stores")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getStores(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.gxzhaiwu.data.api.StoreListResponse>> $completion);
    
    /**
     * 用户管理API接口
     * 提供用户管理相关的网络请求接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}