package com.example.gxzhaiwu.ui.auth.register

import com.example.gxzhaiwu.data.model.RegisterRequest
import com.example.gxzhaiwu.data.model.User
import com.example.gxzhaiwu.data.repository.AuthRepository
import com.example.gxzhaiwu.utils.TestUtils
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class RegisterViewModelTest {

    private lateinit var authRepository: AuthRepository
    private lateinit var viewModel: RegisterViewModel
    private val testDispatcher = UnconfinedTestDispatcher()

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        authRepository = mockk()
        viewModel = RegisterViewModel(authRepository)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `updateName should update name and validate`() = runTest {
        // Given
        val name = "张三"

        // When
        viewModel.updateName(name)

        // Then
        val uiState = viewModel.uiState.first()
        assertEquals(name, uiState.name)
        assertNull(uiState.nameError)
    }

    @Test
    fun `updateName with empty string should show error`() = runTest {
        // Given
        val name = ""

        // When
        viewModel.updateName(name)

        // Then
        val uiState = viewModel.uiState.first()
        assertEquals(name, uiState.name)
        assertEquals("请输入姓名", uiState.nameError)
    }

    @Test
    fun `updateUsername should update username and validate`() = runTest {
        // Given
        val username = "zhangsan"

        // When
        viewModel.updateUsername(username)

        // Then
        val uiState = viewModel.uiState.first()
        assertEquals(username, uiState.username)
        assertNull(uiState.usernameError)
    }

    @Test
    fun `updateEmail should update email and validate`() = runTest {
        // Given
        val email = "<EMAIL>"

        // When
        viewModel.updateEmail(email)

        // Then
        val uiState = viewModel.uiState.first()
        assertEquals(email, uiState.email)
        assertNull(uiState.emailError)
    }

    @Test
    fun `updateEmail with invalid format should show error`() = runTest {
        // Given
        val email = "invalid-email"

        // When
        viewModel.updateEmail(email)

        // Then
        val uiState = viewModel.uiState.first()
        assertEquals(email, uiState.email)
        assertEquals("邮箱格式不正确", uiState.emailError)
    }

    @Test
    fun `updatePassword should update password and validate`() = runTest {
        // Given
        val password = "password123"

        // When
        viewModel.updatePassword(password)

        // Then
        val uiState = viewModel.uiState.first()
        assertEquals(password, uiState.password)
        assertNull(uiState.passwordError)
    }

    @Test
    fun `updatePasswordConfirmation should validate against password`() = runTest {
        // Given
        val password = "password123"
        val confirmation = "password123"

        // When
        viewModel.updatePassword(password)
        viewModel.updatePasswordConfirmation(confirmation)

        // Then
        val uiState = viewModel.uiState.first()
        assertEquals(confirmation, uiState.passwordConfirmation)
        assertNull(uiState.passwordConfirmationError)
    }

    @Test
    fun `updatePasswordConfirmation with mismatch should show error`() = runTest {
        // Given
        val password = "password123"
        val confirmation = "different"

        // When
        viewModel.updatePassword(password)
        viewModel.updatePasswordConfirmation(confirmation)

        // Then
        val uiState = viewModel.uiState.first()
        assertEquals(confirmation, uiState.passwordConfirmation)
        assertEquals("两次输入的密码不一致", uiState.passwordConfirmationError)
    }

    @Test
    fun `register with valid data should call repository and update state`() = runTest {
        // Given
        val mockUser = TestUtils.createMockUser()
        val mockToken = "mock-token"
        coEvery { authRepository.register(any()) } returns Result.success(Pair(mockToken, mockUser))

        // Setup valid form data
        viewModel.updateName("张三")
        viewModel.updateUsername("zhangsan")
        viewModel.updateEmail("<EMAIL>")
        viewModel.updatePassword("password123")
        viewModel.updatePasswordConfirmation("password123")

        // When
        viewModel.register()

        // Then
        coVerify {
            authRepository.register(
                RegisterRequest(
                    name = "张三",
                    username = "zhangsan",
                    email = "<EMAIL>",
                    password = "password123",
                    password_confirmation = "password123"
                )
            )
        }

        val uiState = viewModel.uiState.first()
        assertFalse(uiState.isLoading)
        assertTrue(uiState.isRegisterSuccessful)
        assertNull(uiState.errorMessage)
    }

    @Test
    fun `register with invalid data should not call repository`() = runTest {
        // Given - empty form data (invalid)

        // When
        viewModel.register()

        // Then
        coVerify(exactly = 0) { authRepository.register(any()) }

        val uiState = viewModel.uiState.first()
        assertFalse(uiState.isLoading)
        assertFalse(uiState.isRegisterSuccessful)
        assertNotNull(uiState.nameError)
        assertNotNull(uiState.usernameError)
        assertNotNull(uiState.emailError)
        assertNotNull(uiState.passwordError)
        assertNotNull(uiState.passwordConfirmationError)
    }

    @Test
    fun `register failure should update error state`() = runTest {
        // Given
        val errorMessage = "注册失败"
        coEvery { authRepository.register(any()) } returns Result.failure(Exception(errorMessage))

        // Setup valid form data
        viewModel.updateName("张三")
        viewModel.updateUsername("zhangsan")
        viewModel.updateEmail("<EMAIL>")
        viewModel.updatePassword("password123")
        viewModel.updatePasswordConfirmation("password123")

        // When
        viewModel.register()

        // Then
        val uiState = viewModel.uiState.first()
        assertFalse(uiState.isLoading)
        assertFalse(uiState.isRegisterSuccessful)
        assertEquals(errorMessage, uiState.errorMessage)
    }

    @Test
    fun `clearError should clear error message`() = runTest {
        // Given
        viewModel.updateName("") // This will set an error

        // When
        viewModel.clearError()

        // Then
        val uiState = viewModel.uiState.first()
        assertNull(uiState.errorMessage)
    }

    @Test
    fun `resetRegisterSuccess should reset success state`() = runTest {
        // Given
        val mockUser = TestUtils.createMockUser()
        val mockToken = "mock-token"
        coEvery { authRepository.register(any()) } returns Result.success(Pair(mockToken, mockUser))

        // Setup valid form and register
        viewModel.updateName("张三")
        viewModel.updateUsername("zhangsan")
        viewModel.updateEmail("<EMAIL>")
        viewModel.updatePassword("password123")
        viewModel.updatePasswordConfirmation("password123")
        viewModel.register()

        // When
        viewModel.resetRegisterSuccess()

        // Then
        val uiState = viewModel.uiState.first()
        assertFalse(uiState.isRegisterSuccessful)
    }
}
