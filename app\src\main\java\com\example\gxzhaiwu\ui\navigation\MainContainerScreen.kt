package com.example.gxzhaiwu.ui.navigation

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Construction
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.gxzhaiwu.ui.dashboard.DashboardScreen
import com.example.gxzhaiwu.ui.management.ManagementCenterScreen
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme

/**
 * 主容器屏幕
 * 管理底部导航和页面切换的核心组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainContainerScreen(
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
    viewModel: MainContainerViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    // 初始化用户角色
    LaunchedEffect(Unit) {
        viewModel.initializeUserRoles()
    }

    Scaffold(
        bottomBar = {
            if (uiState.userRoles.isNotEmpty()) {
                BottomNavigationContainer(
                    navController = navController,
                    userRoles = uiState.userRoles,
                    isVisible = uiState.isBottomNavVisible
                )
            }
        },
        modifier = modifier.fillMaxSize()
    ) { paddingValues ->
        MainNavHost(
            navController = navController,
            userRoles = uiState.userRoles,
            startDestination = BottomNavItem.Dashboard.route,
            modifier = Modifier.padding(paddingValues)
        )
    }
}

/**
 * 主导航宿主
 * 管理底部导航对应的页面路由
 */
@Composable
fun MainNavHost(
    navController: NavHostController,
    userRoles: List<String>,
    startDestination: String,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier
    ) {
        // 仪表盘页面
        composable(BottomNavItem.Dashboard.route) {
            DashboardScreen(
                onNavigateToScreen = { route ->
                    navController.navigate(route)
                }
            )
        }

        // 账单管理页面
        composable(BottomNavItem.Bills.route) {
            com.example.gxzhaiwu.ui.bills.BillManagementScreen(
                onNavigateToScreen = { route ->
                    navController.navigate(route)
                }
            )
        }

        // 还款管理页面（待实现）
        composable(BottomNavItem.Payments.route) {
            // TODO: 实现还款管理页面
            PlaceholderScreen(
                title = "还款管理",
                description = "还款管理功能正在开发中..."
            )
        }

        // 管理中心页面
        composable(BottomNavItem.Management.route) {
            ManagementCenterScreen(
                currentUserRoles = emptyList(), // TODO: 从用户状态获取实际角色
                onNavigateBack = { navController.popBackStack() },
                onNavigateToModule = { route ->
                    navController.navigate(route)
                }
            )
        }
    }
}

/**
 * 占位符屏幕
 * 用于显示待实现的功能页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PlaceholderScreen(
    title: String,
    description: String,
    modifier: Modifier = Modifier
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(title) },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    titleContentColor = MaterialTheme.colorScheme.onSurface
                )
            )
        },
        modifier = modifier.fillMaxSize()
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentAlignment = androidx.compose.ui.Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Construction,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(64.dp)
                )
                
                Text(
                    text = title,
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

// 预览组件
@Preview(name = "主容器屏幕")
@Composable
private fun MainContainerScreenPreview() {
    GxZhaiWuTheme {
        Surface {
            MainContainerScreen()
        }
    }
}

@Preview(name = "占位符屏幕")
@Composable
private fun PlaceholderScreenPreview() {
    GxZhaiWuTheme {
        Surface {
            PlaceholderScreen(
                title = "账单管理",
                description = "账单管理功能正在开发中..."
            )
        }
    }
}
