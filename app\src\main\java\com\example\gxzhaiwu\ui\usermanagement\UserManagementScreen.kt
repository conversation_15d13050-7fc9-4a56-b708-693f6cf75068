package com.example.gxzhaiwu.ui.usermanagement

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gxzhaiwu.data.model.UserManagementAction
import com.example.gxzhaiwu.data.model.UserOperationResult
import com.example.gxzhaiwu.ui.usermanagement.components.*
import com.example.gxzhaiwu.ui.theme.cardElevationColor
import com.example.gxzhaiwu.utils.RoleUtils

/**
 * 用户管理主页面
 * 仅限系统管理员访问
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserManagementScreen(
    currentUserRoles: List<String>,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: UserManagementViewModel = hiltViewModel()
) {
    // 权限检查
    if (!RoleUtils.hasPermission(currentUserRoles, com.example.gxzhaiwu.utils.Permission.MANAGE_USERS)) {
        AccessDeniedContent(onNavigateBack = onNavigateBack)
        return
    }

    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val operationResult by viewModel.operationResult.collectAsStateWithLifecycle(
        initialValue = UserOperationResult.Success
    )
    
    val keyboardController = LocalSoftwareKeyboardController.current
    
    // 处理操作结果
    LaunchedEffect(operationResult) {
        when (operationResult) {
            is UserOperationResult.Success -> {
                // 操作成功，可以显示成功提示
            }
            is UserOperationResult.Error -> {
                // 错误处理在UI中显示
            }
            UserOperationResult.Loading -> {
                // 加载状态处理
            }
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部栏
        TopAppBar(
            title = {
                Column {
                    Text(
                        text = "用户管理",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "系统用户管理和权限控制",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            actions = {
                // 刷新按钮
                IconButton(
                    onClick = { viewModel.refresh() }
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.cardElevationColor(0)
            )
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 搜索和筛选栏
        SearchAndFilterBar(
            searchQuery = uiState.searchQuery,
            selectedRole = uiState.selectedRole,
            availableRoles = uiState.roles,
            onSearchQueryChange = { query ->
                viewModel.searchUsers(query)
            },
            onRoleFilterChange = { role ->
                viewModel.filterByRole(role)
            },
            onKeyboardDone = {
                keyboardController?.hide()
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 错误提示
        uiState.error?.let { error ->
            ErrorCard(
                error = error,
                onDismiss = { viewModel.clearError() },
                onRetry = { viewModel.refresh() }
            )
            Spacer(modifier = Modifier.height(16.dp))
        }

        // 操作结果提示
        (operationResult as? UserOperationResult.Error)?.let { errorResult ->
            ErrorCard(
                error = errorResult.message,
                onDismiss = { /* 操作结果会自动清除 */ }
            )
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        // 用户列表
        UserListContent(
            users = uiState.users,
            currentPage = uiState.currentPage,
            totalPages = uiState.totalPages,
            totalUsers = uiState.totalUsers,
            isLoading = uiState.isLoading,
            onAction = { action, user ->
                when (action) {
                    UserManagementAction.VIEW_DETAIL -> {
                        viewModel.showUserDetail(user)
                    }
                    UserManagementAction.EDIT_ROLES -> {
                        viewModel.showRoleDialog(user)
                    }
                    UserManagementAction.EDIT_STORES -> {
                        viewModel.showStoreDialog(user)
                    }
                    UserManagementAction.TOGGLE_STATUS -> {
                        // 如果API支持状态切换，在这里实现
                    }
                }
            },
            onNextPage = { viewModel.loadNextPage() },
            onPreviousPage = { viewModel.loadPreviousPage() },
            modifier = Modifier.weight(1f)
        )
    }
    
    // 用户详情对话框
    uiState.selectedUser?.let { selectedUser ->
        if (uiState.showUserDetail) {
            UserDetailDialog(
                user = selectedUser,
                onDismiss = { viewModel.hideUserDetail() }
            )
        }
    }

    // 角色编辑对话框
    uiState.selectedUser?.let { selectedUser ->
        if (uiState.showRoleDialog) {
            RoleEditDialog(
                user = selectedUser,
                availableRoles = uiState.roles,
                onConfirm = { roleIds ->
                    viewModel.updateUserRoles(selectedUser.id, roleIds)
                },
                onDismiss = { viewModel.hideRoleDialog() },
                isLoading = operationResult is UserOperationResult.Loading
            )
        }
    }
    
    // 门店编辑对话框（如果需要实现）
    if (uiState.showStoreDialog && uiState.selectedUser != null) {
        // TODO: 实现门店编辑对话框
        // StoreEditDialog(...)
    }
}

/**
 * 搜索和筛选栏组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SearchAndFilterBar(
    searchQuery: String,
    selectedRole: String?,
    availableRoles: List<com.example.gxzhaiwu.data.model.Role>,
    onSearchQueryChange: (String) -> Unit,
    onRoleFilterChange: (String?) -> Unit,
    onKeyboardDone: () -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.cardElevationColor(1)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 搜索框
            OutlinedTextField(
                value = searchQuery,
                onValueChange = onSearchQueryChange,
                label = { Text("搜索用户") },
                placeholder = { Text("输入姓名、用户名或邮箱") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = null
                    )
                },
                trailingIcon = {
                    if (searchQuery.isNotEmpty()) {
                        IconButton(
                            onClick = { onSearchQueryChange("") }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "清除搜索"
                            )
                        }
                    }
                },
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = { onKeyboardDone() }
                ),
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 角色筛选
            ExposedDropdownMenuBox(
                expanded = expanded,
                onExpandedChange = { expanded = !expanded }
            ) {
                OutlinedTextField(
                    value = selectedRole?.let { roleSlug ->
                        availableRoles.find { it.slug == roleSlug }?.name
                    } ?: "全部角色",
                    onValueChange = { },
                    readOnly = true,
                    label = { Text("筛选角色") },
                    trailingIcon = {
                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor()
                )
                
                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    DropdownMenuItem(
                        text = { Text("全部角色") },
                        onClick = {
                            onRoleFilterChange(null)
                            expanded = false
                        }
                    )
                    
                    availableRoles.forEach { role ->
                        DropdownMenuItem(
                            text = { Text(role.name) },
                            onClick = {
                                onRoleFilterChange(role.slug)
                                expanded = false
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 错误卡片组件
 */
@Composable
private fun ErrorCard(
    error: String,
    onDismiss: () -> Unit,
    onRetry: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Error,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
                Text(
                    text = error,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
            
            Row {
                if (onRetry != null) {
                    TextButton(onClick = onRetry) {
                        Text(
                            text = "重试",
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
                IconButton(onClick = onDismiss) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}

/**
 * 访问拒绝内容组件
 */
@Composable
private fun AccessDeniedContent(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(32.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Block,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.size(64.dp)
            )
            Text(
                text = "访问被拒绝",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = "您没有权限访问用户管理功能\n仅限系统管理员使用",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Button(
                onClick = onNavigateBack
            ) {
                Text("返回")
            }
        }
    }
}
