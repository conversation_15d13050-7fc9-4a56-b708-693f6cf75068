package com.example.gxzhaiwu.ui.auth.login

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gxzhaiwu.ui.theme.GxZhaiWuTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class LoginScreenTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun loginScreen_displaysAllElements() {
        composeTestRule.setContent {
            GxZhaiWuTheme {
                LoginScreen(
                    onLoginSuccess = {},
                    onNavigateToRegister = {}
                )
            }
        }

        // 验证UI元素是否显示
        composeTestRule.onNodeWithText("债务管理系统").assertIsDisplayed()
        composeTestRule.onNodeWithText("请登录您的账户").assertIsDisplayed()
        composeTestRule.onNodeWithText("邮箱或用户名").assertIsDisplayed()
        composeTestRule.onNodeWithText("密码").assertIsDisplayed()
        composeTestRule.onNodeWithText("登录").assertIsDisplayed()
        composeTestRule.onNodeWithText("还没有账户？").assertIsDisplayed()
        composeTestRule.onNodeWithText("立即注册").assertIsDisplayed()
    }

    @Test
    fun loginScreen_inputValidation_showsErrors() {
        composeTestRule.setContent {
            GxZhaiWuTheme {
                LoginScreen(
                    onLoginSuccess = {},
                    onNavigateToRegister = {}
                )
            }
        }

        // 点击登录按钮而不输入任何内容
        composeTestRule.onNodeWithText("登录").performClick()

        // 验证错误消息是否显示
        composeTestRule.waitForIdle()
        // 注意：由于我们使用的是ViewModel，这里可能需要模拟数据
    }

    @Test
    fun loginScreen_inputText_updatesFields() {
        composeTestRule.setContent {
            GxZhaiWuTheme {
                LoginScreen(
                    onLoginSuccess = {},
                    onNavigateToRegister = {}
                )
            }
        }

        // 输入邮箱
        composeTestRule.onNodeWithText("邮箱或用户名")
            .performTextInput("<EMAIL>")

        // 输入密码
        composeTestRule.onNodeWithText("密码")
            .performTextInput("password123")

        // 验证输入是否正确
        composeTestRule.onNodeWithText("邮箱或用户名")
            .assertTextContains("<EMAIL>")
    }

    @Test
    fun loginScreen_registerButton_isClickable() {
        var registerClicked = false
        
        composeTestRule.setContent {
            GxZhaiWuTheme {
                LoginScreen(
                    onLoginSuccess = {},
                    onNavigateToRegister = { registerClicked = true }
                )
            }
        }

        // 点击注册按钮
        composeTestRule.onNodeWithText("立即注册").performClick()

        // 验证回调是否被调用
        assert(registerClicked)
    }
}
